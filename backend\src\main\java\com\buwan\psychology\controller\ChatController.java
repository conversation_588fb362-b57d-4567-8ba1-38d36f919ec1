package com.buwan.psychology.controller;

import com.buwan.psychology.entity.ChatMessage;
import com.buwan.psychology.entity.ConsultationSession;
import com.buwan.psychology.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 聊天控制器
 */
@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class ChatController {

    private final ChatService chatService;

    /**
     * 获取用户的会话列表
     */
    @GetMapping("/sessions")
    public ResponseEntity<?> getUserSessions(@RequestParam Long userId) {
        try {
            log.info("获取用户会话列表请求，用户ID: {}", userId);
            List<ConsultationSession> sessions = chatService.getUserSessions(userId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", sessions
            ));
        } catch (Exception e) {
            log.error("获取会话列表失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取会话列表失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 创建新会话
     */
    @PostMapping("/sessions")
    public ResponseEntity<?> createSession(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String title = request.get("title").toString();
            Integer sessionType = Integer.valueOf(request.get("sessionType").toString());

            // 获取可选的详细描述
            String description = null;
            if (request.containsKey("description") && request.get("description") != null) {
                description = request.get("description").toString();
            }

            // 获取可选的咨询师ID
            Long counselorId = null;
            if (request.containsKey("counselorId") && request.get("counselorId") != null) {
                counselorId = Long.valueOf(request.get("counselorId").toString());
            }

            // 获取智能匹配参数
            Boolean autoMatch = null;
            if (request.containsKey("autoMatch") && request.get("autoMatch") != null) {
                autoMatch = Boolean.valueOf(request.get("autoMatch").toString());
            }

            log.info("创建会话请求，用户ID: {}, 标题: {}, 描述长度: {}, 类型: {}, 咨询师ID: {}, 智能匹配: {}",
                userId, title, description != null ? description.length() : 0, sessionType, counselorId, autoMatch);

            ConsultationSession session = chatService.createSession(userId, title, description, sessionType, counselorId, autoMatch);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "会话创建成功",
                "data", session
            ));
        } catch (Exception e) {
            log.error("创建会话失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "创建会话失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取会话消息列表
     */
    @GetMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<?> getSessionMessages(@PathVariable Long sessionId) {
        try {
            log.info("获取会话消息请求，会话ID: {}", sessionId);
            List<ChatMessage> messages = chatService.getSessionMessages(sessionId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", messages
            ));
        } catch (Exception e) {
            log.error("获取会话消息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取会话消息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 清空会话消息
     */
    @DeleteMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<?> clearSessionMessages(@PathVariable Long sessionId, @RequestParam Long userId) {
        try {
            log.info("清空会话消息请求，会话ID: {}, 用户ID: {}", sessionId, userId);
            chatService.clearSessionMessages(sessionId, userId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "消息清空成功"
            ));
        } catch (Exception e) {
            log.error("清空会话消息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "清空会话消息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 发送消息（支持附件）
     */
    @PostMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<?> sendMessage(@PathVariable Long sessionId,
                                       @RequestBody Map<String, Object> request) {
        try {
            String content = request.get("content") != null ? request.get("content").toString() : "";

            // 处理附件
            List<ChatMessage.Attachment> attachments = null;
            if (request.containsKey("attachments")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> attachmentMaps = (List<Map<String, Object>>) request.get("attachments");
                attachments = attachmentMaps.stream()
                    .map(this::mapToAttachment)
                    .collect(Collectors.toList());
            }

            // 从JWT token中获取用户ID，这里暂时从请求中获取
            Long userId = request.containsKey("userId") ?
                Long.valueOf(request.get("userId").toString()) : 1L;

            log.info("发送消息请求，会话ID: {}, 用户ID: {}, 附件数量: {}",
                sessionId, userId, attachments != null ? attachments.size() : 0);

            ChatMessage message = chatService.sendMessageBlocking(sessionId, userId, content, attachments);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "消息发送成功",
                "data", message
            ));
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "发送消息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 流式发送消息
     */
    @PostMapping(value = "/sessions/{sessionId}/messages/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> sendMessageStream(@PathVariable Long sessionId,
                                                          @RequestBody Map<String, Object> request) {
        return Flux.create(sink -> {
            try {
                String content = request.get("content") != null ? request.get("content").toString() : "";

                // 处理附件
                List<ChatMessage.Attachment> attachments = null;
                if (request.containsKey("attachments")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> attachmentMaps = (List<Map<String, Object>>) request.get("attachments");
                    attachments = attachmentMaps.stream()
                        .map(this::mapToAttachment)
                        .collect(Collectors.toList());
                }

                // 从JWT token中获取用户ID
                Long userId = request.containsKey("userId") ?
                    Long.valueOf(request.get("userId").toString()) : 1L;

                log.info("流式发送消息请求，会话ID: {}, 用户ID: {}, 内容长度: {}",
                    sessionId, userId, content.length());

                // 先发送开始事件
                sink.next(ServerSentEvent.<String>builder()
                    .event("start")
                    .data("开始生成回复...")
                    .build());

                // 保存用户消息
                try {
                    ChatMessage userMessage = new ChatMessage();
                    userMessage.setSessionId(sessionId);
                    userMessage.setUserId(userId);
                    userMessage.setContent(content);
                    userMessage.setMessageType(0);
                    userMessage.setAttachments(attachments);

                    // 立即保存用户消息到数据库
                    ChatMessage savedUserMessage = chatService.saveUserMessage(userMessage);
                    log.info("用户消息已保存: ID={}, 内容={}", savedUserMessage.getId(),
                        content.substring(0, Math.min(content.length(), 50)));
                } catch (Exception e) {
                    log.error("保存用户消息失败", e);
                }

                // 生成流式响应
                chatService.generateStreamResponse(sessionId, userId, content, attachments)
                    .doOnNext(chunk -> {
                        if (chunk != null && !chunk.trim().isEmpty()) {
                            sink.next(ServerSentEvent.<String>builder()
                                .event("message")
                                .data(chunk)
                                .build());
                        }
                    })
                    .doOnComplete(() -> {
                        sink.next(ServerSentEvent.<String>builder()
                            .event("done")
                            .data("[DONE]")
                            .build());
                        sink.complete();
                        log.info("流式响应完成，会话ID: {}", sessionId);
                    })
                    .doOnError(error -> {
                        log.error("流式响应生成失败，会话ID: {}", sessionId, error);
                        sink.next(ServerSentEvent.<String>builder()
                            .event("error")
                            .data("生成回复失败: " + error.getMessage())
                            .build());
                        sink.complete();
                    })
                    .subscribe();

            } catch (Exception e) {
                log.error("流式发送消息失败", e);
                sink.next(ServerSentEvent.<String>builder()
                    .event("error")
                    .data("发送消息失败: " + e.getMessage())
                    .build());
                sink.complete();
            }
        });
    }

    /**
     * 保存完整的AI回复
     */
    @PostMapping("/sessions/{sessionId}/messages/complete")
    public ResponseEntity<?> saveCompleteResponse(@PathVariable Long sessionId,
                                                 @RequestBody Map<String, Object> request) {
        try {
            String completeResponse = request.get("content").toString();
            Long userId = request.containsKey("userId") ?
                Long.valueOf(request.get("userId").toString()) : 1L;

            ChatMessage aiMessage = chatService.saveCompleteAiResponse(sessionId, userId, completeResponse).block();

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "AI回复保存成功",
                "data", aiMessage
            ));
        } catch (Exception e) {
            log.error("保存AI回复失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "保存AI回复失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 将Map转换为Attachment对象
     */
    private ChatMessage.Attachment mapToAttachment(Map<String, Object> map) {
        ChatMessage.Attachment attachment = new ChatMessage.Attachment();
        attachment.setId((String) map.get("id"));
        attachment.setName((String) map.get("name"));
        attachment.setType((String) map.get("type"));
        attachment.setBase64((String) map.get("base64"));
        attachment.setSize(map.get("size") != null ? ((Number) map.get("size")).longValue() : null);
        attachment.setPreview((String) map.get("preview"));
        return attachment;
    }

    /**
     * 结束会话
     */
    @PostMapping("/sessions/{sessionId}/end")
    public ResponseEntity<?> endSession(@PathVariable Long sessionId,
                                      @RequestBody(required = false) Map<String, Object> request) {
        try {
            // 从JWT token中获取用户ID，这里暂时从请求中获取
            Long userId = (request != null && request.containsKey("userId")) ? 
                Long.valueOf(request.get("userId").toString()) : 1L;
            
            log.info("结束会话请求，会话ID: {}, 用户ID: {}", sessionId, userId);
            
            chatService.endSession(sessionId, userId);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "会话已结束"
            ));
        } catch (Exception e) {
            log.error("结束会话失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "结束会话失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取会话详情
     */
    @GetMapping("/sessions/{sessionId}")
    public ResponseEntity<?> getSession(@PathVariable Long sessionId) {
        try {
            log.info("获取会话详情请求，会话ID: {}", sessionId);
            return chatService.getSession(sessionId)
                .map(session -> ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", session
                )))
                .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("获取会话详情失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取会话详情失败: " + e.getMessage()
            ));
        }
    }
}

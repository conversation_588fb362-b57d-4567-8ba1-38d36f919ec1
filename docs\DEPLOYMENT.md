# 不晚心理平台部署指南

## 环境要求

### 后端环境
- Java 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 前端环境
- Node.js 18+
- npm 或 yarn

## 本地开发环境搭建

### 1. 数据库准备

#### MySQL 数据库
```sql
-- 创建数据库
CREATE DATABASE buwan_psychology CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'buwan'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON buwan_psychology.* TO 'buwan'@'localhost';
FLUSH PRIVILEGES;
```

#### Redis 配置
确保Redis服务运行在默认端口6379。

### 2. 后端启动

```bash
# 进入后端目录
cd backend

# 安装依赖
mvn clean install

# 启动应用（开发模式）
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者打包后运行
mvn clean package
java -jar target/psychology-platform-1.0.0.jar --spring.profiles.active=dev
```

后端服务将在 http://localhost:8080 启动。

### 3. 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 http://localhost:3000 启动。

## 生产环境部署

### 1. 服务器准备

推荐配置：
- CPU: 2核心+
- 内存: 4GB+
- 存储: 50GB+
- 操作系统: Ubuntu 20.04+ / CentOS 7+

### 2. 环境安装

#### 安装 Java
```bash
# Ubuntu
sudo apt update
sudo apt install openjdk-17-jdk

# CentOS
sudo yum install java-17-openjdk-devel
```

#### 安装 MySQL
```bash
# Ubuntu
sudo apt install mysql-server

# CentOS
sudo yum install mysql-server
```

#### 安装 Redis
```bash
# Ubuntu
sudo apt install redis-server

# CentOS
sudo yum install redis
```

#### 安装 Nginx
```bash
# Ubuntu
sudo apt install nginx

# CentOS
sudo yum install nginx
```

### 3. 后端部署

#### 配置文件
创建生产环境配置文件 `application-prod.yml`：

```yaml
spring:
  datasource:
    url: *************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      password: ${REDIS_PASSWORD:}

app:
  ai:
    api-key: ${AI_API_KEY}
    base-url: ${AI_BASE_URL}

logging:
  file:
    name: /var/log/buwan-psychology/application.log
```

#### 部署脚本
```bash
#!/bin/bash
# deploy-backend.sh

# 设置环境变量
export DB_USERNAME="your_db_username"
export DB_PASSWORD="your_db_password"
export AI_API_KEY="your_ai_api_key"

# 停止旧服务
sudo systemctl stop buwan-psychology

# 备份旧版本
sudo cp /opt/buwan-psychology/app.jar /opt/buwan-psychology/app.jar.backup

# 部署新版本
sudo cp target/psychology-platform-1.0.0.jar /opt/buwan-psychology/app.jar

# 启动服务
sudo systemctl start buwan-psychology
sudo systemctl enable buwan-psychology
```

#### 系统服务配置
创建 `/etc/systemd/system/buwan-psychology.service`：

```ini
[Unit]
Description=Buwan Psychology Platform
After=network.target

[Service]
Type=simple
User=buwan
WorkingDirectory=/opt/buwan-psychology
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod app.jar
Restart=always
RestartSec=10

Environment=DB_USERNAME=your_db_username
Environment=DB_PASSWORD=your_db_password
Environment=AI_API_KEY=your_ai_api_key

[Install]
WantedBy=multi-user.target
```

### 4. 前端部署

#### 构建前端
```bash
cd frontend
npm install
npm run build
```

#### Nginx 配置
创建 `/etc/nginx/sites-available/buwan-psychology`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/buwan-psychology;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/buwan-psychology /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. SSL 证书配置

使用 Let's Encrypt：
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 6. 监控和日志

#### 日志配置
```bash
# 创建日志目录
sudo mkdir -p /var/log/buwan-psychology
sudo chown buwan:buwan /var/log/buwan-psychology

# 配置日志轮转
sudo tee /etc/logrotate.d/buwan-psychology << EOF
/var/log/buwan-psychology/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 buwan buwan
}
EOF
```

#### 监控脚本
```bash
#!/bin/bash
# monitor.sh

# 检查后端服务
if ! systemctl is-active --quiet buwan-psychology; then
    echo "Backend service is down, restarting..."
    sudo systemctl restart buwan-psychology
fi

# 检查数据库连接
if ! mysqladmin ping -h localhost --silent; then
    echo "Database connection failed"
    # 发送告警
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is high: ${DISK_USAGE}%"
    # 发送告警
fi
```

## Docker 部署（可选）

### Dockerfile 示例

#### 后端 Dockerfile
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY target/psychology-platform-1.0.0.jar app.jar

EXPOSE 8080
CMD ["java", "-jar", "app.jar"]
```

#### 前端 Dockerfile
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

#### Docker Compose
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: buwan_psychology
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - mysql
      - redis
    ports:
      - "8080:8080"

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

## 安全建议

1. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期备份数据

2. **应用安全**
   - 定期更新依赖
   - 配置防火墙
   - 使用HTTPS

3. **服务器安全**
   - 定期更新系统
   - 配置SSH密钥认证
   - 监控异常访问

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 检查防火墙设置

2. **AI API 调用失败**
   - 验证API密钥
   - 检查网络连接
   - 查看API配额

3. **前端无法访问后端**
   - 检查代理配置
   - 验证CORS设置
   - 查看网络策略

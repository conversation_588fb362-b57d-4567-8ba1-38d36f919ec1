# 心理咨询页面功能更新说明

## 更新概述

本次更新对心理咨询页面进行了重要的功能增强，主要包括：

1. **会话创建增强** - 支持选择咨询师方式（智能匹配或手动选择）
2. **会话列表优化** - 显示咨询师头像、姓名、创建时间等详细信息
3. **欢迎语功能** - 咨询师可配置个性化欢迎语，在新会话中自动显示

## 详细功能说明

### 1. 会话创建流程优化

#### 前端改进
- **NewSessionModal组件**：新增咨询师选择方式选项
  - 智能匹配：系统根据咨询类型自动推荐最合适的咨询师
  - 手动选择：用户从专家团队中选择偏好的咨询师
- **表单字段**：
  - 标题：会话主题描述
  - 类型：咨询问题分类（焦虑、抑郁、学习压力等）
  - 咨询师选择：智能匹配或手动选择

#### 后端改进
- **ChatService**：新增智能匹配逻辑
  - 根据会话类型匹配专业领域相符的咨询师
  - 考虑咨询师评分、负载等因素
  - 支持降级匹配策略
- **ChatController**：支持autoMatch参数

### 2. 会话列表显示增强

#### 显示内容
- **咨询师头像**：显示分配的咨询师头像，未分配时显示默认图标
- **会话标题**：用户创建时输入的会话主题
- **咨询师信息**：显示咨询师姓名和职称
- **会话类型标签**：不同颜色标识咨询类型
- **状态标签**：会话进行状态
- **创建时间**：会话创建的具体时间

#### 技术实现
- **SessionList组件**：更新界面布局和数据显示
- **数据获取**：后端查询时关联咨询师信息
- **类型定义**：前端增加Counselor接口定义

### 3. 咨询师欢迎语功能

#### 数据库改进
- **Counselor表**：新增welcome_message字段
- **数据迁移**：为现有咨询师生成默认欢迎语
- **字段类型**：TEXT类型，支持长文本内容

#### 显示逻辑
- **ChatWindow组件**：
  - 无消息时：显示咨询师欢迎语作为首条消息
  - 有消息时：在消息列表顶部显示欢迎语
  - 头像显示：使用咨询师头像，未设置时使用默认图标

## 技术架构更新

### 后端更新

#### 实体类
```java
// Counselor.java
@Column(columnDefinition = "TEXT")
private String welcomeMessage;
```

#### 服务层
```java
// ChatService.java
public ConsultationSession createSession(Long userId, String title, 
    Integer sessionType, Long counselorId, Boolean autoMatch)
```

#### 数据访问层
```java
// ConsultationSessionRepository.java
@Query("SELECT s FROM ConsultationSession s LEFT JOIN FETCH s.counselor 
    WHERE s.userId = :userId ORDER BY s.createTime DESC")
List<ConsultationSession> findByUserIdWithCounselorOrderByCreateTimeDesc(@Param("userId") Long userId);
```

### 前端更新

#### 类型定义
```typescript
// chatSlice.ts
export interface Counselor {
  id: number
  name: string
  title?: string
  avatar?: string
  welcomeMessage?: string
}

export interface ChatSession {
  // ... 其他字段
  counselorId?: number
  counselor?: Counselor
}
```

#### 组件更新
- **NewSessionModal**：支持咨询师选择方式
- **SessionList**：显示咨询师信息
- **ChatWindow**：显示欢迎语

## 数据库迁移

### 迁移脚本
文件：`backend/src/main/resources/db/migration/V4__add_welcome_message_to_counselors.sql`

```sql
-- 添加欢迎语字段
ALTER TABLE counselors ADD COLUMN welcome_message TEXT COMMENT '咨询师欢迎语';

-- 为现有咨询师生成默认欢迎语
UPDATE counselors SET welcome_message = CONCAT(
    '您好！我是', name, '，',
    CASE 
        WHEN title IS NOT NULL THEN CONCAT('一位', title, '。')
        ELSE '一位专业的心理咨询师。'
    END,
    '很高兴为您提供心理咨询服务。在这里，您可以放心地分享您的想法和感受，我会以专业、保密的态度倾听并为您提供支持。请告诉我，今天有什么想要聊的吗？'
) WHERE welcome_message IS NULL;
```

## 使用说明

### 创建新会话
1. 点击"新建会话"按钮
2. 输入会话标题（简要描述咨询问题）
3. 选择咨询类型
4. 选择咨询师方式：
   - **智能匹配**：系统自动推荐（推荐）
   - **手动选择**：从列表中选择特定咨询师
5. 点击"创建"完成会话创建

### 查看会话信息
- 会话列表显示咨询师头像、姓名、职称
- 显示会话创建时间
- 不同颜色标签标识咨询类型和状态

### 开始咨询
- 进入会话后自动显示咨询师欢迎语
- 欢迎语包含咨询师个人介绍和引导语
- 用户可直接开始输入问题进行咨询

## 后续优化建议

1. **咨询师管理界面**：添加欢迎语编辑功能
2. **智能匹配算法**：优化匹配策略，考虑更多因素
3. **会话统计**：添加咨询师工作量统计
4. **用户偏好**：记录用户对咨询师的偏好设置

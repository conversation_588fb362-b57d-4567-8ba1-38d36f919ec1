package com.buwan.psychology.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户详细资料实体类
 */
@Entity
@Table(name = "user_profiles")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class UserProfile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的用户ID
     */
    @Column(nullable = false, unique = true)
    private Long userId;

    /**
     * 真实姓名
     */
    @Column(length = 50)
    private String realName;

    /**
     * 出生日期
     */
    private LocalDate birthDate;

    /**
     * 婚姻状况：single-未婚，married-已婚，divorced-离异，widowed-丧偶
     */
    @Column(length = 20)
    private String maritalStatus;

    /**
     * 教育程度：primary-小学，middle-初中，high-高中，college-大专，bachelor-本科，master-硕士，doctor-博士
     */
    @Column(length = 20)
    private String education;

    /**
     * 职业
     */
    @Column(length = 100)
    private String occupation;

    /**
     * 月收入范围
     */
    @Column(length = 50)
    private String income;

    /**
     * 居住地址 - 省份
     */
    @Column(length = 50)
    private String province;

    /**
     * 居住地址 - 城市
     */
    @Column(length = 50)
    private String city;

    /**
     * 居住地址 - 区县
     */
    @Column(length = 50)
    private String district;

    /**
     * 紧急联系人姓名
     */
    @Column(length = 50)
    private String emergencyContactName;

    /**
     * 紧急联系人关系
     */
    @Column(length = 20)
    private String emergencyContactRelationship;

    /**
     * 紧急联系人电话
     */
    @Column(length = 20)
    private String emergencyContactPhone;

    /**
     * 既往病史（JSON格式存储数组）
     */
    @Column(columnDefinition = "TEXT")
    private String medicalHistory;

    /**
     * 心理健康史（JSON格式存储数组）
     */
    @Column(columnDefinition = "TEXT")
    private String psychologicalHistory;

    /**
     * 当前用药情况
     */
    @Column(columnDefinition = "TEXT")
    private String currentMedications;

    /**
     * 过敏史
     */
    @Column(columnDefinition = "TEXT")
    private String allergies;

    /**
     * 是否吸烟
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean smoking;

    /**
     * 是否饮酒
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean drinking;

    /**
     * 运动频率
     */
    @Column(length = 50)
    private String exerciseFrequency;

    /**
     * 睡眠时长
     */
    @Column(length = 50)
    private String sleepDuration;

    /**
     * 性格特征（JSON格式存储数组）
     */
    @Column(columnDefinition = "TEXT")
    private String personalityTraits;

    /**
     * 兴趣爱好（JSON格式存储数组）
     */
    @Column(columnDefinition = "TEXT")
    private String interests;

    /**
     * 压力来源（JSON格式存储数组）
     */
    @Column(columnDefinition = "TEXT")
    private String stressFactors;

    /**
     * 应对方式（JSON格式存储数组）
     */
    @Column(columnDefinition = "TEXT")
    private String copingMethods;

    /**
     * 咨询目标
     */
    @Column(columnDefinition = "TEXT")
    private String goals;

    /**
     * 补充信息
     */
    @Column(columnDefinition = "TEXT")
    private String additionalInfo;

    /**
     * 隐私设置 - 是否显示真实姓名
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean showRealName;

    /**
     * 隐私设置 - 是否显示年龄
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean showAge;

    /**
     * 隐私设置 - 是否显示位置信息
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean showLocation;

    /**
     * 隐私设置 - 是否允许数据共享用于研究
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean allowDataSharing;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private LocalDateTime updateTime;

    /**
     * 关联用户实体
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", insertable = false, updatable = false)
    private User user;

    /**
     * 计算年龄
     */
    public Integer getAge() {
        if (birthDate == null) {
            return null;
        }
        return LocalDate.now().getYear() - birthDate.getYear();
    }

    /**
     * 获取完整地址
     */
    public String getFullAddress() {
        StringBuilder address = new StringBuilder();
        if (province != null) address.append(province);
        if (city != null) address.append(city);
        if (district != null) address.append(district);
        return address.toString();
    }
}

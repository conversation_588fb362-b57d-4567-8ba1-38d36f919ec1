package com.buwan.psychology.service;

import com.buwan.psychology.entity.Counselor;
import com.buwan.psychology.repository.CounselorRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Random;

/**
 * 心理咨询师服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CounselorService {

    private final CounselorRepository counselorRepository;

    /**
     * 获取所有可用咨询师
     */
    public List<Counselor> getAllAvailableCounselors() {
        return counselorRepository.findByIsAvailableTrueOrderByRatingDesc();
    }

    /**
     * 根据ID获取咨询师
     */
    public Optional<Counselor> getCounselorById(Long id) {
        return counselorRepository.findById(id);
    }

    /**
     * 根据专业领域获取咨询师
     */
    public List<Counselor> getCounselorsBySpecialization(String specialization) {
        return counselorRepository.findBySpecializationAndAvailable(specialization);
    }

    /**
     * 获取推荐咨询师
     */
    public List<Counselor> getRecommendedCounselors() {
        return counselorRepository.findRecommendedCounselors();
    }

    /**
     * 智能匹配咨询师
     * 根据咨询类型匹配最合适的咨询师
     */
    public Counselor matchCounselorBySessionType(Integer sessionType) {
        String specialization = getSpecializationBySessionType(sessionType);
        
        // 首先尝试找到专业匹配的咨询师
        List<Counselor> matchedCounselors = counselorRepository.findBestMatchCounselors(specialization, 3.5);
        
        if (!matchedCounselors.isEmpty()) {
            // 如果有专业匹配的咨询师，选择评分最高且负载最轻的
            return matchedCounselors.get(0);
        }
        
        // 如果没有专业匹配的，选择综合评分高的咨询师
        List<Counselor> generalCounselors = counselorRepository.findBestMatchCounselors("综合咨询", 3.0);
        if (!generalCounselors.isEmpty()) {
            return generalCounselors.get(0);
        }
        
        // 最后备选：任意可用咨询师
        List<Counselor> availableCounselors = counselorRepository.findByIsAvailableTrueOrderByRatingDesc();
        if (!availableCounselors.isEmpty()) {
            return availableCounselors.get(0);
        }
        
        return null;
    }

    /**
     * 根据会话类型获取对应的专业领域
     */
    private String getSpecializationBySessionType(Integer sessionType) {
        return switch (sessionType) {
            case 1 -> "焦虑障碍";
            case 2 -> "抑郁障碍";
            case 3 -> "青少年心理";
            case 4 -> "职场心理";
            default -> "综合咨询";
        };
    }

    /**
     * 更新咨询师的咨询次数
     */
    public void incrementConsultationCount(Long counselorId) {
        Optional<Counselor> counselorOpt = counselorRepository.findById(counselorId);
        if (counselorOpt.isPresent()) {
            Counselor counselor = counselorOpt.get();
            counselor.setConsultationCount(counselor.getConsultationCount() + 1);
            counselorRepository.save(counselor);
            log.info("咨询师 {} 的咨询次数已更新为 {}", counselor.getName(), counselor.getConsultationCount());
        }
    }

    /**
     * 更新咨询师评分
     */
    public void updateCounselorRating(Long counselorId, Double newRating) {
        Optional<Counselor> counselorOpt = counselorRepository.findById(counselorId);
        if (counselorOpt.isPresent()) {
            Counselor counselor = counselorOpt.get();
            // 这里可以实现更复杂的评分计算逻辑，比如加权平均
            counselor.setRating(newRating);
            counselorRepository.save(counselor);
            log.info("咨询师 {} 的评分已更新为 {}", counselor.getName(), newRating);
        }
    }

    /**
     * 设置咨询师可用状态
     */
    public void setCounselorAvailability(Long counselorId, Boolean isAvailable) {
        Optional<Counselor> counselorOpt = counselorRepository.findById(counselorId);
        if (counselorOpt.isPresent()) {
            Counselor counselor = counselorOpt.get();
            counselor.setIsAvailable(isAvailable);
            counselorRepository.save(counselor);
            log.info("咨询师 {} 的可用状态已设置为 {}", counselor.getName(), isAvailable);
        }
    }

    /**
     * 获取咨询师统计信息
     */
    public CounselorStats getCounselorStats(Long counselorId) {
        Optional<Counselor> counselorOpt = counselorRepository.findById(counselorId);
        if (counselorOpt.isPresent()) {
            Counselor counselor = counselorOpt.get();
            return new CounselorStats(
                counselor.getId(),
                counselor.getName(),
                counselor.getConsultationCount(),
                counselor.getRating(),
                counselor.getIsAvailable()
            );
        }
        return null;
    }

    /**
     * 咨询师统计信息内部类
     */
    public static class CounselorStats {
        public final Long id;
        public final String name;
        public final Integer consultationCount;
        public final Double rating;
        public final Boolean isAvailable;

        public CounselorStats(Long id, String name, Integer consultationCount, Double rating, Boolean isAvailable) {
            this.id = id;
            this.name = name;
            this.consultationCount = consultationCount;
            this.rating = rating;
            this.isAvailable = isAvailable;
        }
    }
}

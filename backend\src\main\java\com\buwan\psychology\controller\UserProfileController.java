package com.buwan.psychology.controller;

import com.buwan.psychology.entity.UserProfile;
import com.buwan.psychology.service.UserProfileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 用户详细资料控制器
 */
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class UserProfileController {

    private final UserProfileService userProfileService;

    /**
     * 获取当前认证用户的ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.debug("获取当前用户ID，认证对象: {}", authentication);

        if (authentication != null) {
            Object principal = authentication.getPrincipal();
            log.debug("Principal类型: {}, 值: {}", principal.getClass().getName(), principal);

            if (principal instanceof Long) {
                return (Long) principal;
            }
        }

        log.error("用户未认证或认证信息格式错误，认证对象: {}", authentication);
        throw new RuntimeException("用户未认证");
    }

    /**
     * 获取用户详细资料
     */
    @GetMapping("/profile")
    public ResponseEntity<?> getUserProfile() {
        try {
            log.info("开始获取用户详细资料");
            Long userId = getCurrentUserId();
            log.info("获取到用户ID: {}", userId);

            // 获取合并后的用户信息
            Map<String, Object> mergedProfile = userProfileService.getMergedUserProfile(userId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "获取用户资料成功",
                "data", mergedProfile
            ));
        } catch (Exception e) {
            log.error("获取用户详细资料失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取用户资料失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 保存或更新用户详细资料
     */
    @PostMapping("/profile")
    public ResponseEntity<?> saveUserProfile(@RequestBody Map<String, Object> request) {
        try {
            Long userId = getCurrentUserId();

            // 构建UserProfile对象
            UserProfile profile = buildUserProfileFromRequest(request);

            // 同时更新User表中的字段
            updateUserFields(userId, request);

            UserProfile savedProfile = userProfileService.saveOrUpdateUserProfile(userId, profile);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "用户资料保存成功",
                "data", savedProfile
            ));
        } catch (Exception e) {
            log.error("保存用户详细资料失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "保存用户资料失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取用户资料完成度统计
     */
    @GetMapping("/profile/stats")
    public ResponseEntity<?> getUserProfileStats(@RequestParam Long userId) {
        try {
            Map<String, Object> stats = userProfileService.getUserProfileStats(userId);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "获取统计信息成功",
                "data", stats
            ));
        } catch (Exception e) {
            log.error("获取用户资料统计失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取统计信息失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 删除用户详细资料
     */
    @DeleteMapping("/profile")
    public ResponseEntity<?> deleteUserProfile(@RequestParam Long userId) {
        try {
            userProfileService.deleteUserProfile(userId);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "用户资料删除成功"
            ));
        } catch (Exception e) {
            log.error("删除用户详细资料失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "删除用户资料失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 上传用户头像
     */
    @PostMapping("/avatar")
    public ResponseEntity<?> uploadAvatar(@RequestParam("avatar") MultipartFile file,
                                        @RequestParam Long userId) {
        try {
            // 这里应该实现文件上传逻辑
            // 可以上传到本地存储、云存储等
            
            // 模拟返回头像URL
            String avatarUrl = "/uploads/avatars/" + userId + "_" + System.currentTimeMillis() + ".jpg";
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "头像上传成功",
                "data", Map.of("url", avatarUrl)
            ));
        } catch (Exception e) {
            log.error("上传头像失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "头像上传失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 从请求构建UserProfile对象
     */
    private UserProfile buildUserProfileFromRequest(Map<String, Object> request) {
        UserProfile profile = new UserProfile();
        
        // 基本信息
        if (request.containsKey("realName")) {
            profile.setRealName((String) request.get("realName"));
        }
        if (request.containsKey("birthDate")) {
            // 这里需要处理日期格式转换
            // profile.setBirthDate(LocalDate.parse((String) request.get("birthDate")));
        }
        if (request.containsKey("maritalStatus")) {
            profile.setMaritalStatus((String) request.get("maritalStatus"));
        }
        if (request.containsKey("education")) {
            profile.setEducation((String) request.get("education"));
        }
        if (request.containsKey("occupation")) {
            profile.setOccupation((String) request.get("occupation"));
        }
        if (request.containsKey("income")) {
            profile.setIncome((String) request.get("income"));
        }
        
        // 地址信息
        Map<String, Object> location = (Map<String, Object>) request.get("location");
        if (location != null) {
            if (location.containsKey("province")) {
                profile.setProvince((String) location.get("province"));
            }
            if (location.containsKey("city")) {
                profile.setCity((String) location.get("city"));
            }
            if (location.containsKey("district")) {
                profile.setDistrict((String) location.get("district"));
            }
        }
        
        // 紧急联系人
        Map<String, Object> emergencyContact = (Map<String, Object>) request.get("emergencyContact");
        if (emergencyContact != null) {
            if (emergencyContact.containsKey("name")) {
                profile.setEmergencyContactName((String) emergencyContact.get("name"));
            }
            if (emergencyContact.containsKey("relationship")) {
                profile.setEmergencyContactRelationship((String) emergencyContact.get("relationship"));
            }
            if (emergencyContact.containsKey("phone")) {
                profile.setEmergencyContactPhone((String) emergencyContact.get("phone"));
            }
        }
        
        // 健康信息
        if (request.containsKey("medicalHistory")) {
            profile.setMedicalHistory(userProfileService.listToJson((java.util.List<String>) request.get("medicalHistory")));
        }
        if (request.containsKey("psychologicalHistory")) {
            profile.setPsychologicalHistory(userProfileService.listToJson((java.util.List<String>) request.get("psychologicalHistory")));
        }
        if (request.containsKey("currentMedications")) {
            profile.setCurrentMedications((String) request.get("currentMedications"));
        }
        if (request.containsKey("allergies")) {
            profile.setAllergies((String) request.get("allergies"));
        }
        
        // 生活方式
        Map<String, Object> lifestyle = (Map<String, Object>) request.get("lifestyle");
        if (lifestyle != null) {
            if (lifestyle.containsKey("smoking")) {
                profile.setSmoking((Boolean) lifestyle.get("smoking"));
            }
            if (lifestyle.containsKey("drinking")) {
                profile.setDrinking((Boolean) lifestyle.get("drinking"));
            }
            if (lifestyle.containsKey("exercise")) {
                profile.setExerciseFrequency((String) lifestyle.get("exercise"));
            }
            if (lifestyle.containsKey("sleep")) {
                profile.setSleepDuration((String) lifestyle.get("sleep"));
            }
        }
        
        // 心理特征
        if (request.containsKey("personalityTraits")) {
            profile.setPersonalityTraits(userProfileService.listToJson((java.util.List<String>) request.get("personalityTraits")));
        }
        if (request.containsKey("interests")) {
            profile.setInterests(userProfileService.listToJson((java.util.List<String>) request.get("interests")));
        }
        if (request.containsKey("stressFactors")) {
            profile.setStressFactors(userProfileService.listToJson((java.util.List<String>) request.get("stressFactors")));
        }
        if (request.containsKey("copingMethods")) {
            profile.setCopingMethods(userProfileService.listToJson((java.util.List<String>) request.get("copingMethods")));
        }
        
        // 咨询目标
        if (request.containsKey("goals")) {
            profile.setGoals((String) request.get("goals"));
        }
        if (request.containsKey("additionalInfo")) {
            profile.setAdditionalInfo((String) request.get("additionalInfo"));
        }
        
        // 隐私设置
        Map<String, Object> privacySettings = (Map<String, Object>) request.get("privacySettings");
        if (privacySettings != null) {
            if (privacySettings.containsKey("showRealName")) {
                profile.setShowRealName((Boolean) privacySettings.get("showRealName"));
            }
            if (privacySettings.containsKey("showAge")) {
                profile.setShowAge((Boolean) privacySettings.get("showAge"));
            }
            if (privacySettings.containsKey("showLocation")) {
                profile.setShowLocation((Boolean) privacySettings.get("showLocation"));
            }
            if (privacySettings.containsKey("allowDataSharing")) {
                profile.setAllowDataSharing((Boolean) privacySettings.get("allowDataSharing"));
            }
        }
        
        return profile;
    }
}

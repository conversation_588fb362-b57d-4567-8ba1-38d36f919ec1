# 不晚心理 - Gemini AI聊天功能部署指南

## 功能特性

### ✨ 已实现的功能
1. **Google Gemini AI集成** - 使用Gemini 2.5 Flash模型
2. **默认流式响应** - 所有AI回复都使用实时流式显示
3. **Markdown渲染** - AI回复支持完整Markdown格式
4. **多媒体支持** - 支持图片、文件上传和base64编码
5. **剪贴板粘贴** - 支持Ctrl+V粘贴截图
6. **表情包支持** - 丰富的表情选择器
7. **拖拽上传** - 支持拖拽文件上传
8. **详细日志** - 完整的聊天记录和处理日志

### 🎯 技术栈
- **后端**: Spring Boot + WebFlux + JPA + MySQL
- **前端**: React + TypeScript + Ant Design + Vite
- **AI模型**: Google Gemini 2.5 Flash
- **实时通信**: Server-Sent Events (SSE)
- **Markdown**: react-markdown + 语法高亮

## 部署步骤

### 1. 环境准备

#### 1.1 获取Gemini API Key
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 创建新项目或选择现有项目
3. 生成API密钥
4. 记录API密钥备用

#### 1.2 系统要求
- Java 17+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+ (可选)

### 2. 后端部署

#### 2.1 配置环境变量
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑环境变量
vim backend/.env
```

设置以下关键变量：
```bash
GEMINI_API_KEY=your-actual-gemini-api-key
DB_PASSWORD=your-database-password
```

#### 2.2 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE buwan_psychology CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'buwan'@'localhost' IDENTIFIED BY 'your-password';
GRANT ALL PRIVILEGES ON buwan_psychology.* TO 'buwan'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.3 启动后端服务
```bash
cd backend
./mvnw clean install
./mvnw spring-boot:run
```

服务将在 http://localhost:8080 启动

### 3. 前端部署

#### 3.1 安装依赖
```bash
cd frontend
npm install
```

#### 3.2 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置
vim .env
```

#### 3.3 启动前端服务
```bash
npm run dev
```

前端将在 http://localhost:3000 启动

### 4. 功能测试

#### 4.1 基础聊天测试
1. 访问 http://localhost:3000
2. 注册/登录账户
3. 创建新的咨询会话
4. 发送文本消息测试AI回复

#### 4.2 流式响应测试
1. 发送任意问题（系统默认使用流式响应）
2. 观察AI回复的实时流式显示效果
3. 注意打字机效果和"正在输入..."状态

#### 4.3 多媒体功能测试
1. **图片上传**: 点击图片按钮上传图片
2. **文件上传**: 点击文件按钮上传文档
3. **剪贴板粘贴**: 使用微信截图后按Ctrl+V粘贴
4. **拖拽上传**: 直接拖拽文件到聊天区域

#### 4.4 Markdown渲染测试
发送以下消息测试Markdown渲染：
```
请用Markdown格式回复，包含：
1. 标题和子标题
2. 代码块
3. 表格
4. 列表
5. 引用块
```

## 配置说明

### Gemini API配置
```yaml
gemini:
  api:
    key: ${GEMINI_API_KEY}
    base-url: https://generativelanguage.googleapis.com
    model: gemini-2.5-flash
    timeout: 30000
```

### 流式响应配置
- 使用Server-Sent Events (SSE)
- 支持实时流式文本传输
- 自动保存完整回复到数据库

### 文件上传限制
- 图片: 最大10MB
- 文件: 最大20MB
- 支持格式: 所有常见图片和文档格式

## 故障排除

### 常见问题

#### 1. Gemini API调用失败
**症状**: AI无法回复，控制台显示API错误
**解决方案**:
- 检查API密钥是否正确
- 确认API密钥有足够的配额
- 检查网络连接是否正常

#### 2. 流式响应不工作
**症状**: 消息发送后没有实时显示
**解决方案**:
- 检查浏览器是否支持EventSource
- 确认后端WebFlux配置正确
- 查看网络请求是否被代理拦截

#### 3. 文件上传失败
**症状**: 图片或文件无法上传
**解决方案**:
- 检查文件大小是否超限
- 确认文件格式是否支持
- 查看后端文件上传配置

#### 4. Markdown渲染异常
**症状**: AI回复的Markdown格式显示不正确
**解决方案**:
- 检查react-markdown依赖是否正确安装
- 确认CSS样式是否加载
- 查看控制台是否有JavaScript错误

### 日志查看
```bash
# 后端日志
tail -f backend/logs/buwan-psychology.log

# 前端控制台
# 打开浏览器开发者工具查看Console
```

## 性能优化

### 1. 数据库优化
- 为session_id和user_id添加索引
- 定期清理过期会话数据
- 使用连接池优化数据库连接

### 2. 前端优化
- 启用代码分割和懒加载
- 优化Markdown渲染性能
- 使用虚拟滚动处理大量消息

### 3. API优化
- 实现请求缓存机制
- 添加API限流保护
- 优化Gemini API调用频率

## 安全考虑

### 1. API密钥安全
- 使用环境变量存储API密钥
- 定期轮换API密钥
- 监控API使用情况

### 2. 文件上传安全
- 验证文件类型和大小
- 扫描上传文件的安全性
- 限制文件访问权限

### 3. 用户数据保护
- 加密敏感聊天内容
- 实现数据备份机制
- 遵循数据保护法规

## 扩展功能

### 计划中的功能
1. **语音输入** - 支持语音转文字
2. **多语言支持** - 国际化界面
3. **主题定制** - 个性化界面主题
4. **导出功能** - 聊天记录导出
5. **群组聊天** - 多人会话支持

### 集成建议
1. **监控系统** - 集成Prometheus + Grafana
2. **日志分析** - 使用ELK Stack
3. **容器化** - Docker + Kubernetes部署
4. **CDN加速** - 静态资源CDN分发

## 联系支持

如遇到问题，请提供以下信息：
1. 错误日志截图
2. 浏览器版本和操作系统
3. 复现步骤描述
4. 相关配置信息（隐藏敏感数据）

---

**注意**: 请确保在生产环境中使用HTTPS协议，并定期更新依赖包以保证安全性。

import React from 'react'
import { Layout, <PERSON>u, Button, Avatar, Dropdown, Space, Typography } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import {
  UserOutlined,
  MessageOutlined,
  DashboardOutlined,
  LogoutOutlined,
  SettingOutlined,
  HeartOutlined,
  TeamOutlined
} from '@ant-design/icons'
import { RootState, AppDispatch } from '@/store'
import { logout } from '@/store/slices/authSlice'

const { Header: AntHeader } = Layout
const { Text } = Typography

const Header: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth)

  const handleLogout = () => {
    dispatch(logout())
    navigate('/')
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'edit-profile',
      icon: <SettingOutlined />,
      label: '编辑资料',
      onClick: () => navigate('/profile/edit'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ]

  const menuItems = [
    {
      key: '/',
      label: '首页',
      onClick: () => navigate('/'),
    },
    {
      key: '/counselors',
      label: '专家团队',
      icon: <TeamOutlined />,
      onClick: () => navigate('/counselors'),
    },
    ...(isAuthenticated ? [
      {
        key: '/chat',
        label: '心理咨询',
        icon: <MessageOutlined />,
        onClick: () => navigate('/chat'),
      },
      {
        key: '/assessment',
        label: '心理评估',
        icon: <HeartOutlined />,
        onClick: () => navigate('/assessment'),
      },
      {
        key: '/profile',
        label: '个人中心',
        icon: <DashboardOutlined />,
        onClick: () => navigate('/profile'),
      },
    ] : []),
  ]

  return (
    <AntHeader className="header">
      <div className="header-content">
        <div className="header-left">
          <div className="logo" onClick={() => navigate('/')}>
            <HeartOutlined className="logo-icon" />
            <Text className="logo-text">不晚心理</Text>
          </div>
          
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            className="header-menu"
          />
        </div>

        <div className="header-right">
          {isAuthenticated ? (
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space className="user-info">
                <Avatar 
                  src={user?.avatar} 
                  icon={<UserOutlined />}
                  className="user-avatar"
                />
                <Text className="user-name">
                  {user?.nickname || user?.username}
                </Text>
              </Space>
            </Dropdown>
          ) : (
            <Space>
              <Button 
                type="text" 
                onClick={() => navigate('/login')}
                className="auth-button"
              >
                登录
              </Button>
              <Button 
                type="primary" 
                onClick={() => navigate('/register')}
                className="auth-button"
              >
                注册
              </Button>
            </Space>
          )}
        </div>
      </div>
    </AntHeader>
  )
}

export default Header

package com.buwan.psychology.service;

import com.buwan.psychology.entity.ChatMessage;
import com.buwan.psychology.entity.ConsultationSession;
import com.buwan.psychology.entity.Counselor;
import com.buwan.psychology.entity.User;
import com.buwan.psychology.repository.ChatMessageRepository;
import com.buwan.psychology.repository.ConsultationSessionRepository;
import com.buwan.psychology.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import java.util.Optional;

/**
 * 聊天服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ChatService {

    private final ConsultationSessionRepository sessionRepository;
    private final ChatMessageRepository messageRepository;
    private final UserRepository userRepository;
    private final GeminiService geminiService;
    private final CounselorService counselorService;

    /**
     * 获取用户的会话列表
     */
    public List<ConsultationSession> getUserSessions(Long userId) {
        log.info("获取用户会话列表，用户ID: {}", userId);
        return sessionRepository.findByUserIdWithCounselorOrderByCreateTimeDesc(userId);
    }

    /**
     * 创建新会话
     */
    public ConsultationSession createSession(Long userId, String title, Integer sessionType) {
        return createSession(userId, title, null, sessionType, null);
    }

    /**
     * 创建新会话（支持指定咨询师）
     */
    public ConsultationSession createSession(Long userId, String title, Integer sessionType, Long counselorId) {
        return createSession(userId, title, null, sessionType, counselorId);
    }

    /**
     * 创建新会话（支持详细描述和指定咨询师）
     */
    public ConsultationSession createSession(Long userId, String title, String description, Integer sessionType, Long counselorId) {
        log.info("创建新会话，用户ID: {}, 标题: {}, 类型: {}, 咨询师ID: {}", userId, title, sessionType, counselorId);

        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        ConsultationSession session = new ConsultationSession();
        session.setUserId(userId);
        session.setTitle(title);
        session.setDescription(description);
        session.setSessionType(sessionType);
        session.setCounselorId(counselorId);
        session.setStatus(0); // 进行中
        session.setStartTime(LocalDateTime.now());
        session.setUrgencyLevel(0); // 正常

        ConsultationSession savedSession = sessionRepository.save(session);
        log.info("会话创建成功，会话ID: {}", savedSession.getId());
        return savedSession;
    }

    /**
     * 创建新会话（支持智能匹配咨询师）
     */
    public ConsultationSession createSession(Long userId, String title, Integer sessionType, Long counselorId, Boolean autoMatch) {
        return createSession(userId, title, null, sessionType, counselorId, autoMatch);
    }

    /**
     * 创建新会话（支持详细描述和智能匹配咨询师）
     */
    public ConsultationSession createSession(Long userId, String title, String description, Integer sessionType, Long counselorId, Boolean autoMatch) {
        log.info("创建新会话，用户ID: {}, 标题: {}, 类型: {}, 咨询师ID: {}, 智能匹配: {}", userId, title, sessionType, counselorId, autoMatch);

        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        Long finalCounselorId = counselorId;

        // 如果启用智能匹配且未指定咨询师，则自动匹配
        if (autoMatch != null && autoMatch && counselorId == null) {
            log.info("启用智能匹配，根据会话类型 {} 匹配咨询师", sessionType);
            var matchedCounselor = counselorService.matchCounselorBySessionType(sessionType);
            if (matchedCounselor != null) {
                finalCounselorId = matchedCounselor.getId();
                log.info("智能匹配到咨询师: {} (ID: {})", matchedCounselor.getName(), finalCounselorId);
            } else {
                log.warn("智能匹配失败，未找到合适的咨询师");
            }
        }

        ConsultationSession session = new ConsultationSession();
        session.setUserId(userId);
        session.setTitle(title);
        session.setDescription(description);
        session.setSessionType(sessionType);
        session.setCounselorId(finalCounselorId);
        session.setStatus(0); // 进行中
        session.setStartTime(LocalDateTime.now());
        session.setUrgencyLevel(0); // 正常

        ConsultationSession savedSession = sessionRepository.save(session);
        log.info("会话创建成功，会话ID: {}, 分配咨询师ID: {}", savedSession.getId(), finalCounselorId);

        // 重新查询会话以获取完整的咨询师信息
        List<ConsultationSession> sessionsWithCounselor = sessionRepository.findByUserIdWithCounselorOrderByCreateTimeDesc(userId);
        Optional<ConsultationSession> newSession = sessionsWithCounselor.stream()
                .filter(s -> s.getId().equals(savedSession.getId()))
                .findFirst();

        if (newSession.isPresent()) {
            return newSession.get();
        }
        return savedSession;
    }

    /**
     * 获取会话消息列表
     */
    public List<ChatMessage> getSessionMessages(Long sessionId) {
        log.info("获取会话消息，会话ID: {}", sessionId);
        return messageRepository.findBySessionIdOrderByCreateTimeAsc(sessionId);
    }

    /**
     * 发送消息（支持附件）
     */
    @SuppressWarnings("unchecked")
    public Mono<ChatMessage> sendMessage(Long sessionId, Long userId, String content, List<ChatMessage.Attachment> attachments) {
        log.info("发送消息，会话ID: {}, 用户ID: {}, 附件数量: {}", sessionId, userId, attachments != null ? attachments.size() : 0);

        return Mono.fromCallable(() -> {
            // 验证会话是否存在
            Optional<ConsultationSession> sessionOpt = sessionRepository.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("会话不存在");
            }

            ConsultationSession session = sessionOpt.get();

            // 验证用户权限
            if (!session.getUserId().equals(userId)) {
                throw new RuntimeException("无权限访问此会话");
            }

            // 保存用户消息
            ChatMessage userMessage = new ChatMessage();
            userMessage.setSessionId(sessionId);
            userMessage.setUserId(userId);
            userMessage.setContent(content);
            userMessage.setMessageType(0); // 用户消息
            userMessage.setAttachments(attachments);

            ChatMessage savedUserMessage = messageRepository.save(userMessage);
            log.info("用户消息保存成功，消息ID: {}", savedUserMessage.getId());

            return savedUserMessage;
        })
        .flatMap(userMessage -> {
            // 异步生成AI回复
            return generateAiResponse(sessionId, userId, content, attachments)
                    .doOnNext(aiMessage -> log.info("AI回复生成成功，消息ID: {}", aiMessage.getId()))
                    .doOnError(error -> log.error("生成AI回复失败", error))
                    .onErrorReturn(createErrorMessage(sessionId, userId))
                    .thenReturn(userMessage);
        });
    }

    /**
     * 发送消息（兼容旧版本）
     */
    public ChatMessage sendMessage(Long sessionId, Long userId, String content) {
        return sendMessage(sessionId, userId, content, null).block();
    }

    /**
     * 阻塞模式发送消息
     */
    public ChatMessage sendMessageBlocking(Long sessionId, Long userId, String content, List<ChatMessage.Attachment> attachments) {
        log.info("阻塞模式发送消息，会话ID: {}, 用户ID: {}, 附件数量: {}", sessionId, userId, attachments != null ? attachments.size() : 0);

        try {
            // 验证会话是否存在
            Optional<ConsultationSession> sessionOpt = sessionRepository.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("会话不存在");
            }

            ConsultationSession session = sessionOpt.get();

            // 验证用户权限
            if (!session.getUserId().equals(userId)) {
                throw new RuntimeException("无权限访问此会话");
            }

            // 保存用户消息
            ChatMessage userMessage = new ChatMessage();
            userMessage.setSessionId(sessionId);
            userMessage.setUserId(userId);
            userMessage.setContent(content);
            userMessage.setMessageType(0); // 用户消息
            userMessage.setAttachments(attachments);
            userMessage.setCreateTime(LocalDateTime.now());

            ChatMessage savedUserMessage = messageRepository.save(userMessage);
            log.info("用户消息保存成功，消息ID: {}", savedUserMessage.getId());

            // 阻塞模式生成AI回复
            ChatMessage aiMessage = generateAiResponseBlocking(sessionId, userId, content, attachments);
            log.info("AI回复生成成功，消息ID: {}", aiMessage.getId());

            return savedUserMessage;
        } catch (Exception e) {
            log.error("阻塞模式发送消息失败", e);
            throw e;
        }
    }

    /**
     * 阻塞模式生成AI回复
     */
    private ChatMessage generateAiResponseBlocking(Long sessionId, Long userId, String content, List<ChatMessage.Attachment> attachments) {
        try {
            // 获取对话历史
            List<ChatMessage> conversationHistory = getSessionMessages(sessionId);

            // 获取会话信息
            Optional<ConsultationSession> sessionOpt = sessionRepository.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("会话不存在");
            }

            ConsultationSession session = sessionOpt.get();

            // 获取咨询师信息
            Counselor counselor = null;
            if (session.getCounselorId() != null) {
                counselor = counselorService.getCounselorById(session.getCounselorId()).orElse(null);
            }

            // 获取用户信息
            User user = userRepository.findById(userId).orElse(null);

            // 调用Gemini API生成回复（阻塞模式）
            String aiResponse = geminiService.generateResponse(conversationHistory, content, attachments).block();

            // 保存AI回复
            ChatMessage aiMessage = new ChatMessage();
            aiMessage.setSessionId(sessionId);
            aiMessage.setUserId(userId);
            aiMessage.setContent(aiResponse);
            aiMessage.setMessageType(1); // AI回复
            aiMessage.setConfidence(85); // 模拟置信度
            aiMessage.setCreateTime(LocalDateTime.now());

            return messageRepository.save(aiMessage);
        } catch (Exception e) {
            log.error("阻塞模式生成AI回复失败", e);
            return createErrorMessage(sessionId, userId);
        }
    }

    /**
     * 生成AI回复
     */
    private Mono<ChatMessage> generateAiResponse(Long sessionId, Long userId, String content, List<ChatMessage.Attachment> attachments) {
        return Mono.fromCallable(() -> {
            // 获取对话历史
            List<ChatMessage> conversationHistory = getSessionMessages(sessionId);
            return conversationHistory;
        })
        .flatMap(history -> {
            // 调用Gemini API生成回复
            return geminiService.generateResponse(history, content, attachments);
        })
        .map(aiResponse -> {
            // 保存AI回复
            ChatMessage aiMessage = new ChatMessage();
            aiMessage.setSessionId(sessionId);
            aiMessage.setUserId(userId);
            aiMessage.setContent(aiResponse);
            aiMessage.setMessageType(1); // AI回复
            aiMessage.setConfidence(85); // 模拟置信度

            return messageRepository.save(aiMessage);
        });
    }

    /**
     * 流式生成AI回复
     */
    public Flux<String> generateStreamResponse(Long sessionId, Long userId, String content, List<ChatMessage.Attachment> attachments) {
        log.info("开始流式生成AI回复，会话ID: {}, 用户ID: {}", sessionId, userId);

        return Mono.fromCallable(() -> {
            // 验证会话权限
            Optional<ConsultationSession> sessionOpt = sessionRepository.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("会话不存在");
            }

            ConsultationSession session = sessionOpt.get();
            if (!session.getUserId().equals(userId)) {
                throw new RuntimeException("无权限访问此会话");
            }

            // 获取对话历史
            List<ChatMessage> history = getSessionMessages(sessionId);

            // 获取咨询师信息
            Counselor counselor = null;
            if (session.getCounselorId() != null) {
                counselor = counselorService.getCounselorById(session.getCounselorId()).orElse(null);
            }

            // 获取用户信息
            User user = userRepository.findById(userId).orElse(null);

            return new Object[]{history, counselor, user, session.getSessionType(), session.getTitle(), session.getDescription()};
        })
        .flatMapMany(data -> {
            Object[] dataArray = (Object[]) data;
            @SuppressWarnings("unchecked")
            List<ChatMessage> history = (List<ChatMessage>) dataArray[0];
            Counselor counselor = (Counselor) dataArray[1];
            User user = (User) dataArray[2];
            Integer sessionType = (Integer) dataArray[3];
            String sessionTitle = (String) dataArray[4];
            String sessionDescription = (String) dataArray[5];

            // 调用Gemini流式API（带会话信息）
            return geminiService.generateStreamResponse(history, content, attachments, counselor, user,
                    sessionType, sessionTitle, sessionDescription);
        })
        .doOnComplete(() -> log.info("流式回复生成完成，会话ID: {}", sessionId))
        .doOnError(error -> log.error("流式回复生成失败，会话ID: {}", sessionId, error));
    }

    /**
     * 保存完整的AI回复
     */
    public Mono<ChatMessage> saveCompleteAiResponse(Long sessionId, Long userId, String completeResponse) {
        return Mono.fromCallable(() -> {
            // 查找该会话的所有消息，按时间倒序
            List<ChatMessage> allMessages = messageRepository.findBySessionIdOrderByCreateTimeDesc(sessionId);

            // 查找最新的用户消息
            ChatMessage latestUserMessage = null;
            for (ChatMessage message : allMessages) {
                if (message.getMessageType() == 0) { // 用户消息
                    latestUserMessage = message;
                    break;
                }
            }

            // 创建AI消息，确保时间在最新用户消息之后
            LocalDateTime aiMessageTime;
            if (latestUserMessage != null && latestUserMessage.getCreateTime() != null) {
                // AI消息时间 = 用户消息时间 + 2秒，确保有足够的时间差
                aiMessageTime = latestUserMessage.getCreateTime().plusSeconds(2);
                log.info("基于用户消息时间设置AI消息时间: 用户消息时间={}, AI消息时间={}",
                    latestUserMessage.getCreateTime(), aiMessageTime);
            } else {
                // 如果没有用户消息，使用当前时间
                aiMessageTime = LocalDateTime.now();
                log.info("未找到用户消息，使用当前时间作为AI消息时间: {}", aiMessageTime);
            }

            ChatMessage aiMessage = new ChatMessage();
            aiMessage.setSessionId(sessionId);
            aiMessage.setUserId(userId);
            aiMessage.setContent(completeResponse);
            aiMessage.setMessageType(1); // AI回复
            aiMessage.setConfidence(85);
            aiMessage.setCreateTime(aiMessageTime); // 设置创建时间

            ChatMessage saved = messageRepository.save(aiMessage);
            log.info("完整AI回复保存成功，消息ID: {}, 创建时间: {}", saved.getId(), aiMessageTime);
            return saved;
        });
    }

    /**
     * 创建错误消息
     */
    private ChatMessage createErrorMessage(Long sessionId, Long userId) {
        ChatMessage errorMessage = new ChatMessage();
        errorMessage.setSessionId(sessionId);
        errorMessage.setUserId(userId);
        errorMessage.setContent("抱歉，我现在无法回复您的消息，请稍后再试。");
        errorMessage.setMessageType(1); // AI回复
        errorMessage.setConfidence(0);

        return messageRepository.save(errorMessage);
    }

    /**
     * 结束会话
     */
    public void endSession(Long sessionId, Long userId) {
        log.info("结束会话，会话ID: {}, 用户ID: {}", sessionId, userId);
        
        Optional<ConsultationSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isEmpty()) {
            throw new RuntimeException("会话不存在");
        }
        
        ConsultationSession session = sessionOpt.get();
        
        // 验证用户权限
        if (!session.getUserId().equals(userId)) {
            throw new RuntimeException("无权限访问此会话");
        }

        session.setStatus(1); // 已结束
        session.setEndTime(LocalDateTime.now());
        
        // 生成会话摘要
        List<ChatMessage> messages = getSessionMessages(sessionId);
        if (!messages.isEmpty()) {
            StringBuilder summary = new StringBuilder();
            summary.append("会话包含 ").append(messages.size()).append(" 条消息。");
            
            // 简单的摘要生成逻辑
            long userMessages = messages.stream().filter(m -> m.getMessageType() == 0).count();
            long aiMessages = messages.stream().filter(m -> m.getMessageType() == 1).count();
            
            summary.append("用户发送了 ").append(userMessages).append(" 条消息，");
            summary.append("AI回复了 ").append(aiMessages).append(" 条消息。");
            
            session.setSummary(summary.toString());
        }

        sessionRepository.save(session);
        log.info("会话结束成功，会话ID: {}", sessionId);
    }

    /**
     * 获取会话详情
     */
    public Optional<ConsultationSession> getSession(Long sessionId) {
        return sessionRepository.findById(sessionId);
    }

    /**
     * 保存用户消息
     */
    public ChatMessage saveUserMessage(ChatMessage userMessage) {
        try {
            userMessage.setCreateTime(LocalDateTime.now());
            ChatMessage saved = messageRepository.save(userMessage);
            log.info("保存用户消息成功，消息ID: {}, 内容长度: {}", saved.getId(), userMessage.getContent().length());
            return saved;
        } catch (Exception e) {
            log.error("保存用户消息失败", e);
            throw new RuntimeException("保存用户消息失败", e);
        }
    }

    /**
     * 清空会话消息
     */
    public void clearSessionMessages(Long sessionId, Long userId) {
        try {
            // 验证会话权限
            Optional<ConsultationSession> sessionOpt = sessionRepository.findById(sessionId);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("会话不存在");
            }

            ConsultationSession session = sessionOpt.get();
            if (!session.getUserId().equals(userId)) {
                throw new RuntimeException("无权限访问此会话");
            }

            // 删除该会话的所有消息
            List<ChatMessage> messages = messageRepository.findBySessionIdOrderByCreateTimeAsc(sessionId);
            messageRepository.deleteAll(messages);

            log.info("清空会话消息成功，会话ID: {}, 删除消息数量: {}", sessionId, messages.size());
        } catch (Exception e) {
            log.error("清空会话消息失败", e);
            throw new RuntimeException("清空会话消息失败", e);
        }
    }

    /**
     * 保存完整的AI回复
     */
    public void saveCompleteResponse(Long sessionId, String content) {
        try {
            // 查找该会话的所有消息，按时间倒序
            List<ChatMessage> allMessages = messageRepository.findBySessionIdOrderByCreateTimeDesc(sessionId);

            // 查找最新的AI消息
            ChatMessage latestAiMessage = null;
            for (ChatMessage message : allMessages) {
                if (message.getMessageType() == 1) { // AI消息
                    latestAiMessage = message;
                    break;
                }
            }

            if (latestAiMessage != null) {
                // 更新最新的AI消息
                latestAiMessage.setContent(content);
                messageRepository.save(latestAiMessage);
                log.info("更新AI消息完成，消息ID: {}, 内容长度: {}", latestAiMessage.getId(), content.length());
            } else {
                // 查找最新的用户消息
                ChatMessage latestUserMessage = null;
                for (ChatMessage message : allMessages) {
                    if (message.getMessageType() == 0) { // 用户消息
                        latestUserMessage = message;
                        break;
                    }
                }

                // 创建新的AI消息，确保时间在最新用户消息之后
                LocalDateTime aiMessageTime;
                if (latestUserMessage != null && latestUserMessage.getCreateTime() != null) {
                    // AI消息时间 = 用户消息时间 + 2秒，确保有足够的时间差
                    aiMessageTime = latestUserMessage.getCreateTime().plusSeconds(2);
                    log.info("基于用户消息时间设置AI消息时间: 用户消息时间={}, AI消息时间={}",
                        latestUserMessage.getCreateTime(), aiMessageTime);
                } else {
                    // 如果没有用户消息，使用当前时间
                    aiMessageTime = LocalDateTime.now();
                    log.info("未找到用户消息，使用当前时间作为AI消息时间: {}", aiMessageTime);
                }

                ChatMessage aiMessage = new ChatMessage();
                aiMessage.setSessionId(sessionId);
                aiMessage.setUserId(1L); // 系统用户ID
                aiMessage.setContent(content);
                aiMessage.setMessageType(1); // AI消息
                aiMessage.setCreateTime(aiMessageTime);

                ChatMessage saved = messageRepository.save(aiMessage);
                log.info("创建新AI消息完成，消息ID: {}, 内容长度: {}, 创建时间: {}",
                    saved.getId(), content.length(), aiMessageTime);
            }
        } catch (Exception e) {
            log.error("保存完整回复失败", e);
            throw new RuntimeException("保存完整回复失败", e);
        }
    }
}

package com.buwan.psychology.repository;

import com.buwan.psychology.entity.Counselor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 心理咨询师数据访问层
 */
@Repository
public interface CounselorRepository extends JpaRepository<Counselor, Long> {

    /**
     * 查找可用的咨询师
     */
    List<Counselor> findByIsAvailableTrue();

    /**
     * 根据专业领域查找咨询师
     */
    @Query("SELECT c FROM Counselor c WHERE c.isAvailable = true AND c.specializations LIKE %:specialization%")
    List<Counselor> findBySpecializationAndAvailable(@Param("specialization") String specialization);

    /**
     * 按评分降序查找可用咨询师
     */
    List<Counselor> findByIsAvailableTrueOrderByRatingDesc();

    /**
     * 按咨询次数升序查找可用咨询师（负载均衡）
     */
    List<Counselor> findByIsAvailableTrueOrderByConsultationCountAsc();

    /**
     * 查找评分高于指定值的可用咨询师
     */
    List<Counselor> findByIsAvailableTrueAndRatingGreaterThanEqual(Double minRating);

    /**
     * 根据从业年限查找咨询师
     */
    List<Counselor> findByIsAvailableTrueAndExperienceGreaterThanEqual(Integer minExperience);

    /**
     * 智能匹配咨询师 - 根据专业领域、评分和负载
     */
    @Query("SELECT c FROM Counselor c WHERE c.isAvailable = true " +
           "AND c.specializations LIKE %:specialization% " +
           "AND c.rating >= :minRating " +
           "ORDER BY c.rating DESC, c.consultationCount ASC")
    List<Counselor> findBestMatchCounselors(@Param("specialization") String specialization, 
                                          @Param("minRating") Double minRating);

    /**
     * 获取推荐咨询师（高评分、经验丰富）
     */
    @Query("SELECT c FROM Counselor c WHERE c.isAvailable = true " +
           "AND c.rating >= 4.0 AND c.experience >= 3 " +
           "ORDER BY c.rating DESC, c.experience DESC")
    List<Counselor> findRecommendedCounselors();
}

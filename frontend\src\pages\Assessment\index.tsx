import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Typography, Button, Tag, Progress, Empty, Spin } from 'antd'
import {
  ExperimentOutlined,
  PlayCircleOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  UserOutlined,
  HeartOutlined,
  <PERSON>boltOutlined,
  TeamOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import './index.css'

const { Title, Paragraph, Text } = Typography

// 评估类型定义
interface Assessment {
  id: string
  title: string
  description: string
  type: 'test' | 'game' | 'survey'
  category: string
  duration: number // 分钟
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  icon: React.ReactNode
  color: string
  completedCount?: number
  totalQuestions?: number
  isPopular?: boolean
  isNew?: boolean
}

const Assessment: React.FC = () => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // 模拟评估数据
  const assessments: Assessment[] = [
    {
      id: 'anxiety-scale',
      title: '焦虑自评量表',
      description: '通过专业的心理量表，评估您当前的焦虑水平，了解焦虑对生活的影响程度',
      type: 'test',
      category: '情绪评估',
      duration: 10,
      difficulty: 'easy',
      tags: ['焦虑', '情绪', '自评'],
      icon: <HeartOutlined />,
      color: '#ff7875',
      totalQuestions: 20,
      isPopular: true
    },
    {
      id: 'depression-scale',
      title: '抑郁自评量表',
      description: '科学评估抑郁情绪状态，帮助您了解心理健康状况，及时发现潜在问题',
      type: 'test',
      category: '情绪评估',
      duration: 12,
      difficulty: 'easy',
      tags: ['抑郁', '情绪', '心理健康'],
      icon: <HeartOutlined />,
      color: '#597ef7',
      totalQuestions: 21
    },
    {
      id: 'personality-test',
      title: '大五人格测试',
      description: '深入了解您的性格特征，包括开放性、尽责性、外向性、宜人性和神经质五个维度',
      type: 'test',
      category: '性格分析',
      duration: 25,
      difficulty: 'medium',
      tags: ['性格', '人格', '自我认知'],
      icon: <UserOutlined />,
      color: '#73d13d',
      totalQuestions: 60,
      isPopular: true
    },
    {
      id: 'stress-test',
      title: '压力水平评估',
      description: '评估您当前面临的压力程度，识别压力来源，提供针对性的缓解建议',
      type: 'test',
      category: '压力管理',
      duration: 8,
      difficulty: 'easy',
      tags: ['压力', '工作', '生活'],
      icon: <ThunderboltOutlined />,
      color: '#ffa940',
      totalQuestions: 15
    },
    {
      id: 'emotional-intelligence',
      title: '情商测试',
      description: '测试您的情绪智力水平，包括自我认知、自我管理、社会认知和关系管理能力',
      type: 'test',
      category: '能力评估',
      duration: 20,
      difficulty: 'medium',
      tags: ['情商', '社交', '情绪管理'],
      icon: <TeamOutlined />,
      color: '#b37feb',
      totalQuestions: 40
    },
    {
      id: 'color-psychology',
      title: '色彩心理游戏',
      description: '通过有趣的色彩选择游戏，探索您的内心世界和潜在性格特征',
      type: 'game',
      category: '趣味测试',
      duration: 5,
      difficulty: 'easy',
      tags: ['色彩', '潜意识', '趣味'],
      icon: <ExperimentOutlined />,
      color: '#ff85c0',
      isNew: true
    },
    {
      id: 'memory-game',
      title: '记忆力挑战',
      description: '通过记忆游戏测试您的短期记忆、工作记忆和注意力水平',
      type: 'game',
      category: '认知能力',
      duration: 15,
      difficulty: 'hard',
      tags: ['记忆力', '注意力', '认知'],
      icon: <ThunderboltOutlined />,
      color: '#36cfc9',
      isNew: true
    },
    {
      id: 'life-satisfaction',
      title: '生活满意度调查',
      description: '全面评估您对生活各个方面的满意程度，帮助识别需要改善的领域',
      type: 'survey',
      category: '生活质量',
      duration: 15,
      difficulty: 'easy',
      tags: ['生活质量', '满意度', '幸福感'],
      icon: <TrophyOutlined />,
      color: '#95de64',
      totalQuestions: 25
    }
  ]

  // 分类选项
  const categories = [
    { key: 'all', label: '全部', count: assessments.length },
    { key: '情绪评估', label: '情绪评估', count: assessments.filter(a => a.category === '情绪评估').length },
    { key: '性格分析', label: '性格分析', count: assessments.filter(a => a.category === '性格分析').length },
    { key: '压力管理', label: '压力管理', count: assessments.filter(a => a.category === '压力管理').length },
    { key: '能力评估', label: '能力评估', count: assessments.filter(a => a.category === '能力评估').length },
    { key: '趣味测试', label: '趣味测试', count: assessments.filter(a => a.category === '趣味测试').length },
    { key: '认知能力', label: '认知能力', count: assessments.filter(a => a.category === '认知能力').length },
    { key: '生活质量', label: '生活质量', count: assessments.filter(a => a.category === '生活质量').length }
  ]

  // 过滤评估
  const filteredAssessments = selectedCategory === 'all'
    ? assessments
    : assessments.filter(assessment => assessment.category === selectedCategory)

  // 获取难度标签颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'green'
      case 'medium': return 'orange'
      case 'hard': return 'red'
      default: return 'default'
    }
  }

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'test': return '心理测试'
      case 'game': return '心理游戏'
      case 'survey': return '问卷调查'
      default: return type
    }
  }

  // 开始评估
  const handleStartAssessment = (assessment: Assessment) => {
    navigate(`/assessment/${assessment.id}`)
  }

  return (
    <div className="assessment-container">
      {/* 页面标题 */}
      <div className="assessment-hero">
        <div className="assessment-hero-content">
          <ExperimentOutlined className="hero-icon" />
          <Title level={1} className="hero-title">
            心理评估中心
          </Title>
          <Paragraph className="hero-subtitle">
            通过科学的心理测试和趣味游戏，深入了解自己的心理状态和性格特征
          </Paragraph>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="assessment-main-content">
        {/* 分类筛选 */}
        <Card className="category-card">
          <div className="category-tabs">
            {categories.map(category => (
              <Button
                key={category.key}
                type={selectedCategory === category.key ? 'primary' : 'default'}
                className={`category-tab ${selectedCategory === category.key ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.key)}
              >
                {category.label}
                <span className="category-count">({category.count})</span>
              </Button>
            ))}
          </div>
        </Card>

        {/* 评估列表 */}
        <Spin spinning={loading} size="large">
          {filteredAssessments.length === 0 ? (
            <div className="empty-state">
              <Empty
                description="暂无相关评估"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          ) : (
            <Row gutter={[24, 24]} className="assessments-grid">
              {filteredAssessments.map(assessment => (
                <Col xs={24} sm={12} lg={8} xl={6} key={assessment.id}>
                  <Card
                    hoverable
                    className="assessment-card"
                    actions={[
                      <Button
                        type="primary"
                        icon={<PlayCircleOutlined />}
                        onClick={() => handleStartAssessment(assessment)}
                        className="start-button"
                      >
                        开始测试
                      </Button>
                    ]}
                  >
                    {/* 卡片头部 */}
                    <div className="assessment-header">
                      <div
                        className="assessment-icon"
                        style={{ backgroundColor: assessment.color }}
                      >
                        {assessment.icon}
                      </div>
                      <div className="assessment-badges">
                        {assessment.isPopular && (
                          <Tag color="red" className="badge">热门</Tag>
                        )}
                        {assessment.isNew && (
                          <Tag color="blue" className="badge">新品</Tag>
                        )}
                      </div>
                    </div>

                    {/* 卡片内容 */}
                    <div className="assessment-content">
                      <Title level={4} className="assessment-title">
                        {assessment.title}
                      </Title>

                      <Paragraph
                        className="assessment-description"
                        ellipsis={{ rows: 3 }}
                      >
                        {assessment.description}
                      </Paragraph>

                      {/* 标签 */}
                      <div className="assessment-tags">
                        {assessment.tags.slice(0, 3).map(tag => (
                          <Tag key={tag} className="assessment-tag">
                            {tag}
                          </Tag>
                        ))}
                      </div>

                      {/* 元信息 */}
                      <div className="assessment-meta">
                        <div className="meta-item">
                          <ClockCircleOutlined className="meta-icon" />
                          <span>{assessment.duration}分钟</span>
                        </div>
                        <div className="meta-item">
                          <Tag color={getDifficultyColor(assessment.difficulty)}>
                            {assessment.difficulty === 'easy' ? '简单' :
                             assessment.difficulty === 'medium' ? '中等' : '困难'}
                          </Tag>
                        </div>
                      </div>

                      <div className="assessment-type">
                        <Tag color="blue">{getTypeLabel(assessment.type)}</Tag>
                        {assessment.totalQuestions && (
                          <Text type="secondary" className="question-count">
                            {assessment.totalQuestions}题
                          </Text>
                        )}
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Spin>
      </div>
    </div>
  )
}

export default Assessment

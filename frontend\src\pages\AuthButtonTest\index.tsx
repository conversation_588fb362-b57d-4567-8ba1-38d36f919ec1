import React, { useState } from 'react'
import { Card, Button, Typography, Row, Col, Form, Input, Space } from 'antd'
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const AuthButtonTest: React.FC = () => {
  const [loginLoading, setLoginLoading] = useState(false)
  const [registerLoading, setRegisterLoading] = useState(false)

  const handleLoginTest = () => {
    setLoginLoading(true)
    setTimeout(() => setLoginLoading(false), 2000)
  }

  const handleRegisterTest = () => {
    setRegisterLoading(true)
    setTimeout(() => setRegisterLoading(false), 2000)
  }

  return (
    <div style={{ padding: '40px', background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1} style={{ textAlign: 'center', marginBottom: '40px' }}>
          认证页面设计测试
        </Title>

        <Row gutter={[24, 24]}>
          {/* 登录按钮测试 */}
          <Col xs={24} md={12}>
            <Card title="登录按钮测试" style={{ height: '100%' }}>
              <div style={{ 
                background: 'rgba(255, 255, 255, 0.95)',
                padding: '32px',
                borderRadius: '16px',
                backdropFilter: 'blur(10px)'
              }}>
                <Title level={3} style={{ textAlign: 'center', marginBottom: '24px', color: '#1e293b' }}>
                  登录
                </Title>
                
                <Form layout="vertical">
                  <Form.Item label="用户名" required>
                    <Input 
                      prefix={<UserOutlined />} 
                      placeholder="请输入用户名"
                      className="auth-input"
                    />
                  </Form.Item>
                  
                  <Form.Item label="密码" required>
                    <Input.Password 
                      prefix={<LockOutlined />} 
                      placeholder="请输入密码"
                      className="auth-input"
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loginLoading}
                      className="auth-button"
                      block
                      onClick={handleLoginTest}
                    >
                      登录
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </Card>
          </Col>

          {/* 注册按钮测试 */}
          <Col xs={24} md={12}>
            <Card title="注册按钮测试" style={{ height: '100%' }}>
              <div style={{ 
                background: 'rgba(255, 255, 255, 0.95)',
                padding: '32px',
                borderRadius: '16px',
                backdropFilter: 'blur(10px)'
              }}>
                <Title level={3} style={{ textAlign: 'center', marginBottom: '24px', color: '#1e293b' }}>
                  注册
                </Title>
                
                <Form layout="vertical">
                  <Form.Item label="用户名" required>
                    <Input 
                      prefix={<UserOutlined />} 
                      placeholder="请输入用户名"
                      className="auth-input"
                    />
                  </Form.Item>
                  
                  <Form.Item label="邮箱" required>
                    <Input 
                      prefix={<MailOutlined />} 
                      placeholder="请输入邮箱"
                      className="auth-input"
                    />
                  </Form.Item>
                  
                  <Form.Item label="密码" required>
                    <Input.Password 
                      prefix={<LockOutlined />} 
                      placeholder="请输入密码"
                      className="auth-input"
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={registerLoading}
                      className="auth-button"
                      block
                      onClick={handleRegisterTest}
                    >
                      注册
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </Card>
          </Col>

          {/* 按钮样式对比 */}
          <Col xs={24}>
            <Card title="按钮样式对比测试">
              <div style={{ background: '#f8fafc', padding: '32px', borderRadius: '12px' }}>
                <Row gutter={[24, 24]}>
                  <Col xs={24} md={8}>
                    <div style={{ textAlign: 'center' }}>
                      <Text strong style={{ display: 'block', marginBottom: '16px' }}>
                        认证按钮（正常状态）
                      </Text>
                      <Button className="auth-button" block>
                        认证按钮
                      </Button>
                    </div>
                  </Col>
                  
                  <Col xs={24} md={8}>
                    <div style={{ textAlign: 'center' }}>
                      <Text strong style={{ display: 'block', marginBottom: '16px' }}>
                        认证按钮（加载状态）
                      </Text>
                      <Button className="auth-button" loading block>
                        加载中...
                      </Button>
                    </div>
                  </Col>
                  
                  <Col xs={24} md={8}>
                    <div style={{ textAlign: 'center' }}>
                      <Text strong style={{ display: 'block', marginBottom: '16px' }}>
                        标准Primary按钮
                      </Text>
                      <Button type="primary" block>
                        标准按钮
                      </Button>
                    </div>
                  </Col>
                </Row>
              </div>
            </Card>
          </Col>

          {/* 样式规格说明 */}
          <Col xs={24}>
            <Card title="认证按钮样式规格">
              <div style={{ background: '#f8fafc', padding: '32px', borderRadius: '12px' }}>
                <Title level={4} style={{ marginBottom: '24px' }}>
                  按钮应该满足以下规格：
                </Title>
                
                <Row gutter={[24, 24]}>
                  <Col xs={24} md={6}>
                    <div>
                      <Text strong style={{ color: '#059669' }}>✅ 尺寸规格</Text>
                      <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                        <li>高度：52px</li>
                        <li>宽度：100%（全宽）</li>
                        <li>圆角：12px</li>
                        <li>字体大小：16px</li>
                      </ul>
                    </div>
                  </Col>
                  
                  <Col xs={24} md={6}>
                    <div>
                      <Text strong style={{ color: '#059669' }}>✅ 颜色样式</Text>
                      <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                        <li>背景：紫色渐变</li>
                        <li>文字：白色</li>
                        <li>字体粗细：600</li>
                        <li>无边框</li>
                      </ul>
                    </div>
                  </Col>
                  
                  <Col xs={24} md={6}>
                    <div>
                      <Text strong style={{ color: '#059669' }}>✅ 交互效果</Text>
                      <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                        <li>悬停：向上移动3px</li>
                        <li>悬停：阴影加深</li>
                        <li>点击：向上移动1px</li>
                        <li>平滑过渡动画</li>
                      </ul>
                    </div>
                  </Col>
                  
                  <Col xs={24} md={6}>
                    <div>
                      <Text strong style={{ color: '#059669' }}>✅ 状态变化</Text>
                      <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                        <li>加载：显示加载图标</li>
                        <li>加载：背景色稍微变化</li>
                        <li>禁用：降低透明度</li>
                        <li>保持一致性</li>
                      </ul>
                    </div>
                  </Col>
                </Row>

                <div style={{ 
                  marginTop: '32px',
                  padding: '20px',
                  background: 'rgba(99, 102, 241, 0.1)',
                  borderRadius: '8px',
                  border: '1px solid rgba(99, 102, 241, 0.2)'
                }}>
                  <Text strong style={{ color: '#6366f1' }}>
                    🎯 关键要求：登录按钮和注册按钮必须完全一致
                  </Text>
                  <div style={{ marginTop: '8px' }}>
                    <Text style={{ color: '#475569' }}>
                      两个按钮应该有相同的尺寸、颜色、字体、圆角、阴影和交互效果。
                      不应该因为页面不同而有任何视觉差异。
                    </Text>
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default AuthButtonTest

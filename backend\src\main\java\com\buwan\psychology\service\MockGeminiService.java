package com.buwan.psychology.service;

import com.buwan.psychology.entity.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;

/**
 * 模拟Gemini服务，用于测试流式响应
 * 通过配置 gemini.mock.enabled=true 启用
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "gemini.mock.enabled", havingValue = "true")
public class MockGeminiService extends GeminiService {

    private static final String MOCK_RESPONSE = """
            # 心理咨询回复
            
            感谢您的信任，我理解您现在的感受。作为您的心理咨询师，我想告诉您：
            
            ## 您的感受是完全正常的
            
            每个人都会经历情绪波动，这是人类情感的自然表现。重要的是：
            
            1. **接纳自己的情绪** - 不要试图压抑或否认
            2. **寻找支持** - 与信任的人分享您的感受
            3. **关注当下** - 专注于此时此刻，而不是过度担忧未来
            
            ## 一些实用的建议
            
            ```markdown
            - 深呼吸练习：4秒吸气，4秒屏息，4秒呼气
            - 写日记：记录您的想法和感受
            - 适度运动：散步、瑜伽或其他您喜欢的活动
            ```
            
            > "治愈不是忘记痛苦，而是学会与它和平共处。"
            
            ## 下一步行动
            
            | 时间 | 建议活动 | 目标 |
            |------|----------|------|
            | 今天 | 深呼吸练习 | 缓解当前焦虑 |
            | 本周 | 建立日常routine | 增加稳定感 |
            | 长期 | 培养兴趣爱好 | 提升生活质量 |
            
            请记住，**您并不孤单**。如果情况持续恶化，建议寻求专业医疗帮助。
            
            您还有什么想要分享或询问的吗？我会一直陪伴您度过这个阶段。
            """;

    public MockGeminiService() {
        super(null, null); // 传入null，因为我们不会调用父类方法
    }

    @Override
    public Mono<String> generateResponse(List<ChatMessage> conversationHistory, String userMessage, List<ChatMessage.Attachment> attachments) {
        log.info("使用模拟Gemini服务生成回复");
        return Mono.just(MOCK_RESPONSE)
                .delayElement(Duration.ofSeconds(2)); // 模拟API延迟
    }

    @Override
    public Flux<String> generateStreamResponse(List<ChatMessage> conversationHistory, String userMessage, List<ChatMessage.Attachment> attachments) {
        log.info("使用模拟Gemini服务生成流式回复");
        
        // 将回复分割成小块，模拟流式响应
        String[] words = MOCK_RESPONSE.split("(?<=\\s)|(?=\\n)");
        
        return Flux.fromArray(words)
                .delayElements(Duration.ofMillis(50)) // 每50ms发送一个词
                .doOnNext(word -> log.debug("发送模拟数据块: {}", word.replace("\n", "\\n")))
                .doOnComplete(() -> log.info("模拟流式响应完成"));
    }
}

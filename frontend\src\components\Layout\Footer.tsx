import React from 'react'
import { Layout, Row, Col, Typography, Space, Divider } from 'antd'
import { 
  HeartOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  EnvironmentOutlined,
  WechatOutlined,
  QqOutlined
} from '@ant-design/icons'

const { Footer: AntFooter } = Layout
const { Title, Text, Link } = Typography

const Footer: React.FC = () => {
  return (
    <AntFooter className="footer dark-bg">
      <div className="footer-content">
        <Row gutter={[32, 32]}>
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <div className="footer-logo">
                <HeartOutlined className="footer-logo-icon" />
                <Title level={4} className="footer-logo-text">不晚心理</Title>
              </div>
              <Text className="footer-description">
                专业的AI心理健康平台，为您提供24/7的心理支持服务。
                关注心理健康，从现在开始。
              </Text>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={5} className="footer-title">服务内容</Title>
              <ul className="footer-links">
                <li><Link href="/chat">AI心理咨询</Link></li>
                <li><Link href="/assessment">心理健康评估</Link></li>
                <li><Link href="/teenager">青少年专项</Link></li>
                <li><Link href="/crisis">危机干预</Link></li>
              </ul>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={5} className="footer-title">帮助支持</Title>
              <ul className="footer-links">
                <li><Link href="/help">使用指南</Link></li>
                <li><Link href="/faq">常见问题</Link></li>
                <li><Link href="/privacy">隐私政策</Link></li>
                <li><Link href="/terms">服务条款</Link></li>
              </ul>
            </div>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={5} className="footer-title">联系我们</Title>
              <Space direction="vertical" size="small" className="contact-info">
                <div className="contact-item">
                  <PhoneOutlined className="contact-icon" />
                  <Text>************</Text>
                </div>
                <div className="contact-item">
                  <MailOutlined className="contact-icon" />
                  <Text><EMAIL></Text>
                </div>
                <div className="contact-item">
                  <EnvironmentOutlined className="contact-icon" />
                  <Text>北京市朝阳区xxx大厦</Text>
                </div>
              </Space>
              
              <div className="social-links">
                <Title level={5} className="footer-title">关注我们</Title>
                <Space size="middle">
                  <WechatOutlined className="social-icon" />
                  <QqOutlined className="social-icon" />
                </Space>
              </div>
            </div>
          </Col>
        </Row>

        <Divider className="footer-divider" />

        <div className="footer-bottom">
          <Row justify="space-between" align="middle">
            <Col>
              <Text className="copyright">
                © 2024 不晚心理. 保留所有权利.
              </Text>
            </Col>
            <Col>
              <Space split={<Divider type="vertical" />}>
                <Link href="/privacy">隐私政策</Link>
                <Link href="/terms">服务条款</Link>
                <Link href="/sitemap">网站地图</Link>
              </Space>
            </Col>
          </Row>
        </div>

        <div className="footer-disclaimer">
          <Text type="secondary" className="disclaimer-text">
            免责声明：本平台提供的AI心理咨询服务仅供参考，不能替代专业医疗诊断和治疗。
            如遇紧急情况，请立即联系当地急救服务或专业医疗机构。
          </Text>
        </div>
      </div>
    </AntFooter>
  )
}

export default Footer

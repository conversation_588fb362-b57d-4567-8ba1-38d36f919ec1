/**
 * 认证相关工具函数
 */

/**
 * 检查token是否过期
 */
export const isTokenExpired = (token: string | null): boolean => {
  if (!token) return true
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    console.error('解析token失败:', error)
    return true
  }
}

/**
 * 清除认证信息
 */
export const clearAuth = (): void => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
}

/**
 * 检查并清理过期的认证信息
 */
export const checkAndClearExpiredAuth = (): boolean => {
  const token = localStorage.getItem('token')
  
  if (isTokenExpired(token)) {
    clearAuth()
    return true // 表示已清理
  }
  
  return false // 表示未清理
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    try {
      return JSON.parse(userStr)
    } catch (error) {
      console.error('解析用户信息失败:', error)
      clearAuth()
      return null
    }
  }
  return null
}

/**
 * 获取当前token
 */
export const getCurrentToken = (): string | null => {
  const token = localStorage.getItem('token')
  
  if (isTokenExpired(token)) {
    clearAuth()
    return null
  }
  
  return token
}

/**
 * 检查用户是否已认证
 */
export const isAuthenticated = (): boolean => {
  const token = getCurrentToken()
  return !!token
}

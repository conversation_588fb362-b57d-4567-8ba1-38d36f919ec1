import React from 'react'
import { Form, Input, Button, Card, Typography, Select, InputNumber, Divider, Space } from 'antd'
import { UserOutlined, LockOutlined, MailOutlined, HeartOutlined } from '@ant-design/icons'
import { Link, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { register } from '@/store/slices/authSlice'
import './Auth.css'

const { Title, Text } = Typography
const { Option } = Select

interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  nickname?: string
  age?: number
  gender?: number
}

const Register: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()
  const { loading } = useSelector((state: RootState) => state.auth)
  const [form] = Form.useForm()

  const onFinish = async (values: RegisterForm) => {
    try {
      const { confirmPassword, ...userData } = values
      await dispatch(register(userData)).unwrap()
      navigate('/login')
    } catch (error) {
      // 错误已在slice中处理
    }
  }

  return (
    <div className="auth-container">
      <div className="auth-content">
        <Card className="auth-card">
          <div className="auth-brand">
            <div className="auth-brand-icon">
              <HeartOutlined />
            </div>
            <span className="auth-brand-text">不晚心理</span>
          </div>

          <div className="auth-header">
            <Title level={2} className="auth-title">
              加入不晚心理
            </Title>
            <Text className="auth-subtitle">
              创建您的账户，开始心理健康之旅
            </Text>
          </div>

          <Form
            form={form}
            name="register"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
            className="auth-form"
            layout="vertical"
          >
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3位' },
                { max: 20, message: '用户名最多20位' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' },
                { max: 20, message: '密码最多20位' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请再次输入密码"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              name="nickname"
              label="昵称（可选）"
            >
              <Input
                placeholder="请输入昵称"
                className="auth-input"
              />
            </Form.Item>

            <Space.Compact style={{ width: '100%' }}>
              <Form.Item
                name="age"
                label="年龄（可选）"
                style={{ width: '50%', marginRight: 8 }}
              >
                <InputNumber
                  placeholder="年龄"
                  min={1}
                  max={120}
                  style={{ width: '100%' }}
                  className="auth-input"
                />
              </Form.Item>

              <Form.Item
                name="gender"
                label="性别（可选）"
                style={{ width: '50%' }}
              >
                <Select
                  placeholder="请选择性别"
                  className="auth-input"
                  allowClear
                >
                  <Option value={1}>男</Option>
                  <Option value={2}>女</Option>
                  <Option value={0}>不愿透露</Option>
                </Select>
              </Form.Item>
            </Space.Compact>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="auth-button"
                block
              >
                注册
              </Button>
            </Form.Item>
          </Form>

          <Divider className="auth-divider">
            <Text type="secondary">其他选项</Text>
          </Divider>

          <div className="auth-links">
            <div className="auth-link-item">
              <Text>已有账户？</Text>
              <Link to="/login" className="auth-link">
                立即登录
              </Link>
            </div>
          </div>

          <div className="auth-footer">
            <Text type="secondary" className="auth-footer-text">
              注册即表示您同意我们的
              <Link to="/terms" className="auth-link"> 服务条款 </Link>
              和
              <Link to="/privacy" className="auth-link"> 隐私政策</Link>
            </Text>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default Register

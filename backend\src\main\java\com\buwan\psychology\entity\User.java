package com.buwan.psychology.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名
     */
    @Column(unique = true, nullable = false, length = 50)
    private String username;

    /**
     * 邮箱
     */
    @Column(unique = true, nullable = false, length = 100)
    private String email;

    /**
     * 密码（加密后）
     */
    @Column(nullable = false)
    private String password;

    /**
     * 昵称
     */
    @Column(length = 50)
    private String nickname;

    /**
     * 头像URL
     */
    @Column(length = 500)
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    @Column(length = 20)
    private String phone;

    /**
     * 用户类型：0-普通用户，1-青少年用户，2-管理员
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer userType;

    /**
     * 账户状态：0-正常，1-禁用
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer status;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private LocalDateTime updateTime;

    /**
     * 用户简介
     */
    @Column(length = 500)
    private String bio;

    /**
     * 是否是青少年用户
     */
    public boolean isTeenager() {
        return userType != null && userType == 1;
    }

    /**
     * 是否是管理员
     */
    public boolean isAdmin() {
        return userType != null && userType == 2;
    }

    /**
     * 账户是否可用
     */
    public boolean isEnabled() {
        return status == null || status == 0;
    }
}

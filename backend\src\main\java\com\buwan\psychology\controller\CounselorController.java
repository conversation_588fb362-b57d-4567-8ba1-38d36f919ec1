package com.buwan.psychology.controller;

import com.buwan.psychology.entity.Counselor;
import com.buwan.psychology.service.CounselorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 心理咨询师控制器
 */
@RestController
@RequestMapping("/api/counselors")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "心理咨询师管理", description = "心理咨询师相关API")
public class CounselorController {

    private final CounselorService counselorService;

    /**
     * 获取所有可用咨询师
     */
    @GetMapping
    @Operation(summary = "获取所有可用咨询师", description = "获取所有可用的心理咨询师列表")
    public ResponseEntity<ApiResponse<List<Counselor>>> getAllCounselors() {
        try {
            List<Counselor> counselors = counselorService.getAllAvailableCounselors();
            return ResponseEntity.ok(ApiResponse.success("获取咨询师列表成功", counselors));
        } catch (Exception e) {
            log.error("获取咨询师列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取咨询师列表失败"));
        }
    }

    /**
     * 根据ID获取咨询师详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取咨询师详情", description = "根据ID获取心理咨询师的详细信息")
    public ResponseEntity<ApiResponse<Counselor>> getCounselorById(
            @Parameter(description = "咨询师ID") @PathVariable Long id) {
        try {
            Optional<Counselor> counselor = counselorService.getCounselorById(id);
            if (counselor.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取咨询师详情成功", counselor.get()));
            } else {
                return ResponseEntity.ok(ApiResponse.error("咨询师不存在"));
            }
        } catch (Exception e) {
            log.error("获取咨询师详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取咨询师详情失败"));
        }
    }

    /**
     * 根据专业领域获取咨询师
     */
    @GetMapping("/specialization/{specialization}")
    @Operation(summary = "按专业领域获取咨询师", description = "根据专业领域筛选心理咨询师")
    public ResponseEntity<ApiResponse<List<Counselor>>> getCounselorsBySpecialization(
            @Parameter(description = "专业领域") @PathVariable String specialization) {
        try {
            List<Counselor> counselors = counselorService.getCounselorsBySpecialization(specialization);
            return ResponseEntity.ok(ApiResponse.success("获取专业咨询师列表成功", counselors));
        } catch (Exception e) {
            log.error("获取专业咨询师列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取专业咨询师列表失败"));
        }
    }

    /**
     * 获取推荐咨询师
     */
    @GetMapping("/recommended")
    @Operation(summary = "获取推荐咨询师", description = "获取高评分、经验丰富的推荐咨询师")
    public ResponseEntity<ApiResponse<List<Counselor>>> getRecommendedCounselors() {
        try {
            List<Counselor> counselors = counselorService.getRecommendedCounselors();
            return ResponseEntity.ok(ApiResponse.success("获取推荐咨询师成功", counselors));
        } catch (Exception e) {
            log.error("获取推荐咨询师失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取推荐咨询师失败"));
        }
    }

    /**
     * 智能匹配咨询师
     */
    @GetMapping("/match")
    @Operation(summary = "智能匹配咨询师", description = "根据咨询类型智能匹配最合适的咨询师")
    public ResponseEntity<ApiResponse<Counselor>> matchCounselor(
            @Parameter(description = "咨询类型") @RequestParam Integer sessionType) {
        try {
            Counselor counselor = counselorService.matchCounselorBySessionType(sessionType);
            if (counselor != null) {
                return ResponseEntity.ok(ApiResponse.success("智能匹配咨询师成功", counselor));
            } else {
                return ResponseEntity.ok(ApiResponse.error("暂无可用咨询师"));
            }
        } catch (Exception e) {
            log.error("智能匹配咨询师失败", e);
            return ResponseEntity.ok(ApiResponse.error("智能匹配咨询师失败"));
        }
    }

    /**
     * 获取咨询师统计信息
     */
    @GetMapping("/{id}/stats")
    @Operation(summary = "获取咨询师统计信息", description = "获取咨询师的统计数据")
    public ResponseEntity<ApiResponse<CounselorService.CounselorStats>> getCounselorStats(
            @Parameter(description = "咨询师ID") @PathVariable Long id) {
        try {
            CounselorService.CounselorStats stats = counselorService.getCounselorStats(id);
            if (stats != null) {
                return ResponseEntity.ok(ApiResponse.success("获取咨询师统计信息成功", stats));
            } else {
                return ResponseEntity.ok(ApiResponse.error("咨询师不存在"));
            }
        } catch (Exception e) {
            log.error("获取咨询师统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取咨询师统计信息失败"));
        }
    }

    /**
     * API响应包装类
     */
    public static class ApiResponse<T> {
        public boolean success;
        public String message;
        public T data;

        public ApiResponse(boolean success, String message, T data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }

        public static <T> ApiResponse<T> success(String message, T data) {
            return new ApiResponse<>(true, message, data);
        }

        public static <T> ApiResponse<T> error(String message) {
            return new ApiResponse<>(false, message, null);
        }
    }
}

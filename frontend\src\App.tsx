import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'

import Header from './components/Layout/Header'
import Home from './pages/Home'
import Login from './pages/Auth/Login'
import Register from './pages/Auth/Register'
import Chat from './pages/Chat'
import Counselors from './pages/Counselors'
import Profile from './pages/Profile'
import Assessment from './pages/Assessment'
import AnxietyScale from './pages/Assessment/AnxietyScale'
import ColorPsychology from './pages/Assessment/ColorPsychology'
import Dashboard from './pages/Dashboard'
import StyleDemo from './pages/StyleDemo'
import ColorTest from './pages/ColorTest'
import ButtonTest from './pages/ButtonTest'
import HomeButtonTest from './pages/HomeButtonTest'
import AuthButtonTest from './pages/AuthButtonTest'
import AuthDebug from './pages/AuthDebug'
import TokenExpired from './pages/TokenExpired'
import ProtectedRoute from './components/Auth/ProtectedRoute'

import './App.css'

const { Content } = Layout

const App: React.FC = () => {
  return (
    <Layout className="app-layout">
      <Header />
      <Content className="app-content">
        <Routes>
          {/* 公开路由 */}
          <Route path="/" element={<Home />} />
          <Route path="/counselors" element={<Counselors />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/token-expired" element={<TokenExpired />} />
          <Route path="/style-demo" element={<StyleDemo />} />
          <Route path="/color-test" element={<ColorTest />} />
          <Route path="/button-test" element={<ButtonTest />} />
          <Route path="/home-button-test" element={<HomeButtonTest />} />
          <Route path="/auth-button-test" element={<AuthButtonTest />} />
          <Route path="/auth-debug" element={<AuthDebug />} />

          {/* 受保护的路由 */}
          <Route path="/chat" element={
            <ProtectedRoute>
              <Chat />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          } />
          <Route path="/assessment" element={
            <ProtectedRoute>
              <Assessment />
            </ProtectedRoute>
          } />
          <Route path="/assessment/anxiety-scale" element={
            <ProtectedRoute>
              <AnxietyScale />
            </ProtectedRoute>
          } />
          <Route path="/assessment/color-psychology" element={
            <ProtectedRoute>
              <ColorPsychology />
            </ProtectedRoute>
          } />

          {/* 默认重定向 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Content>
    </Layout>
  )
}

export default App

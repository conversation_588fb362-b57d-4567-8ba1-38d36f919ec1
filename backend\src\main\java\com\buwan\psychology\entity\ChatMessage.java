package com.buwan.psychology.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 聊天消息实体类
 */
@Slf4j
@Entity
@Table(name = "chat_messages")
@Data
@NoArgsConstructor
@AllArgsConstructor

public class ChatMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会话ID
     */
    @Column(nullable = false)
    private Long sessionId;

    /**
     * 用户ID
     */
    @Column(nullable = false)
    private Long userId;

    /**
     * 消息内容
     */
    @Column(nullable = false, length = 5000)
    private String content;

    /**
     * 消息类型：0-用户消息，1-AI回复，2-系统消息
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer messageType;

    /**
     * 附件JSON数据
     */
    @Column(columnDefinition = "TEXT")
    private String attachmentsJson;

    /**
     * 附件列表（不持久化）
     */
    @Transient
    private List<Attachment> attachments;

    /**
     * 情感分析结果
     */
    @Column(length = 500)
    private String emotionAnalysis;

    /**
     * 关键词提取
     */
    @Column(length = 1000)
    private String keywords;

    /**
     * AI置信度（0-100）
     */
    private Integer confidence;

    /**
     * 是否包含风险信号
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean hasRiskSignal;

    /**
     * 风险级别：0-无风险，1-低风险，2-中风险，3-高风险
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer riskLevel;

    /**
     * Token数量
     */
    private Integer tokenCount;

    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false)
    private LocalDateTime createTime;

    /**
     * 是否是用户消息
     */
    public boolean isUserMessage() {
        return messageType != null && messageType == 0;
    }

    /**
     * 是否是AI回复
     */
    public boolean isAiMessage() {
        return messageType != null && messageType == 1;
    }

    /**
     * 是否是系统消息
     */
    public boolean isSystemMessage() {
        return messageType != null && messageType == 2;
    }

    /**
     * 获取消息类型描述
     */
    public String getMessageTypeDesc() {
        if (messageType == null) return "用户消息";
        return switch (messageType) {
            case 1 -> "AI回复";
            case 2 -> "系统消息";
            default -> "用户消息";
        };
    }

    /**
     * 获取风险级别描述
     */
    public String getRiskLevelDesc() {
        if (riskLevel == null) return "无风险";
        return switch (riskLevel) {
            case 1 -> "低风险";
            case 2 -> "中风险";
            case 3 -> "高风险";
            default -> "无风险";
        };
    }

    /**
     * 是否需要人工干预
     */
    public boolean needsHumanIntervention() {
        return hasRiskSignal != null && hasRiskSignal && riskLevel != null && riskLevel >= 2;
    }

    // 附件实体类
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Attachment {
        private String id;
        private String name;
        private String type; // "image" or "file"
        private String base64;
        private Long size;
        private String preview;
    }

    /**
     * 获取附件列表
     */
    public List<Attachment> getAttachments() {
        if (attachments == null && attachmentsJson != null && !attachmentsJson.trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                attachments = mapper.readValue(attachmentsJson, new TypeReference<List<Attachment>>() {});
            } catch (JsonProcessingException e) {
                log.error("解析附件JSON失败: {}", attachmentsJson, e);
                attachments = new ArrayList<>();
            }
        }
        return attachments != null ? attachments : new ArrayList<>();
    }

    /**
     * 设置附件列表
     */
    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
        if (attachments != null && !attachments.isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                this.attachmentsJson = mapper.writeValueAsString(attachments);
            } catch (JsonProcessingException e) {
                log.error("序列化附件JSON失败", e);
                this.attachmentsJson = null;
            }
        } else {
            this.attachmentsJson = null;
        }
    }
}

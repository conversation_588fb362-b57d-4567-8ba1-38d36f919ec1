import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App'
import { store } from './store'
import './styles/variables.css'
import './index.css'

// 设置dayjs中文
dayjs.locale('zh-cn')

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <ConfigProvider
          locale={zhCN}
          theme={{
            token: {
              colorPrimary: '#6366f1',
              colorSuccess: '#10b981',
              colorWarning: '#f59e0b',
              colorError: '#ef4444',
              borderRadius: 8,
              borderRadiusLG: 12,
              borderRadiusXS: 4,
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
              fontSize: 14,
              lineHeight: 1.5,
              colorBgContainer: 'rgba(255, 255, 255, 0.95)',
              colorBgElevated: 'rgba(255, 255, 255, 0.98)',
              boxShadow: '0 4px 24px rgba(0, 0, 0, 0.08)',
              boxShadowSecondary: '0 2px 12px rgba(0, 0, 0, 0.06)',
            },
            components: {
              Button: {
                borderRadius: 8,
                controlHeight: 40,
                fontWeight: 500,
              },
              Card: {
                borderRadius: 16,
                boxShadow: '0 4px 24px rgba(0, 0, 0, 0.06)',
              },
              Input: {
                borderRadius: 8,
                controlHeight: 40,
              },
              Menu: {
                borderRadius: 8,
              },
            },
          }}
        >
          <App />
        </ConfigProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>,
)

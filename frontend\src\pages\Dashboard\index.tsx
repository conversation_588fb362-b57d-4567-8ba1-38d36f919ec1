import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Typography,
  Button,
  Avatar,
  Row,
  Col,
  Statistic,
  Progress,
  List,
  Tag,
  Space,
  Divider,
  Badge,
  Tooltip,
  message
} from 'antd'
import {
  UserOutlined,
  EditOutlined,
  MessageOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  ExperimentOutlined
} from '@ant-design/icons'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { RootState } from '@/store'
import { userStatsApi } from '@/api/user'
import TokenExpiredModal from '@/components/TokenExpiredModal'
import './index.css'

const { Title, Text } = Typography

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useSelector((state: RootState) => state.auth)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalSessions: 0,
    completedAssessments: 0,
    consultationHours: 0,
    profileCompleteness: 0,
    growthPoints: 0
  })
  const [recentSessions, setRecentSessions] = useState<any[]>([])
  const [recentAssessments, setRecentAssessments] = useState<any[]>([])
  const [showTokenExpiredModal, setShowTokenExpiredModal] = useState(false)

  useEffect(() => {
    if (user?.id) {
      loadDashboardData()
    }
  }, [user?.id])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      // 并行加载所有数据
      const [statsResult, sessionsResult, assessmentsResult] = await Promise.all([
        userStatsApi.getStats(),
        userStatsApi.getRecentSessions(3),
        userStatsApi.getRecentAssessments(3)
      ])

      if (statsResult.success) {
        setStats(statsResult.data)
      } else {
        console.error('获取统计数据失败:', statsResult.message)
        if (!statsResult.message.includes('401') && !statsResult.message.includes('403')) {
          message.error(statsResult.message)
        }
      }

      if (sessionsResult.success) {
        setRecentSessions(sessionsResult.data)
      } else {
        console.error('获取会话数据失败:', sessionsResult.message)
        if (!sessionsResult.message.includes('401') && !sessionsResult.message.includes('403')) {
          message.error(sessionsResult.message)
        }
      }

      if (assessmentsResult.success) {
        setRecentAssessments(assessmentsResult.data)
      } else {
        console.error('获取评估数据失败:', assessmentsResult.message)
        if (!assessmentsResult.message.includes('401') && !assessmentsResult.message.includes('403')) {
          message.error(assessmentsResult.message)
        }
      }
    } catch (error: any) {
      console.error('加载数据失败:', error)
      // 如果是认证错误，不显示错误消息，因为会自动跳转到登录页
      if (error.response?.status !== 401 && error.response?.status !== 403) {
        message.error('加载数据失败')
      }
    } finally {
      setLoading(false)
    }
  }



  // 快捷操作
  const quickActions = [
    {
      key: 'start-chat',
      title: '开始咨询',
      description: '与AI咨询师开始新的对话',
      icon: <MessageOutlined />,
      color: '#1890ff',
      action: () => navigate('/chat')
    },
    {
      key: 'take-assessment',
      title: '心理评估',
      description: '进行心理健康测试',
      icon: <ExperimentOutlined />,
      color: '#52c41a',
      action: () => navigate('/assessment')
    },
    {
      key: 'edit-profile',
      title: '编辑资料',
      description: '完善个人信息',
      icon: <EditOutlined />,
      color: '#fa8c16',
      action: () => navigate('/profile/edit')
    },
    {
      key: 'view-counselors',
      title: '专家团队',
      description: '查看专业咨询师',
      icon: <UserOutlined />,
      color: '#722ed1',
      action: () => navigate('/counselors')
    }
  ]

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'processing' // 进行中
      case 1: return 'success'    // 已完成
      case 2: return 'error'      // 已取消
      default: return 'default'
    }
  }

  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '进行中'
      case 1: return '已完成'
      case 2: return '已取消'
      default: return '未知'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-content">
        {/* 用户信息卡片 */}
        <Card className="user-info-card">
          <Row gutter={[24, 24]} align="middle">
            <Col xs={24} sm={8} md={6}>
              <div className="user-avatar-section">
                <Avatar
                  size={80}
                  src={user?.avatar}
                  icon={<UserOutlined />}
                  className="user-avatar"
                />
                <div className="avatar-badge">
                  <Badge status="success" text="在线" />
                </div>
              </div>
            </Col>
            <Col xs={24} sm={16} md={18}>
              <div className="user-info">
                <Title level={3} style={{ margin: 0 }}>
                  {user?.nickname || user?.username || '用户'}
                </Title>
                <Text type="secondary" style={{ fontSize: '16px' }}>
                  {user?.email}
                </Text>
                <div className="profile-progress" style={{ marginTop: '16px' }}>
                  <Text>资料完整度</Text>
                  <Progress
                    percent={stats.profileCompleteness}
                    size="small"
                    style={{ marginLeft: '12px', flex: 1 }}
                  />
                  <Button
                    type="link"
                    size="small"
                    onClick={() => navigate('/profile/edit')}
                  >
                    完善资料
                  </Button>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Row gutter={[24, 24]}>
          {/* 左侧：统计数据和快捷操作 */}
          <Col xs={24} lg={16}>
            {/* 统计数据 */}
            <Card title="数据概览" className="stats-card">
              <Row gutter={[16, 16]}>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="咨询会话"
                    value={stats.totalSessions}
                    prefix={<MessageOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="完成评估"
                    value={stats.completedAssessments}
                    prefix={<ExperimentOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="咨询时长"
                    value={stats.consultationHours}
                    suffix="小时"
                    prefix={<ClockCircleOutlined />}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="成长积分"
                    value={stats.growthPoints}
                    prefix={<TrophyOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              </Row>
            </Card>

            {/* 快捷操作 */}
            <Card title="快捷操作" className="quick-actions-card">
              <Row gutter={[16, 16]}>
                {quickActions.map(action => (
                  <Col xs={12} sm={6} key={action.key}>
                    <Card
                      hoverable
                      className="action-card"
                      onClick={action.action}
                      styles={{ body: { padding: '20px', textAlign: 'center' } }}
                    >
                      <div
                        className="action-icon"
                        style={{ color: action.color, fontSize: '32px', marginBottom: '12px' }}
                      >
                        {action.icon}
                      </div>
                      <Title level={5} style={{ margin: '0 0 8px 0' }}>
                        {action.title}
                      </Title>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {action.description}
                      </Text>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>

          {/* 右侧：最近活动 */}
          <Col xs={24} lg={8}>
            {/* 最近咨询 */}
            <Card
              title="最近咨询"
              className="recent-card"
              extra={
                <Button
                  type="link"
                  size="small"
                  onClick={() => navigate('/chat')}
                >
                  查看全部
                </Button>
              }
            >
              <List
                dataSource={recentSessions}
                loading={loading}
                renderItem={session => (
                  <List.Item className="recent-item">
                    <div className="recent-item-content">
                      <div className="recent-item-header">
                        <Text strong>{session.title}</Text>
                        <Tag color={getStatusColor(session.status)}>
                          {getStatusText(session.status)}
                        </Tag>
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {session.counselor?.name || 'AI咨询师'} • {formatDate(session.createTime)}
                      </Text>
                    </div>
                  </List.Item>
                )}
              />
            </Card>

            {/* 最近评估 */}
            <Card
              title="最近评估"
              className="recent-card"
              style={{ marginTop: '16px' }}
              extra={
                <Button
                  type="link"
                  size="small"
                  onClick={() => navigate('/assessment')}
                >
                  查看全部
                </Button>
              }
            >
              <List
                dataSource={recentAssessments}
                loading={loading}
                renderItem={assessment => (
                  <List.Item className="recent-item">
                    <div className="recent-item-content">
                      <div className="recent-item-header">
                        <Text strong>{assessment.title}</Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                          {assessment.score}分
                        </Text>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {assessment.date}
                        </Text>
                        <Tag>{assessment.level}</Tag>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>

        {/* Token过期模态框 */}
        <TokenExpiredModal
          visible={showTokenExpiredModal}
          onClose={() => setShowTokenExpiredModal(false)}
        />
      </div>
    </div>
  )
}

export default Dashboard

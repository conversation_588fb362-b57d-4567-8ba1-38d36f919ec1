import React from 'react'
import { Card, Button, Avatar, Tag, Space, Typography, Row, Col } from 'antd'
import { 
  HeartOutlined, 
  MessageOutlined, 
  UserOutlined, 
  RobotOutlined,
  StarOutlined,
  SafetyOutlined 
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography

const StyleDemo: React.FC = () => {
  return (
    <div style={{ 
      padding: '40px',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      minHeight: '100vh'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1} style={{ 
          textAlign: 'center', 
          marginBottom: '40px',
          background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          不晚心理 - 设计系统展示
        </Title>

        <Row gutter={[24, 24]}>
          {/* 按钮展示 */}
          <Col xs={24} md={12}>
            <Card title="按钮样式" style={{ height: '100%' }}>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Button type="primary" size="large" icon={<HeartOutlined />}>
                  主要按钮
                </Button>
                <Button size="large" icon={<MessageOutlined />}>
                  次要按钮
                </Button>
                <Button type="dashed" size="large" icon={<StarOutlined />}>
                  虚线按钮
                </Button>
                <Button type="text" size="large" icon={<SafetyOutlined />}>
                  文本按钮
                </Button>
              </Space>
            </Card>
          </Col>

          {/* 头像展示 */}
          <Col xs={24} md={12}>
            <Card title="头像样式" style={{ height: '100%' }}>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <Text strong>用户头像：</Text>
                  <Avatar 
                    size={64} 
                    icon={<UserOutlined />}
                    style={{
                      background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                      boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
                      border: '2px solid rgba(255, 255, 255, 0.9)',
                      marginLeft: '16px'
                    }}
                  />
                </div>
                <div>
                  <Text strong>AI头像：</Text>
                  <Avatar 
                    size={64} 
                    icon={<RobotOutlined />}
                    style={{
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                      boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
                      border: '2px solid rgba(255, 255, 255, 0.9)',
                      marginLeft: '16px'
                    }}
                  />
                </div>
              </Space>
            </Card>
          </Col>

          {/* 标签展示 */}
          <Col xs={24} md={12}>
            <Card title="标签样式" style={{ height: '100%' }}>
              <Space wrap>
                <Tag color="processing">进行中</Tag>
                <Tag color="success">已完成</Tag>
                <Tag color="warning">已暂停</Tag>
                <Tag color="error">高风险</Tag>
                <Tag color="default">一般咨询</Tag>
                <Tag color="blue">学习压力</Tag>
                <Tag color="green">人际关系</Tag>
                <Tag color="orange">焦虑问题</Tag>
                <Tag color="red">抑郁问题</Tag>
                <Tag color="purple">其他</Tag>
              </Space>
            </Card>
          </Col>

          {/* 卡片展示 */}
          <Col xs={24} md={12}>
            <Card title="卡片样式" style={{ height: '100%' }}>
              <Card 
                size="small" 
                title="嵌套卡片"
                style={{
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}
              >
                <Paragraph>
                  这是一个使用了毛玻璃效果的卡片，具有现代化的视觉效果。
                  背景使用了半透明和模糊滤镜，营造出层次感。
                </Paragraph>
              </Card>
            </Card>
          </Col>

          {/* 聊天气泡展示 */}
          <Col xs={24}>
            <Card title="聊天气泡样式">
              <div style={{ padding: '20px' }}>
                {/* 用户消息 */}
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'flex-end', 
                  marginBottom: '16px' 
                }}>
                  <div style={{
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                    color: 'white',
                    padding: '14px 18px',
                    borderRadius: '18px',
                    borderTopRightRadius: '6px',
                    maxWidth: '70%',
                    boxShadow: '0 4px 16px rgba(99, 102, 241, 0.25)',
                    fontSize: '15px',
                    fontWeight: '500'
                  }}>
                    你好，我最近感到有些焦虑，不知道该怎么办？
                  </div>
                </div>

                {/* AI回复 */}
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'flex-start', 
                  marginBottom: '16px' 
                }}>
                  <div style={{
                    background: 'rgba(255, 255, 255, 0.95)',
                    color: '#334155',
                    padding: '14px 18px',
                    borderRadius: '18px',
                    borderTopLeftRadius: '6px',
                    maxWidth: '70%',
                    boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
                    border: '1px solid rgba(226, 232, 240, 0.5)',
                    backdropFilter: 'blur(10px)',
                    fontSize: '15px'
                  }}>
                    我理解你的感受。焦虑是很常见的情绪反应，让我们一起来探讨一下可能的原因和应对方法。首先，你能告诉我是什么具体的事情让你感到焦虑吗？
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default StyleDemo

package com.buwan.psychology.service;

import com.buwan.psychology.entity.ConsultationSession;
import com.buwan.psychology.entity.ChatMessage;
import com.buwan.psychology.entity.UserProfile;
import com.buwan.psychology.repository.ConsultationSessionRepository;
import com.buwan.psychology.repository.ChatMessageRepository;
import com.buwan.psychology.repository.UserProfileRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户统计数据服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserStatsService {

    private final ConsultationSessionRepository sessionRepository;
    private final ChatMessageRepository messageRepository;
    private final UserProfileRepository userProfileRepository;

    /**
     * 获取用户统计数据
     */
    public Map<String, Object> getUserStats(Long userId) {
        log.info("获取用户统计数据，用户ID: {}", userId);

        Map<String, Object> stats = new HashMap<>();

        // 咨询会话统计
        List<ConsultationSession> userSessions = sessionRepository.findByUserIdOrderByCreateTimeDesc(userId);
        stats.put("totalSessions", userSessions.size());

        // 计算咨询时长（小时）
        double totalHours = calculateConsultationHours(userId);
        stats.put("consultationHours", totalHours);

        // 完成的评估数量（这里需要根据实际的评估系统来统计）
        int completedAssessments = getCompletedAssessmentsCount(userId);
        stats.put("completedAssessments", completedAssessments);

        // 资料完整度
        int profileCompleteness = getProfileCompleteness(userId);
        stats.put("profileCompleteness", profileCompleteness);

        // 成长积分（根据活跃度计算）
        int growthPoints = calculateGrowthPoints(userId, userSessions);
        stats.put("growthPoints", growthPoints);

        // 最近活跃时间
        LocalDateTime lastActiveTime = getLastActiveTime(userId);
        stats.put("lastActiveTime", lastActiveTime);

        log.info("用户统计数据获取完成，用户ID: {}, 会话数: {}, 咨询时长: {}小时", 
                userId, userSessions.size(), totalHours);

        return stats;
    }

    /**
     * 获取最近的咨询会话
     */
    public List<ConsultationSession> getRecentSessions(Long userId, int limit) {
        log.info("获取最近咨询会话，用户ID: {}, 限制数量: {}", userId, limit);
        
        List<ConsultationSession> sessions = sessionRepository.findByUserIdWithCounselorOrderByCreateTimeDesc(userId);
        return sessions.stream().limit(limit).toList();
    }

    /**
     * 获取最近的评估记录（模拟数据，实际需要根据评估系统实现）
     */
    public List<Map<String, Object>> getRecentAssessments(Long userId, int limit) {
        log.info("获取最近评估记录，用户ID: {}, 限制数量: {}", userId, limit);
        
        // 这里返回模拟数据，实际应该从评估记录表中查询
        return List.of(
            Map.of(
                "id", 1,
                "title", "焦虑自评量表",
                "date", "2024-01-14",
                "score", 45,
                "level", "轻度焦虑"
            ),
            Map.of(
                "id", 2,
                "title", "抑郁自评量表", 
                "date", "2024-01-10",
                "score", 38,
                "level", "正常范围"
            )
        );
    }

    /**
     * 计算咨询时长
     */
    private double calculateConsultationHours(Long userId) {
        List<ConsultationSession> sessions = sessionRepository.findByUserIdOrderByCreateTimeDesc(userId);
        
        double totalMinutes = 0;
        for (ConsultationSession session : sessions) {
            if (session.getStartTime() != null) {
                LocalDateTime endTime = session.getEndTime() != null ? 
                    session.getEndTime() : LocalDateTime.now();
                
                long minutes = ChronoUnit.MINUTES.between(session.getStartTime(), endTime);
                
                // 限制单次会话最长时间为2小时
                if (minutes > 120) {
                    minutes = 120;
                }
                
                totalMinutes += minutes;
            }
        }
        
        return Math.round(totalMinutes / 60.0 * 10.0) / 10.0; // 保留一位小数
    }

    /**
     * 获取完成的评估数量
     */
    private int getCompletedAssessmentsCount(Long userId) {
        // 这里应该查询评估记录表，暂时返回模拟数据
        // 可以根据用户的活跃度和会话数量来估算
        List<ConsultationSession> sessions = sessionRepository.findByUserIdOrderByCreateTimeDesc(userId);
        return Math.min(sessions.size() / 2, 10); // 假设每2次会话完成1次评估，最多10次
    }

    /**
     * 计算资料完整度
     */
    private int getProfileCompleteness(Long userId) {
        Optional<UserProfile> profileOpt = userProfileRepository.findByUserId(userId);
        
        if (profileOpt.isEmpty()) {
            return 20; // 基础注册信息
        }
        
        UserProfile profile = profileOpt.get();
        int completedFields = 0;
        int totalFields = 20;
        
        // 检查各个字段是否完整
        if (profile.getRealName() != null && !profile.getRealName().trim().isEmpty()) completedFields++;
        if (profile.getBirthDate() != null) completedFields++;
        if (profile.getMaritalStatus() != null) completedFields++;
        if (profile.getEducation() != null) completedFields++;
        if (profile.getOccupation() != null) completedFields++;
        if (profile.getIncome() != null) completedFields++;
        if (profile.getProvince() != null) completedFields++;
        if (profile.getEmergencyContactName() != null) completedFields++;
        if (profile.getEmergencyContactPhone() != null) completedFields++;
        if (profile.getMedicalHistory() != null) completedFields++;
        if (profile.getPsychologicalHistory() != null) completedFields++;
        if (profile.getCurrentMedications() != null) completedFields++;
        if (profile.getAllergies() != null) completedFields++;
        if (profile.getExerciseFrequency() != null) completedFields++;
        if (profile.getSleepDuration() != null) completedFields++;
        if (profile.getPersonalityTraits() != null) completedFields++;
        if (profile.getInterests() != null) completedFields++;
        if (profile.getStressFactors() != null) completedFields++;
        if (profile.getCopingMethods() != null) completedFields++;
        if (profile.getGoals() != null) completedFields++;
        
        return Math.min((int) Math.round((double) completedFields / totalFields * 100), 100);
    }

    /**
     * 计算成长积分
     */
    private int calculateGrowthPoints(Long userId, List<ConsultationSession> sessions) {
        int points = 0;
        
        // 基础积分：每次会话10分
        points += sessions.size() * 10;
        
        // 连续咨询奖励
        if (sessions.size() >= 5) points += 50;
        if (sessions.size() >= 10) points += 100;
        
        // 资料完整度奖励
        int completeness = getProfileCompleteness(userId);
        if (completeness >= 80) points += 30;
        if (completeness >= 100) points += 50;
        
        // 评估完成奖励
        int assessments = getCompletedAssessmentsCount(userId);
        points += assessments * 20;
        
        return points;
    }

    /**
     * 获取最后活跃时间
     */
    private LocalDateTime getLastActiveTime(Long userId) {
        // 查找最近的消息时间
        List<ChatMessage> recentMessages = messageRepository.findTop1ByUserIdOrderByCreateTimeDesc(userId);
        
        if (!recentMessages.isEmpty()) {
            return recentMessages.get(0).getCreateTime();
        }
        
        // 如果没有消息，查找最近的会话时间
        List<ConsultationSession> recentSessions = sessionRepository.findTop1ByUserIdOrderByCreateTimeDesc(userId);
        
        if (!recentSessions.isEmpty()) {
            return recentSessions.get(0).getCreateTime();
        }
        
        return null;
    }

    /**
     * 获取用户活跃度统计
     */
    public Map<String, Object> getUserActivityStats(Long userId) {
        Map<String, Object> activity = new HashMap<>();
        
        // 本周活跃天数
        LocalDateTime weekStart = LocalDateTime.now().minusDays(7);
        List<ConsultationSession> weekSessions = sessionRepository.findByUserIdAndCreateTimeAfter(userId, weekStart);
        activity.put("weeklyActiveDays", weekSessions.size());
        
        // 本月会话数
        LocalDateTime monthStart = LocalDateTime.now().minusDays(30);
        List<ConsultationSession> monthSessions = sessionRepository.findByUserIdAndCreateTimeAfter(userId, monthStart);
        activity.put("monthlySessions", monthSessions.size());
        
        // 平均会话时长
        double avgDuration = calculateAverageSessionDuration(userId);
        activity.put("averageSessionDuration", avgDuration);
        
        return activity;
    }

    /**
     * 计算平均会话时长
     */
    private double calculateAverageSessionDuration(Long userId) {
        List<ConsultationSession> sessions = sessionRepository.findByUserIdOrderByCreateTimeDesc(userId);
        
        if (sessions.isEmpty()) {
            return 0.0;
        }
        
        double totalMinutes = 0;
        int validSessions = 0;
        
        for (ConsultationSession session : sessions) {
            if (session.getStartTime() != null) {
                LocalDateTime endTime = session.getEndTime() != null ? 
                    session.getEndTime() : LocalDateTime.now();
                
                long minutes = ChronoUnit.MINUTES.between(session.getStartTime(), endTime);
                
                // 只统计合理时长的会话（5分钟到2小时）
                if (minutes >= 5 && minutes <= 120) {
                    totalMinutes += minutes;
                    validSessions++;
                }
            }
        }
        
        return validSessions > 0 ? Math.round(totalMinutes / validSessions * 10.0) / 10.0 : 0.0;
    }
}

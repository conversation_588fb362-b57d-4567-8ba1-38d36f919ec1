/* 聊天页面样式 */
.chat-page-container {
  height: calc(100vh - 64px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
  border-radius: 0;
  overflow: hidden;
  position: relative;
}

.chat-layout-wrapper {
  height: 100%;
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0;
  overflow: hidden;
}

/* 左侧边栏样式 */
.chat-sidebar {
  width: 320px;
  background: rgba(248, 250, 252, 0.9);
  border-right: 1px solid rgba(226, 232, 240, 0.3);
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(15px);
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.title-icon {
  color: #6366f1;
  font-size: 20px;
}

.new-session-btn {
  width: 100%;
  height: 44px;
  border-radius: 12px;
  font-weight: 600;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.new-session-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.35);
}

/* 会话列表内容 */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.session-list {
  padding: 0 8px;
}

.session-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 4px 0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid transparent;
}

.session-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.session-item.active {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.15);
}

.session-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
}

.session-type {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.session-time {
  color: #94a3b8;
}

.session-status {
  flex-shrink: 0;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.empty-sessions {
  padding: 60px 20px;
  text-align: center;
}

/* 会话列表样式 */
.session-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 12px;
  margin: 4px 8px;
  border-bottom: none;
}

.session-item:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%) !important;
  transform: translateX(4px);
}

.session-item.active {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.12) 100%) !important;
  border-left: 4px solid #6366f1 !important;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.15);
}

.session-item.new-session {
  animation: newSessionPulse 2s ease-in-out;
}

@keyframes newSessionPulse {
  0% {
    background: #f6ffed;
    transform: scale(1);
  }
  50% {
    background: #d9f7be;
    transform: scale(1.02);
  }
  100% {
    background: #f6ffed;
    transform: scale(1);
  }
}

/* 聊天内容区域 */
.chat-page-content {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 欢迎页面样式 */
.chat-welcome-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  padding: 40px;
}

.chat-welcome-card {
  max-width: 600px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.chat-welcome-content {
  text-align: center;
  padding: 20px;
}

.chat-welcome-icon {
  font-size: 72px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
}

.chat-welcome-title {
  color: #1e293b;
  margin-bottom: 16px;
  font-weight: 600;
}

.chat-welcome-description {
  color: #475569;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 32px;
}

.chat-welcome-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.chat-feature-item {
  display: flex;
  align-items: center;
  text-align: left;
  padding: 16px;
  background: #f8f9ff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.chat-feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chat-feature-icon {
  font-size: 24px;
  margin-right: 12px;
}

.chat-feature-text {
  flex: 1;
}

.chat-feature-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.chat-feature-desc {
  font-size: 12px;
  color: #64748b;
}

.chat-start-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-page-layout .ant-layout-sider {
    position: fixed;
    left: 0;
    top: 64px;
    bottom: 70px;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .chat-page-layout .ant-layout-sider.mobile-open {
    transform: translateX(0);
  }

  .chat-welcome-features {
    grid-template-columns: 1fr;
  }

  .chat-feature-item {
    flex-direction: column;
    text-align: center;
  }

  .chat-feature-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .chat-welcome-card {
    margin: 16px;
  }

  .chat-welcome-content {
    padding: 16px;
  }

  .chat-welcome-icon {
    font-size: 48px;
  }

  .chat-welcome-title {
    font-size: 24px;
  }

  .chat-welcome-description {
    font-size: 14px;
  }
}

/* 右侧聊天区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.chat-window {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  background: rgba(248, 250, 252, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header-actions {
  display: flex;
  gap: 8px;
}

.chat-header-actions .ant-btn {
  color: #64748b;
  transition: all 0.2s ease;
}

.chat-header-actions .ant-btn:hover {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.chat-session-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.session-avatar-large {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.chat-session-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.chat-session-type {
  font-size: 14px;
  color: #64748b;
}

.chat-messages {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.3) 0%, rgba(241, 245, 249, 0.2) 100%);
  position: relative;
}

/* 消息样式 */
.message, .welcome-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  max-width: 80%;
}

.user-message {
  flex-direction: row-reverse;
  margin-left: auto;
  margin-right: 0;
}

.ai-message, .welcome-message {
  margin-right: auto;
  margin-left: 0;
}

.ai-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 100%;
}

.ai-message .message-text, .welcome-message .message-text {
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px 20px;
  border-radius: 18px 18px 18px 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
  color: #1e293b;
  line-height: 1.5;
  font-size: 15px;
  word-wrap: break-word;
  white-space: pre-wrap;
  border: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
  transition: all 0.2s ease;
}

.user-message .message-text {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  padding: 16px 20px;
  border-radius: 18px 18px 6px 18px;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.25), 0 1px 4px rgba(79, 70, 229, 0.1);
  color: white;
  line-height: 1.5;
  font-size: 15px;
  word-wrap: break-word;
  white-space: pre-wrap;
  position: relative;
  transition: all 0.2s ease;
}

/* 消息悬停效果 */
.ai-message .message-text:hover, .welcome-message .message-text:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.06);
}

.user-message .message-text:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.35), 0 2px 6px rgba(79, 70, 229, 0.15);
}

/* 消息入场动画 */
.message {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表情在消息中的显示 */
.message-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

.message-text .emoji {
  font-size: 1.2em;
  vertical-align: middle;
}

.message-time {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
  color: rgba(255, 255, 255, 0.7);
}

.chat-input {
  padding: 16px 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(15px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.message-input {
  flex: 1;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 8px 0 !important;
  font-size: 15px !important;
  line-height: 1.5 !important;
  resize: none !important;
  min-height: 20px !important;
}

.message-input:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.message-input::placeholder {
  color: #94a3b8;
  font-size: 15px;
}

.send-btn {
  height: 36px !important;
  padding: 0 16px !important;
  border-radius: 10px !important;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2) !important;
  flex-shrink: 0 !important;
}

.send-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3) !important;
  background: linear-gradient(135deg, #5b5bf6 0%, #7c3aed 100%) !important;
}

.send-btn:active {
  transform: translateY(0) !important;
}

.send-btn:disabled {
  background: #e2e8f0 !important;
  color: #94a3b8 !important;
  transform: none !important;
  box-shadow: none !important;
  cursor: not-allowed !important;
}

.input-tip {
  margin-top: 12px;
  font-size: 12px;
  color: #94a3b8;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.input-tip::before,
.input-tip::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(148, 163, 184, 0.3) 50%, transparent 100%);
}

/* 粘贴提示样式 */
.paste-hint {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
  animation: fadeInUp 0.3s ease-out;
  white-space: nowrap;
  z-index: 1000;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.input-tip {
  position: relative;
}



/* 流式消息样式 */
.message.ai-message .typing {
  position: relative;
}

.message.ai-message .typing::after {
  content: '▋';
  color: #6366f1;
  animation: blink 1s infinite;
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 流式消息的特殊样式 */
.message.ai-message .message-text .typing .markdown-renderer {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 流式状态下的输入提示样式 */
.input-tip {
  transition: all 0.3s ease;
}

.input-tip:has-text("AI正在思考") {
  color: #6366f1;
  font-weight: 500;
}

/* 拖拽样式 */
.chat-main.drag-over {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border: 2px dashed #6366f1;
  border-radius: 12px;
  position: relative;
}

.chat-main.drag-over::before {
  content: '📎 拖拽文件到此处上传';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(99, 102, 241, 0.9);
  color: white;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
  z-index: 1000;
  pointer-events: none;
}

/* 附件预览样式 */
.attachments-preview {
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.2);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.4) 100%);
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  backdrop-filter: blur(10px);
}

.attachment-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.attachment-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.attachment-image {
  position: relative;
  width: 80px;
  height: 80px;
}

.attachment-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.attachment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.attachment-image:hover .attachment-overlay {
  opacity: 1;
}

.attachment-name {
  color: white;
  font-size: 10px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.remove-attachment-btn {
  align-self: flex-end;
  color: white !important;
  padding: 0 !important;
  width: 20px !important;
  height: 20px !important;
  min-width: 20px !important;
}

.attachment-file {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
  min-width: 200px;
  max-width: 250px;
}

.file-icon {
  font-size: 24px;
  color: #6366f1;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 12px;
  font-weight: 500;
  color: #1e293b;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.file-size {
  font-size: 10px;
  color: #64748b;
}

/* 输入区域样式更新 */
.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 8px 12px;
  border: 2px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.input-wrapper:hover {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.08);
}

.input-wrapper:focus-within {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 20px rgba(99, 102, 241, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.upload-btn {
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
  border-radius: 10px !important;
  color: #64748b !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
}

.upload-btn:hover {
  background: rgba(99, 102, 241, 0.1) !important;
  color: #6366f1 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15) !important;
}

.upload-btn:active {
  transform: translateY(0) !important;
}

.message-input {
  flex: 1;
}

/* 消息附件样式 */
.message-attachments {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-attachment {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.user-message .message-attachment {
  background: rgba(255, 255, 255, 0.2);
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.message-image:hover {
  transform: scale(1.02);
}

.message-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.message-file {
  display: flex;
  align-items: center;
  padding: 12px;
  gap: 12px;
  min-width: 180px;
  max-width: 250px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}

.user-message .message-file {
  background: rgba(255, 255, 255, 0.3);
}

.message-file-icon {
  font-size: 24px;
  color: #6366f1;
  flex-shrink: 0;
}

.user-message .message-file-icon {
  color: rgba(255, 255, 255, 0.9);
}

.message-file-info {
  flex: 1;
  min-width: 0;
}

.message-file-name {
  font-size: 13px;
  font-weight: 500;
  color: #1e293b;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-bottom: 2px;
}

.user-message .message-file-name {
  color: rgba(255, 255, 255, 0.95);
}

.message-file-size {
  font-size: 11px;
  color: #64748b;
}

.user-message .message-file-size {
  color: rgba(255, 255, 255, 0.7);
}

/* 表情选择器样式 */
.emoji-picker {
  width: 320px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.emoji-category {
  margin-bottom: 16px;
}

.emoji-category-title {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 8px;
  padding: 0 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
}

.emoji-item {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
}

.emoji-item:hover {
  background: rgba(99, 102, 241, 0.1);
  transform: scale(1.2);
}

.emoji-item:active {
  transform: scale(1.1);
}

/* 表情按钮特殊样式 */
.emoji-btn {
  color: #f59e0b !important;
}

.emoji-btn:hover {
  color: #d97706 !important;
  background: rgba(245, 158, 11, 0.1) !important;
}

/* 表情选择器弹窗样式 */
.emoji-popover .ant-popover-content {
  padding: 0;
}

.emoji-popover .ant-popover-inner {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(226, 232, 240, 0.3);
}

.emoji-popover .ant-popover-title {
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  color: #1e293b;
}

/* 表情选择器滚动条 */
.emoji-picker::-webkit-scrollbar {
  width: 6px;
}

.emoji-picker::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 3px;
}

.emoji-picker::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.emoji-picker::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.8);
}

/* 欢迎屏幕 */
.welcome-screen {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.3) 0%, rgba(241, 245, 249, 0.2) 100%);
}

.welcome-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
}

.welcome-icon {
  font-size: 80px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-desc {
  font-size: 16px;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 32px;
}

.start-chat-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 24px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.start-chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(99, 102, 241, 0.35);
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.8);
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.session-item {
  animation: slideInLeft 0.3s ease-out;
}

.chat-welcome-content > * {
  animation: slideInRight 0.6s ease-out;
}

/* 新建会话弹窗样式 */
.new-session-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.new-session-modal .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
}

.new-session-modal .ant-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.new-session-modal .ant-modal-body {
  padding: 24px;
}

/* Select下拉选项样式优化 */
.session-type-select .ant-select-dropdown {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e2e8f0;
}

.session-type-select .ant-select-item {
  padding: 0 !important;
  border-radius: 8px;
  margin: 4px 8px;
  transition: all 0.2s ease;
}

.session-type-select .ant-select-item:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%) !important;
}

.session-type-select .ant-select-item-option-selected {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.12) 100%) !important;
  border-left: 3px solid #6366f1;
}

.session-type-option {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.session-type-option:last-child {
  border-bottom: none;
}

.session-type-option:hover {
  background: rgba(99, 102, 241, 0.05);
}

.session-type-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: #262626;
  font-size: 14px;
}

.session-type-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 表单样式优化 */
.new-session-modal .ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

.new-session-modal .ant-input {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.new-session-modal .ant-input:focus,
.new-session-modal .ant-input-focused {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.new-session-modal .ant-select {
  border-radius: 8px;
}

.new-session-modal .ant-select .ant-select-selector {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.new-session-modal .ant-select-focused .ant-select-selector {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* 温馨提示样式 */
.session-tip {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border: 1px solid #b7eb8f;
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.session-tip-content {
  font-size: 13px;
  color: #374151;
  line-height: 1.6;
}

.session-tip-content strong {
  color: #059669;
}

/* 咨询师欢迎消息样式 */
.welcome-message {
  margin-bottom: 24px;
}

.counselor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid #fff;
}

.counselor-avatar .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.counselor-intro {
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 24px;
  border-radius: 20px 20px 20px 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
}

.counselor-name {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.counselor-credentials {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.credential {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.counselor-specialties {
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 12px;
  border-left: 4px solid #6366f1;
}

.counselor-specialties p {
  margin: 0;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
}

.counselor-specialties strong {
  color: #6366f1;
}

.welcome-text {
  color: #1e293b;
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}

/* 聊天头部咨询师头像 */
.counselor-header-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* 清除聊天记录按钮 */
.clear-chat-button {
  width: 44px !important;
  height: 44px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  background: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.clear-chat-button:hover {
  background: rgba(239, 68, 68, 0.15) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  transform: scale(1.05) !important;
}

.clear-chat-button .anticon {
  font-size: 18px !important;
  color: #ef4444 !important;
}

.clear-chat-button:disabled {
  background: rgba(156, 163, 175, 0.1) !important;
  border-color: rgba(156, 163, 175, 0.2) !important;
}

.clear-chat-button:disabled .anticon {
  color: #9ca3af !important;
}

/* 消息分隔线样式 */
.messages-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  opacity: 0.6;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.divider-text {
  padding: 0 16px;
  font-size: 12px;
  color: #64748b;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  white-space: nowrap;
}

/* 发送中动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

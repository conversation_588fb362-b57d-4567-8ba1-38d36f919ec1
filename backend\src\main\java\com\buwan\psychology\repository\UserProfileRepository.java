package com.buwan.psychology.repository;

import com.buwan.psychology.entity.UserProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户详细资料Repository
 */
@Repository
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {

    /**
     * 根据用户ID查找用户资料
     */
    Optional<UserProfile> findByUserId(Long userId);

    /**
     * 根据用户ID删除用户资料
     */
    void deleteByUserId(Long userId);

    /**
     * 检查用户是否已有资料
     */
    boolean existsByUserId(Long userId);

    /**
     * 查找用户资料并关联用户信息
     */
    @Query("SELECT up FROM UserProfile up LEFT JOIN FETCH up.user WHERE up.userId = :userId")
    Optional<UserProfile> findByUserIdWithUser(@Param("userId") Long userId);
}

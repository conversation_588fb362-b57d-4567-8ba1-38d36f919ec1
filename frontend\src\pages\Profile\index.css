/* 全新现代化个人资料页面 */
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 英雄区域 */
.profile-hero {
  position: relative;
  z-index: 1;
  padding: 60px 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 个人资料卡片 */
.profile-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 100%);
  backdrop-filter: blur(30px);
  border-radius: 28px;
  padding: 48px;
  box-shadow:
    0 24px 80px rgba(0, 0, 0, 0.15),
    0 12px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  gap: 48px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.8) 100%);
  opacity: 0.8;
}

.profile-card:hover {
  transform: translateY(-6px);
  box-shadow:
    0 40px 120px rgba(0, 0, 0, 0.2),
    0 20px 60px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* 头像区域 */
.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.main-avatar {
  border: 5px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(102, 126, 234, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
}

.main-avatar:hover {
  transform: scale(1.05);
  box-shadow:
    0 16px 50px rgba(0, 0, 0, 0.2),
    0 8px 24px rgba(102, 126, 234, 0.3);
}

.avatar-ring {
  position: absolute;
  top: -12px;
  left: -12px;
  right: -12px;
  bottom: -12px;
  border: 3px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
  background-size: 200% 200%;
  animation: pulse 3s infinite, gradient-rotate 4s linear infinite;
  z-index: 1;
}

.avatar-ring::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  bottom: 3px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.08);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes gradient-rotate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 个人详情 */
.profile-details {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-size: 36px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  margin: 0 0 8px 0;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.profile-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 20px 0;
  line-height: 1.4;
  font-weight: 400;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  padding: 10px 16px;
  border-radius: 14px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-item .anticon {
  color: rgba(255, 255, 255, 0.9);
  font-size: 18px;
  transition: all 0.3s ease;
}

.contact-item:hover .anticon {
  transform: scale(1.1);
  color: white;
}

/* 操作按钮 */
.profile-actions {
  flex-shrink: 0;
}

.edit-actions {
  display: flex;
  gap: 16px;
}

.primary-action {
  height: 52px !important;
  padding: 0 36px !important;
  border-radius: 26px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow:
    0 6px 20px rgba(102, 126, 234, 0.35) !important,
    0 2px 8px rgba(0, 0, 0, 0.1) !important,
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.primary-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.primary-action:hover::before {
  left: 100%;
}

.primary-action:hover {
  transform: translateY(-3px) !important;
  box-shadow:
    0 12px 32px rgba(102, 126, 234, 0.45) !important,
    0 6px 16px rgba(0, 0, 0, 0.15) !important,
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.secondary-action {
  height: 52px !important;
  padding: 0 28px !important;
  border-radius: 26px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  border: 2px solid rgba(102, 126, 234, 0.25) !important;
  color: #667eea !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

.secondary-action:hover {
  background: rgba(102, 126, 234, 0.12) !important;
  border-color: rgba(102, 126, 234, 0.5) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
}

/* 表单区域 */
.form-section {
  position: relative;
  z-index: 1;
  padding: 0 0 60px 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.02) 100%);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.profile-form {
  background: transparent;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  align-items: start;
}

.form-grid .form-card:nth-child(1) {
  animation: slideInLeft 0.8s ease-out 0.6s both;
}

.form-grid .form-card:nth-child(2) {
  animation: slideInLeft 0.8s ease-out 0.8s both;
}

.form-grid .form-card:nth-child(3) {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.form-card.full-width {
  grid-column: 1 / -1;
}

/* 表单卡片 */
.form-card {
  background: rgba(255, 255, 255, 0.97);
  backdrop-filter: blur(25px);
  border-radius: 24px;
  overflow: hidden;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 24px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  z-index: 1;
}

.form-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 32px 80px rgba(0, 0, 0, 0.15),
    0 16px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 28px 36px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 2;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

.header-icon {
  width: 52px;
  height: 52px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.header-icon:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.3);
}

.header-content h3 {
  color: white;
  font-size: 22px;
  font-weight: 700;
  margin: 0 0 6px 0;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-content p {
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  margin: 0;
  line-height: 1.3;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-body {
  padding: 32px;
}

/* 表单行布局 */
.form-row {
  margin-bottom: 24px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-row-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

/* 表单项样式 */
.profile-form .ant-form-item {
  margin-bottom: 0;
}

.profile-form .ant-form-item-label > label {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 15px;
  margin-bottom: 8px;
}

.profile-form .ant-form-item-explain-error {
  font-size: 13px;
  margin-top: 6px;
}

/* 输入框样式 */
.form-input {
  border-radius: 14px !important;
  border: 2px solid rgba(232, 232, 232, 0.8) !important;
  padding: 14px 18px !important;
  font-size: 15px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02) !important;
}

.form-input:hover {
  border-color: rgba(102, 126, 234, 0.6) !important;
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04) !important;
  transform: translateY(-1px) !important;
}

.form-input:focus {
  border-color: #667eea !important;
  box-shadow:
    0 0 0 4px rgba(102, 126, 234, 0.12) !important,
    0 6px 16px rgba(0, 0, 0, 0.06) !important;
  background: white !important;
  transform: translateY(-1px) !important;
}

.form-input.ant-input-disabled {
  background: rgba(248, 248, 248, 0.9) !important;
  border-color: rgba(232, 232, 232, 0.6) !important;
  color: #999 !important;
  transform: none !important;
}

/* 文本域样式 */
.form-textarea {
  border-radius: 12px !important;
  border: 2px solid #e8e8e8 !important;
  padding: 16px !important;
  font-size: 15px !important;
  line-height: 1.6 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  resize: none !important;
}

.form-textarea:hover {
  border-color: #667eea !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.form-textarea:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: white !important;
}

/* 单选按钮组样式 */
.radio-group {
  display: flex !important;
  gap: 8px !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
}

.radio-group .ant-radio-button-wrapper {
  border-radius: 20px !important;
  border: 2px solid rgba(232, 232, 232, 0.8) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
  padding: 10px 18px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02) !important;
  flex: 1 !important;
  text-align: center !important;
  min-width: 0 !important;
  white-space: nowrap !important;
}

/* 修复Ant Design单选按钮组的默认样式 */
.radio-group .ant-radio-button-wrapper {
  border-left-width: 2px !important;
  border-right-width: 2px !important;
}

.radio-group .ant-radio-button-wrapper:not(:first-child)::before {
  display: none !important;
}

.radio-group .ant-radio-button-wrapper:hover {
  border-color: rgba(102, 126, 234, 0.6) !important;
  background: rgba(102, 126, 234, 0.08) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04) !important;
  z-index: 2 !important;
}

.radio-group .ant-radio-button-wrapper-checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
  color: white !important;
  box-shadow:
    0 4px 16px rgba(102, 126, 234, 0.3) !important,
    0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
  z-index: 3 !important;
}

.radio-group .ant-radio-button-wrapper-checked:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
  box-shadow:
    0 6px 20px rgba(102, 126, 234, 0.4) !important,
    0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 输入框图标样式 */
.input-icon {
  color: #667eea !important;
  font-size: 16px !important;
}

/* 日期选择器样式 */
.form-input.ant-picker {
  width: 100% !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-hero {
    padding: 40px 0;
  }

  .profile-card {
    flex-direction: column;
    text-align: center;
    gap: 32px;
    padding: 32px 24px;
  }

  .profile-name {
    font-size: 28px;
  }

  .profile-subtitle {
    font-size: 16px;
  }

  .contact-info {
    align-items: center;
  }

  .edit-actions {
    flex-direction: column;
    width: 100%;
  }

  .primary-action,
  .secondary-action {
    width: 100% !important;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .form-row-split {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-header {
    padding: 20px 24px;
  }

  .card-body {
    padding: 24px;
  }

  .hero-content,
  .section-container {
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .profile-card {
    padding: 24px 16px;
  }

  .profile-name {
    font-size: 24px;
  }
}

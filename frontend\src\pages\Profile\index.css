/* 现代化个人资料页面样式 */
.modern-profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.profile-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 顶部横幅样式 */
.profile-banner {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 40px 0;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 24px;
}

.user-info-section {
  display: flex;
  align-items: center;
  gap: 24px;
}

.user-avatar-large {
  border: 4px solid #fff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-name {
  margin: 0 !important;
  color: #1a1a1a !important;
  font-weight: 600 !important;
  font-size: 28px !important;
}

.user-subtitle {
  color: #666 !important;
  font-size: 16px !important;
  font-weight: 500;
}

.action-buttons .edit-btn,
.action-buttons .save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  height: 44px !important;
  padding: 0 24px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s ease !important;
}

.action-buttons .edit-btn:hover,
.action-buttons .save-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.action-buttons .cancel-btn {
  border-radius: 12px !important;
  height: 44px !important;
  padding: 0 24px !important;
  font-weight: 600 !important;
  border: 2px solid #d9d9d9 !important;
  background: #fff !important;
  color: #666 !important;
  transition: all 0.3s ease !important;
}

.action-buttons .cancel-btn:hover {
  border-color: #667eea !important;
  color: #667eea !important;
  background: rgba(102, 126, 234, 0.05) !important;
}

/* 表单内容区域 */
.form-content {
  padding: 0 0 40px 0;
}

.modern-form {
  background: transparent;
}

/* 信息卡片样式 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 0;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.2);
}

/* 主要卡片统一高度 */
.main-card {
  min-height: 520px;
}

.main-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.card-header {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  padding: 22px 28px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  gap: 14px;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 28px;
  right: 28px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
}

.card-icon {
  color: #667eea;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: 0.5px;
}

.card-content {
  padding: 24px 28px 28px 28px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 确保所有卡片内容区域高度一致 */
.main-card .card-content {
  min-height: 420px;
}

/* 头像卡片 */
.avatar-card {
  text-align: center;
  position: sticky;
  top: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-avatar {
  margin-bottom: 16px;
  border: 4px solid #f0f0f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-upload {
  margin-bottom: 16px;
}

.upload-button {
  border-radius: 6px !important;
  border-style: dashed !important;
}

.user-basic-info {
  width: 100%;
}

.user-basic-info .ant-typography-title {
  color: #1a1a1a !important;
  font-size: 18px !important;
}

/* 表单样式优化 */
.modern-form .ant-form-item-label > label {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 14px;
  margin-bottom: 6px;
}

.modern-form .ant-form-item {
  margin-bottom: 20px;
}

.modern-form .ant-form-item-extra {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

/* 主卡片内的表单项间距调整 */
.main-card .ant-form-item {
  margin-bottom: 18px;
}

.main-card .ant-form-item:last-child {
  margin-bottom: 0;
  flex: 1;
}

/* 文本域在主卡片中的特殊处理 */
.main-card .ant-form-item:has(textarea) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-card .ant-form-item:has(textarea) .ant-form-item-control {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-card .ant-form-item:has(textarea) textarea {
  flex: 1;
  min-height: 120px !important;
}

.modern-form .ant-input,
.modern-form .ant-select-selector,
.modern-form .ant-picker {
  border-radius: 12px !important;
  border: 2px solid #e8ecf0 !important;
  transition: all 0.3s ease !important;
  font-size: 16px !important;
  padding: 12px 16px !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.modern-form .ant-input-lg,
.modern-form .ant-select-lg .ant-select-selector,
.modern-form .ant-picker-large {
  height: 48px !important;
  padding: 12px 16px !important;
}

.modern-form .ant-input:hover,
.modern-form .ant-select-selector:hover,
.modern-form .ant-picker:hover {
  border-color: #667eea !important;
  background: rgba(255, 255, 255, 1) !important;
}

.modern-form .ant-input:focus,
.modern-form .ant-select-focused .ant-select-selector,
.modern-form .ant-picker-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
}

.modern-form .ant-input[disabled],
.modern-form .ant-select-disabled .ant-select-selector,
.modern-form .ant-picker-disabled {
  background: rgba(248, 249, 250, 0.8) !important;
  border-color: #e8ecf0 !important;
  color: #8c8c8c !important;
}

/* 单选按钮组样式 */
.modern-form .ant-radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.modern-form .ant-radio-wrapper {
  margin-right: 0 !important;
  font-weight: 500;
  font-size: 16px;
  color: #1a1a1a;
}

.modern-form .ant-radio-wrapper .ant-radio {
  margin-right: 8px;
}

.modern-form .ant-radio-wrapper .ant-radio-checked .ant-radio-inner {
  border-color: #667eea !important;
  background-color: #667eea !important;
}

.modern-form .ant-radio-wrapper:hover .ant-radio-inner {
  border-color: #667eea !important;
}

/* 复选框 */
.ant-checkbox-wrapper {
  font-weight: 500;
}

/* 标签选择器 */
.ant-select-selection-item {
  background: #f0f8ff !important;
  border: 1px solid #1890ff !important;
  color: #1890ff !important;
  border-radius: 4px !important;
}

/* 文本域 */
.ant-input {
  resize: vertical;
}

/* 隐私提示 */
.privacy-notice {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

.ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .profile-wrapper {
    padding: 0 16px;
  }
}

/* 平板设备适配 */
@media (max-width: 1200px) and (min-width: 769px) {
  .main-card {
    min-height: 480px;
  }
}

@media (max-width: 768px) {
  .profile-wrapper {
    padding: 0 16px;
  }

  .profile-banner {
    padding: 28px 0;
    border-radius: 0 0 20px 20px;
    margin-bottom: 24px;
  }

  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .user-info-section {
    flex-direction: column;
    text-align: center;
    gap: 18px;
  }

  .user-avatar-large {
    width: 90px !important;
    height: 90px !important;
  }

  .user-name {
    font-size: 26px !important;
    line-height: 1.3 !important;
  }

  .user-subtitle {
    font-size: 15px !important;
    line-height: 1.4 !important;
    max-width: 280px;
    margin: 0 auto;
  }

  .card-header {
    padding: 20px 24px;
  }

  .card-header::after {
    left: 24px;
    right: 24px;
  }

  .card-content {
    padding: 24px;
  }

  .card-title {
    font-size: 17px;
  }

  .modern-form .ant-radio-group {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .action-buttons {
    width: 100%;
  }

  .action-buttons .ant-space {
    width: 100%;
    justify-content: center;
  }

  .action-buttons .edit-btn,
  .action-buttons .save-btn,
  .action-buttons .cancel-btn {
    min-width: 120px;
  }

  .modern-form .ant-form-item {
    margin-bottom: 20px;
  }

  /* 移动端卡片高度自适应 */
  .main-card {
    min-height: auto;
    height: auto;
  }

  .main-card .ant-form-item:has(textarea) textarea {
    min-height: 100px !important;
  }
}

@media (max-width: 576px) {
  .profile-container {
    padding: 8px;
  }
  
  .profile-header .ant-typography-title {
    font-size: 20px !important;
  }
  
  .profile-card .ant-card-body {
    padding: 12px;
  }
  
  .profile-card .ant-card-head {
    padding: 8px 12px;
  }
  
  .profile-card .ant-card-head-title {
    font-size: 14px;
  }
  
  .user-avatar {
    width: 80px !important;
    height: 80px !important;
  }
  
  .user-basic-info .ant-typography-title {
    font-size: 16px !important;
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-form-item-label > label {
    font-size: 14px;
  }
}

/* 动画效果 */
.profile-banner {
  animation: slideDown 0.6s ease-out;
}

.info-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.info-card:nth-child(1) { animation-delay: 0.1s; }
.info-card:nth-child(2) { animation-delay: 0.2s; }
.info-card:nth-child(3) { animation-delay: 0.3s; }
.info-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
.ant-spin-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin: 40px auto;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ant-spin-container .ant-spin-text {
  color: #667eea;
  font-weight: 500;
  font-size: 16px;
  margin-top: 16px;
}

.ant-spin-container .ant-spin-dot {
  color: #667eea !important;
}

/* 成功状态样式 */
.modern-form .ant-form-item-has-success .ant-input,
.modern-form .ant-form-item-has-success .ant-select-selector,
.modern-form .ant-form-item-has-success .ant-picker {
  border-color: #52c41a !important;
  box-shadow: 0 0 0 3px rgba(82, 196, 26, 0.1) !important;
}

/* 禁用状态的优化 */
.modern-form .ant-radio-wrapper-disabled {
  color: #bfbfbf !important;
}

.modern-form .ant-radio-disabled .ant-radio-inner {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

/* 文本域样式 */
.modern-form .ant-input {
  resize: none;
  min-height: 48px;
  line-height: 1.6;
}

.modern-form textarea.ant-input {
  resize: none;
  line-height: 1.6;
  padding: 12px 16px !important;
}

/* 前缀图标样式 */
.modern-form .ant-input-prefix {
  color: #667eea;
  margin-right: 12px;
  font-size: 16px;
}

/* 字符计数样式 */
.modern-form .ant-input-show-count-suffix {
  color: #8c8c8c;
  font-size: 12px;
}

.modern-form .ant-input-data-count {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
  text-align: right;
}

/* 错误状态样式 */
.modern-form .ant-form-item-has-error .ant-input,
.modern-form .ant-form-item-has-error .ant-select-selector,
.modern-form .ant-form-item-has-error .ant-picker {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.1) !important;
}

.modern-form .ant-form-item-explain-error {
  color: #ff4d4f;
  font-size: 13px;
  margin-top: 6px;
  line-height: 1.4;
}

/* 表单验证样式 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-select-selector,
.ant-form-item-has-error .ant-picker {
  border-color: #ff4d4f !important;
}

.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-select-focused .ant-select-selector,
.ant-form-item-has-error .ant-picker-focused {
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

/* 成功状态 */
.ant-form-item-has-success .ant-input,
.ant-form-item-has-success .ant-select-selector,
.ant-form-item-has-success .ant-picker {
  border-color: #52c41a !important;
}

/* 加载状态 */
.ant-spin-container {
  min-height: 200px;
}

/* 卡片标题图标 */
.ant-card-head-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

/* 输入框前缀图标 */
.ant-input-prefix {
  color: #1890ff;
}

/* 选择器样式优化 */
.ant-select-multiple .ant-select-selection-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 日期选择器 */
.ant-picker {
  width: 100%;
}

/* 上传组件 */
.ant-upload {
  width: 100%;
}

.ant-upload-list {
  margin-top: 8px;
}

/* 禁用表单样式 */
.form-disabled .ant-input,
.form-disabled .ant-select-selector,
.form-disabled .ant-picker,
.form-disabled .ant-radio-wrapper,
.form-disabled .ant-checkbox-wrapper {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.form-disabled .ant-input:hover,
.form-disabled .ant-select-selector:hover,
.form-disabled .ant-picker:hover {
  border-color: #d9d9d9 !important;
}

.form-disabled .ant-radio-wrapper,
.form-disabled .ant-checkbox-wrapper {
  color: #999 !important;
}

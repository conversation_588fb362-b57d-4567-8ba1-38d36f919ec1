/* 全新现代化个人资料页面 */
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}

.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 英雄区域 */
.profile-hero {
  position: relative;
  z-index: 1;
  padding: 60px 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 个人资料卡片 */
.profile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 48px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.15),
    0 16px 48px rgba(0, 0, 0, 0.1);
}

/* 头像区域 */
.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.main-avatar {
  border: 4px solid rgba(102, 126, 234, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.avatar-ring {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 个人详情 */
.profile-details {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.profile-subtitle {
  font-size: 18px;
  color: #666;
  margin: 0 0 24px 0;
  line-height: 1.4;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #555;
}

.contact-item .anticon {
  color: #667eea;
  font-size: 18px;
}

/* 操作按钮 */
.profile-actions {
  flex-shrink: 0;
}

.edit-actions {
  display: flex;
  gap: 16px;
}

.primary-action {
  height: 48px !important;
  padding: 0 32px !important;
  border-radius: 24px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.primary-action:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

.secondary-action {
  height: 48px !important;
  padding: 0 24px !important;
  border-radius: 24px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border: 2px solid rgba(102, 126, 234, 0.2) !important;
  color: #667eea !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.secondary-action:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  border-color: rgba(102, 126, 234, 0.4) !important;
  transform: translateY(-1px) !important;
}

/* 表单区域 */
.form-section {
  position: relative;
  z-index: 1;
  padding: 0 0 60px 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.02) 100%);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.profile-form {
  background: transparent;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  align-items: start;
}

.form-card.full-width {
  grid-column: 1 / -1;
}

/* 表单卡片 */
.form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 24px 64px rgba(0, 0, 0, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.08);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px 32px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.header-content h3 {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.header-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

.card-body {
  padding: 32px;
}

/* 表单行布局 */
.form-row {
  margin-bottom: 24px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-row-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

/* 表单项样式 */
.profile-form .ant-form-item {
  margin-bottom: 0;
}

.profile-form .ant-form-item-label > label {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 15px;
  margin-bottom: 8px;
}

.profile-form .ant-form-item-explain-error {
  font-size: 13px;
  margin-top: 6px;
}

/* 输入框样式 */
.form-input {
  border-radius: 12px !important;
  border: 2px solid #e8e8e8 !important;
  padding: 12px 16px !important;
  font-size: 15px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.form-input:hover {
  border-color: #667eea !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.form-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: white !important;
}

.form-input.ant-input-disabled {
  background: rgba(248, 248, 248, 0.8) !important;
  border-color: #e8e8e8 !important;
  color: #999 !important;
}

/* 文本域样式 */
.form-textarea {
  border-radius: 12px !important;
  border: 2px solid #e8e8e8 !important;
  padding: 16px !important;
  font-size: 15px !important;
  line-height: 1.6 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  resize: none !important;
}

.form-textarea:hover {
  border-color: #667eea !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.form-textarea:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: white !important;
}

/* 单选按钮组样式 */
.radio-group {
  display: flex !important;
  gap: 8px !important;
}

.radio-group .ant-radio-button-wrapper {
  border-radius: 20px !important;
  border: 2px solid #e8e8e8 !important;
  background: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.radio-group .ant-radio-button-wrapper:hover {
  border-color: #667eea !important;
  background: rgba(102, 126, 234, 0.05) !important;
}

.radio-group .ant-radio-button-wrapper-checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
  color: white !important;
}

.radio-group .ant-radio-button-wrapper-checked:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: #667eea !important;
}

/* 输入框图标样式 */
.input-icon {
  color: #667eea !important;
  font-size: 16px !important;
}

/* 日期选择器样式 */
.form-input.ant-picker {
  width: 100% !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-hero {
    padding: 40px 0;
  }

  .profile-card {
    flex-direction: column;
    text-align: center;
    gap: 32px;
    padding: 32px 24px;
  }

  .profile-name {
    font-size: 28px;
  }

  .profile-subtitle {
    font-size: 16px;
  }

  .contact-info {
    align-items: center;
  }

  .edit-actions {
    flex-direction: column;
    width: 100%;
  }

  .primary-action,
  .secondary-action {
    width: 100% !important;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .form-row-split {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-header {
    padding: 20px 24px;
  }

  .card-body {
    padding: 24px;
  }

  .hero-content,
  .section-container {
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .profile-card {
    padding: 24px 16px;
  }

  .profile-name {
    font-size: 24px;
  }
}

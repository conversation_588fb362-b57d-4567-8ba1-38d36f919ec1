import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { authApi } from '@/services/api'
import { User } from './authSlice'

interface UserState {
  profile: User | null
  loading: boolean
  error: string | null
}

const initialState: UserState = {
  profile: null,
  loading: false,
  error: null,
}

// 获取用户信息
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (userId: number) => {
    const response = await authApi.getUserInfo(userId)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 更新用户信息
export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async ({ userId, userData }: { userId: number; userData: Partial<User> }) => {
    const response = await authApi.updateUser(userId, userData)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 修改密码
export const changeUserPassword = createAsyncThunk(
  'user/changePassword',
  async ({ userId, passwordData }: { 
    userId: number; 
    passwordData: { oldPassword: string; newPassword: string } 
  }) => {
    const response = await authApi.changePassword(userId, passwordData)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setProfile: (state, action: PayloadAction<User>) => {
      state.profile = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取用户信息
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = false
        state.profile = action.payload
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '获取用户信息失败'
      })
      // 更新用户信息
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false
        state.profile = action.payload
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '更新用户信息失败'
      })
      // 修改密码
      .addCase(changeUserPassword.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(changeUserPassword.fulfilled, (state) => {
        state.loading = false
      })
      .addCase(changeUserPassword.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '修改密码失败'
      })
  },
})

export const { clearError, setProfile } = userSlice.actions
export default userSlice.reducer

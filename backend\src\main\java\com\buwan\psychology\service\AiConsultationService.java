package com.buwan.psychology.service;

import com.buwan.psychology.entity.ChatMessage;
import com.buwan.psychology.entity.ConsultationSession;
import com.buwan.psychology.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * AI心理咨询服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AiConsultationService {

    private final WebClient.Builder webClientBuilder;
    
    @Value("${app.ai.api-key:}")
    private String aiApiKey;
    
    @Value("${app.ai.base-url:https://api.openai.com/v1}")
    private String aiBaseUrl;

    // 风险关键词模式
    private static final Pattern RISK_PATTERNS = Pattern.compile(
        "(?i)(自杀|自残|伤害自己|不想活|想死|结束生命|轻生|自我伤害|割腕|跳楼|服毒)", 
        Pattern.CASE_INSENSITIVE
    );

    // 情感关键词
    private static final Map<String, String> EMOTION_KEYWORDS = Map.of(
        "焦虑", "anxiety",
        "抑郁", "depression", 
        "愤怒", "anger",
        "恐惧", "fear",
        "悲伤", "sadness",
        "快乐", "happiness",
        "平静", "calm"
    );

    /**
     * 生成AI回复
     */
    public String generateAiResponse(ConsultationSession session, List<ChatMessage> chatHistory, String userMessage, User user) {
        try {
            String systemPrompt = buildSystemPrompt(user, session);
            String conversationContext = buildConversationContext(chatHistory);
            
            // 构建请求体
            Map<String, Object> requestBody = Map.of(
                "model", "gpt-3.5-turbo",
                "messages", List.of(
                    Map.of("role", "system", "content", systemPrompt),
                    Map.of("role", "user", "content", conversationContext + "\n用户: " + userMessage)
                ),
                "max_tokens", 1000,
                "temperature", 0.7
            );

            // 调用AI API
            WebClient webClient = webClientBuilder
                .baseUrl(aiBaseUrl)
                .defaultHeader("Authorization", "Bearer " + aiApiKey)
                .defaultHeader("Content-Type", "application/json")
                .build();

            // 这里是模拟响应，实际项目中需要调用真实的AI API
            String aiResponse = generateMockAiResponse(userMessage, user);
            
            log.info("AI回复生成成功，用户: {}, 会话: {}", user.getUsername(), session.getId());
            return aiResponse;
            
        } catch (Exception e) {
            log.error("AI回复生成失败", e);
            return "抱歉，我现在遇到了一些技术问题。请稍后再试，或者联系我们的人工客服。";
        }
    }

    /**
     * 分析消息情感
     */
    public String analyzeEmotion(String message) {
        // 简单的情感分析逻辑
        for (Map.Entry<String, String> entry : EMOTION_KEYWORDS.entrySet()) {
            if (message.contains(entry.getKey())) {
                return entry.getValue();
            }
        }
        return "neutral";
    }

    /**
     * 检测风险信号
     */
    public boolean detectRiskSignal(String message) {
        return RISK_PATTERNS.matcher(message).find();
    }

    /**
     * 评估风险级别
     */
    public int assessRiskLevel(String message, String emotion) {
        if (detectRiskSignal(message)) {
            return 3; // 高风险
        }
        
        if (emotion.equals("depression") || emotion.equals("anxiety")) {
            if (message.contains("严重") || message.contains("无法") || message.contains("绝望")) {
                return 2; // 中风险
            }
            return 1; // 低风险
        }
        
        return 0; // 无风险
    }

    /**
     * 提取关键词
     */
    public String extractKeywords(String message) {
        // 简单的关键词提取逻辑
        StringBuilder keywords = new StringBuilder();
        for (String keyword : EMOTION_KEYWORDS.keySet()) {
            if (message.contains(keyword)) {
                if (keywords.length() > 0) {
                    keywords.append(",");
                }
                keywords.append(keyword);
            }
        }
        return keywords.toString();
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(User user, ConsultationSession session) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一位专业的心理咨询师，名叫小晚。你的任务是为用户提供专业、温暖、有帮助的心理支持。\n\n");
        
        prompt.append("用户信息：\n");
        prompt.append("- 昵称：").append(user.getNickname() != null ? user.getNickname() : user.getUsername()).append("\n");
        if (user.getAge() != null) {
            prompt.append("- 年龄：").append(user.getAge()).append("岁\n");
        }
        if (user.isTeenager()) {
            prompt.append("- 特别注意：这是一位青少年用户，请使用适合青少年的语言和方法\n");
        }
        
        prompt.append("\n咨询类型：").append(session.getSessionTypeDesc()).append("\n\n");
        
        prompt.append("请遵循以下原则：\n");
        prompt.append("1. 保持专业、温暖、非评判的态度\n");
        prompt.append("2. 使用倾听、共情、引导的技巧\n");
        prompt.append("3. 如果发现自杀或自残倾向，立即表达关心并建议寻求专业帮助\n");
        prompt.append("4. 不要提供医学诊断，但可以建议寻求专业医疗帮助\n");
        prompt.append("5. 回复要简洁明了，一般控制在200字以内\n");
        
        if (user.isTeenager()) {
            prompt.append("6. 对青少年用户，要特别关注学习压力、人际关系、身份认同等问题\n");
        }
        
        return prompt.toString();
    }

    /**
     * 构建对话上下文
     */
    private String buildConversationContext(List<ChatMessage> chatHistory) {
        StringBuilder context = new StringBuilder();
        context.append("对话历史：\n");
        
        int maxMessages = Math.min(chatHistory.size(), 10); // 最多包含最近10条消息
        for (int i = chatHistory.size() - maxMessages; i < chatHistory.size(); i++) {
            ChatMessage message = chatHistory.get(i);
            String role = message.isUserMessage() ? "用户" : "小晚";
            context.append(role).append(": ").append(message.getContent()).append("\n");
        }
        
        return context.toString();
    }

    /**
     * 生成模拟AI回复（用于演示）
     */
    private String generateMockAiResponse(String userMessage, User user) {
        // 检测风险信号
        if (detectRiskSignal(userMessage)) {
            return "我很担心你现在的状态。你提到的想法让我非常关心你的安全。请记住，你并不孤单，总有人愿意帮助你。我强烈建议你立即联系专业的心理健康服务或拨打心理危机干预热线。如果情况紧急，请联系当地的紧急服务。你的生命很宝贵，请不要放弃。";
        }
        
        // 根据情感分析生成回复
        String emotion = analyzeEmotion(userMessage);
        
        if (emotion.equals("anxiety")) {
            return "我能感受到你现在的焦虑情绪。焦虑是很常见的情感反应，你不需要为此感到羞愧。让我们一起来探讨一下，是什么让你感到焦虑呢？同时，你可以尝试深呼吸练习来缓解当下的紧张感。";
        } else if (emotion.equals("depression")) {
            return "听起来你现在情绪比较低落，这一定很不好受。抑郁的感觉就像被乌云笼罩，但请记住，乌云终会散去。你愿意和我分享一下最近发生了什么吗？我们可以一起寻找一些应对的方法。";
        } else if (user.isTeenager()) {
            return "作为青少年，面对各种挑战和变化是很正常的。你现在的感受完全可以理解。青春期是一个充满变化的时期，有时候会感到困惑或压力。你愿意告诉我更多关于你现在面临的情况吗？";
        } else {
            return "谢谢你愿意和我分享你的想法。我在这里倾听你的声音，理解你的感受。每个人都会遇到困难的时候，重要的是我们如何面对和处理这些挑战。你觉得现在最困扰你的是什么呢？";
        }
    }
}

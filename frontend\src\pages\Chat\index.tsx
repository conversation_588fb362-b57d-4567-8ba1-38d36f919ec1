import React, { useState, useEffect, useRef } from 'react'
import { Layout, Typography, Button, Empty, message, Input, Upload, Tooltip, Popover } from 'antd'
import { useSelector, useDispatch } from 'react-redux'
import { PlusOutlined, MessageOutlined, PaperClipOutlined, PictureOutlined, FileOutlined, CloseOutlined, SmileOutlined, DeleteOutlined, DownOutlined } from '@ant-design/icons'
import { RootState, AppDispatch } from '@/store'
import { fetchSessions, createSession, setCurrentSession, fetchMessages, setStreamingStatus, clearSessionMessages, clearMessages, addMessage } from '@/store/slices/chatSlice'
import { chatApi } from '@/services/api'
import SessionList from './components/SessionList'
import ChatWindow from './components/ChatWindow'
import NewSessionModal from './components/NewSessionModal'
import MarkdownRenderer from '../../components/MarkdownRenderer'
import dayjs from 'dayjs'
import './index.css'



const Chat: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { sessions, currentSession, loading, messages, sending } = useSelector((state: RootState) => state.chat)
  const [newSessionModalVisible, setNewSessionModalVisible] = useState(false)
  const [messageInput, setMessageInput] = useState('')
  const [attachments, setAttachments] = useState<Array<{
    id: string
    name: string
    type: 'image' | 'file'
    base64: string
    size: number
    preview?: string
  }>>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const imageInputRef = useRef<HTMLInputElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [emojiVisible, setEmojiVisible] = useState(false)
  const [showPasteHint, setShowPasteHint] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [userScrolled, setUserScrolled] = useState(false)


  // 表情包数据
  const emojiCategories = {
    '常用': ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
    '情感': ['😥', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲'],
    '手势': ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵'],
    '物品': ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐'],
    '自然': ['🌱', '🌿', '🍀', '🍃', '🌾', '🌵', '🌲', '🌳', '🌴', '🌸', '🌺', '🌻', '🌷', '🌹', '🥀', '🌼', '🌙', '🌛', '🌜', '🌚', '🌕', '🌖', '🌗', '🌘', '🌑', '🌒', '🌓', '🌔', '⭐', '🌟', '💫'],
    '食物': ['🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐']
  }

  useEffect(() => {
    if (user?.id) {
      dispatch(fetchSessions(user.id))
    }
  }, [dispatch, user?.id])

  // 监听消息变化，自动滚动
  useEffect(() => {
    if (messages.length > 0) {
      // 延迟滚动，确保DOM更新完成
      setTimeout(() => smartScrollToBottom(), 100)
    }
  }, [messages.length, messages]) // 监听消息数量变化和消息内容变化

  // 添加剪贴板粘贴事件监听
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      // 只在聊天页面且有当前会话时处理粘贴
      if (!currentSession) return

      const items = e.clipboardData?.items
      if (!items) return

      // 遍历剪贴板项目
      for (let i = 0; i < items.length; i++) {
        const item = items[i]

        // 检查是否为图片
        if (item.type.indexOf('image') !== -1) {
          e.preventDefault() // 阻止默认粘贴行为

          const file = item.getAsFile()
          if (file) {
            await handleClipboardImage(file)
          }
          break
        }
      }
    }

    // 添加全局粘贴事件监听
    document.addEventListener('paste', handlePaste)

    // 键盘事件监听，显示粘贴提示
    const handleKeyDown = (e: KeyboardEvent) => {
      if (currentSession && e.ctrlKey && e.key === 'v') {
        setShowPasteHint(true)
        setTimeout(() => setShowPasteHint(false), 2000) // 2秒后隐藏提示
      }
    }

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown)

    // 清理事件监听
    return () => {
      document.removeEventListener('paste', handlePaste)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [currentSession]) // 依赖currentSession，确保只在有会话时处理

  // 自动滚动到底部
  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth'
      })
    }
  }

  // 检查是否需要自动滚动（用户是否在底部附近）
  const shouldAutoScroll = () => {
    if (!messagesContainerRef.current) return false
    
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current
    const threshold = 100 // 距离底部100px内认为是"底部附近"
    return scrollTop + clientHeight >= scrollHeight - threshold
  }

  // 智能滚动：如果用户在底部附近，则自动滚动；否则不滚动
  const smartScrollToBottom = () => {
    if (shouldAutoScroll() && !userScrolled) {
      scrollToBottom()
    }
  }

  // 处理用户手动滚动
  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 50
      setUserScrolled(!isAtBottom)
    }
  }

  const handleCreateSession = async (sessionData: { title: string; description?: string; sessionType: number; counselorId?: number; autoMatch?: boolean }) => {
    if (!user?.id) return

    try {
      const newSession = await dispatch(createSession({
        userId: user.id,
        ...sessionData
      })).unwrap()

      setNewSessionModalVisible(false)
      message.success('创建会话成功！')

      // 重新获取会话列表以确保数据完整性
      await dispatch(fetchSessions(user.id))

      // 从更新后的会话列表中找到新创建的会话
      const updatedSessions = await dispatch(fetchSessions(user.id)).unwrap()
      const completeNewSession = updatedSessions.find((session: any) => session.id === newSession.id)

      // 先清空消息，然后设置新会话
      dispatch(clearMessages())
      if (completeNewSession) {
        dispatch(setCurrentSession(completeNewSession))
      } else {
        dispatch(setCurrentSession(newSession))
      }
    } catch (error) {
      message.error('创建会话失败')
    }
  }

  const handleSelectSession = (session: any) => {
    // 先清空当前消息，避免显示旧会话的消息
    dispatch(clearMessages())
    dispatch(setCurrentSession(session))
    // 获取该会话的消息
    if (session.id) {
      dispatch(fetchMessages(session.id))
    }
    // 切换会话后滚动到底部
    setTimeout(() => scrollToBottom(), 100)
  }

  const handleClearMessages = async () => {
    if (!currentSession || !user) return

    try {
      await dispatch(clearSessionMessages({
        sessionId: currentSession.id,
        userId: user.id
      })).unwrap()
      message.success('历史记录已清空')
    } catch (error) {
      message.error('清空历史记录失败')
    }
  }

  const getSessionTypeLabel = (type: number) => {
    const types = ['一般咨询', '焦虑问题', '抑郁问题', '学习压力', '人际关系', '其他']
    return types[type] || '未知类型'
  }

  const handleSendMessage = async () => {
    if ((!messageInput.trim() && attachments.length === 0) || !currentSession || sending) return

    // 先清空输入框和附件，避免重复发送
    const messageContent = messageInput.trim()
    const messageAttachments = [...attachments]
    setMessageInput('')
    setAttachments([])

    try {
      // 立即添加用户消息到本地状态
      const userMessage = {
        id: -Date.now(), // 使用负数作为临时ID，避免与后端ID冲突
        sessionId: currentSession!.id,
        userId: user?.id || 0,
        content: messageContent,
        messageType: 0, // 用户消息
        createTime: new Date().toISOString(),
        attachments: messageAttachments.length > 0 ? messageAttachments : undefined
      }
      
      // 立即显示用户消息
      dispatch(addMessage(userMessage))

      // 用户发送消息后立即滚动到底部
      setTimeout(() => scrollToBottom(), 100)

      // 设置发送状态
      dispatch(setStreamingStatus(true))

      // 构建消息数据
      const messageData = {
        content: messageContent,
        attachments: messageAttachments.length > 0 ? messageAttachments : undefined
      }

      // 异步发送消息，不阻塞UI
      handleAsyncMessage(messageData)

    } catch (error) {
      message.error('发送消息失败')
      // 发送失败时恢复输入内容
      setMessageInput(messageContent)
      setAttachments(messageAttachments)
    }
  }

  // 异步处理消息发送
  const handleAsyncMessage = async (messageData: any) => {
    try {
      // 发送消息并等待完整响应
      const response = await chatApi.sendMessage(currentSession!.id, messageData)
      
      if (response.success) {
        // 刷新消息列表以显示AI回复（包含用户消息和AI回复）
        await dispatch(fetchMessages(currentSession!.id))
        // AI回复完成后滚动到底部
        setTimeout(() => scrollToBottom(), 100)
        // 不显示成功消息，因为用户消息已经显示了
      } else {
        throw new Error(response.message || '发送消息失败')
      }
    } catch (error) {
      console.error('异步发送消息失败:', error)
      message.error('AI回复生成失败，请稍后重试')
    } finally {
      // 重置发送状态
      dispatch(setStreamingStatus(false))
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !sending) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 文件转base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  }

  // 处理文件上传
  const handleFileUpload = async (file: File, type: 'image' | 'file') => {
    // 文件大小限制：图片10MB，文件20MB
    const maxSize = type === 'image' ? 10 * 1024 * 1024 : 20 * 1024 * 1024
    if (file.size > maxSize) {
      message.error(`${type === 'image' ? '图片' : '文件'}大小不能超过${type === 'image' ? '10' : '20'}MB`)
      return
    }

    try {
      const base64 = await fileToBase64(file)
      const attachment = {
        id: Date.now().toString(),
        name: file.name,
        type,
        base64,
        size: file.size,
        preview: type === 'image' ? base64 : undefined
      }

      setAttachments(prev => [...prev, attachment])
      message.success(`${type === 'image' ? '图片' : '文件'}添加成功`)
    } catch (error) {
      message.error('文件处理失败')
    }
  }

  // 处理剪贴板图片
  const handleClipboardImage = async (file: File) => {
    try {
      // 生成一个有意义的文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const fileName = `截图_${timestamp}.png`

      // 创建一个新的File对象，设置正确的文件名
      const renamedFile = new File([file], fileName, { type: file.type })

      await handleFileUpload(renamedFile, 'image')
      message.success('截图已添加到消息中')
    } catch (error) {
      message.error('粘贴图片失败')
    }
  }

  // 处理图片上传
  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      Array.from(files).forEach(file => {
        if (file.type.startsWith('image/')) {
          handleFileUpload(file, 'image')
        } else {
          message.error('请选择图片文件')
        }
      })
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = ''
  }

  // 处理文件上传
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      Array.from(files).forEach(file => {
        handleFileUpload(file, 'file')
      })
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = ''
  }

  // 移除附件
  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(item => item.id !== id))
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 插入表情
  const insertEmoji = (emoji: string) => {
    setMessageInput(prev => prev + emoji)
    setEmojiVisible(false)
  }

  // 表情选择器内容
  const renderEmojiPicker = () => (
    <div className="emoji-picker">
      {Object.entries(emojiCategories).map(([category, emojis]) => (
        <div key={category} className="emoji-category">
          <div className="emoji-category-title">{category}</div>
          <div className="emoji-grid">
            {emojis.map((emoji, index) => (
              <button
                key={index}
                className="emoji-item"
                onClick={() => insertEmoji(emoji)}
                title={emoji}
              >
                {emoji}
              </button>
            ))}
          </div>
        </div>
      ))}
    </div>
  )

  // 拖拽处理函数
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)

    if (!currentSession) return

    const files = Array.from(e.dataTransfer.files)

    for (const file of files) {
      if (file.type.startsWith('image/')) {
        await handleFileUpload(file, 'image')
      } else {
        await handleFileUpload(file, 'file')
      }
    }
  }

  return (
    <div className="chat-page-container">
      <div className="chat-layout-wrapper">
        {/* 左侧会话列表 */}
        <div className="chat-sidebar">
          <div className="sidebar-header">
            <div className="sidebar-title">
              <MessageOutlined className="title-icon" />
              <span>心理咨询</span>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setNewSessionModalVisible(true)}
              className="new-session-btn"
            >
              新建会话
            </Button>
          </div>

          <div className="sidebar-content">
            {sessions.length > 0 ? (
              <div className="session-list">
                {sessions.map((session: any) => (
                  <div
                    key={session.id}
                    className={`session-item ${currentSession?.id === session.id ? 'active' : ''}`}
                    onClick={() => handleSelectSession(session)}
                  >
                    <div className="session-avatar">
                      {session.counselor?.avatar ? (
                        <img
                          src={session.counselor.avatar}
                          alt={session.counselor.name}
                          style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}
                        />
                      ) : (
                        <MessageOutlined />
                      )}
                    </div>
                    <div className="session-info">
                      <div className="session-title">
                        {session.counselor?.name ? `${session.counselor.name} - ${session.title}` : session.title}
                      </div>
                      <div className="session-meta">
                        <span className="session-type">
                          {getSessionTypeLabel(session.sessionType)}
                        </span>
                        <span className="session-time">
                          {session.updateTime ?
                            dayjs(session.updateTime).format('MM-DD HH:mm') :
                            session.createTime ?
                            dayjs(session.createTime).format('MM-DD HH:mm') :
                            session.startTime ?
                            dayjs(session.startTime).format('MM-DD HH:mm') :
                            '未知时间'
                          }
                        </span>
                      </div>
                    </div>
                    <div className="session-status">
                      {session.status === 0 && <div className="status-dot active"></div>}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-sessions">
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="还没有咨询会话"
                />
              </div>
            )}
          </div>
        </div>

        {/* 右侧聊天区域 */}
        <div
          className={`chat-main ${dragOver ? 'drag-over' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {currentSession ? (
            <div className="chat-window">
              <div className="chat-header">
                <div className="chat-session-info">
                  <div className="session-avatar-large">
                    <img
                      src={currentSession.counselor?.avatar || "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=50&h=50&fit=crop&crop=face"}
                      alt={currentSession.counselor?.name || "心理咨询师"}
                      className="counselor-header-avatar"
                    />
                  </div>
                  <div>
                    <div className="chat-session-title">{currentSession.title}</div>
                    <div className="chat-session-type">
                      {getSessionTypeLabel(currentSession.sessionType)} · {currentSession.counselor?.name || 'AI心理咨询师'}
                    </div>
                  </div>
                </div>
                <div className="chat-header-actions">
                  <Tooltip title="清空历史记录">
                    <Button
                      type="text"
                      size="large"
                      icon={<DeleteOutlined />}
                      onClick={handleClearMessages}
                      disabled={loading || messages.length === 0}
                      className="clear-chat-button"
                    />
                  </Tooltip>
                </div>
              </div>

              <div className="chat-messages" ref={messagesContainerRef} onScroll={handleScroll}>
                {/* 滚动到底部按钮 */}
                {userScrolled && (
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<DownOutlined />}
                    size="large"
                    onClick={() => {
                      scrollToBottom()
                      setUserScrolled(false)
                    }}
                    style={{
                      position: 'absolute',
                      bottom: '20px',
                      right: '20px',
                      zIndex: 1000,
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                      background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                      border: 'none'
                    }}
                  />
                )}
                {/* 培训师信息始终显示 */}
                <div className="welcome-message">
                  <div className="counselor-avatar">
                    <img
                      src={currentSession.counselor?.avatar || "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100&h=100&fit=crop&crop=face"}
                      alt={currentSession.counselor?.name || "心理咨询师"}
                      className="avatar-image"
                    />
                  </div>
                  <div className="message-content">
                    <div className="counselor-intro">
                      <h3 className="counselor-name">
                        心理咨询师 - {currentSession.counselor?.name || 'AI心理咨询师'}
                      </h3>
                      {currentSession.counselor?.title && (
                        <div className="counselor-credentials">
                          <span className="credential">{currentSession.counselor.title}</span>
                          {currentSession.counselor.experience && (
                            <span className="credential">{currentSession.counselor.experience}年经验</span>
                          )}
                          {currentSession.counselor.rating && (
                            <span className="credential">评分 {currentSession.counselor.rating}</span>
                          )}
                        </div>
                      )}
                      {currentSession.counselor?.specializations && (
                        <div className="counselor-specialties">
                          <p><strong>专业领域：</strong>{currentSession.counselor.specializations}</p>
                        </div>
                      )}
                      <div className="welcome-text">
                        {currentSession.counselor?.welcomeMessage ||
                         `您好！我是${currentSession.counselor?.name || 'AI心理咨询师'}，很高兴为您提供心理咨询服务。请告诉我您遇到的问题，我会尽力帮助您。`}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 聊天消息列表 */}
                {messages.length > 0 && (
                  <div className="messages-divider">
                    <div className="divider-line"></div>
                    <span className="divider-text">开始对话</span>
                    <div className="divider-line"></div>
                  </div>
                )}

                {messages.map((msg: any) => (
                    <div key={msg.id} className={`message ${msg.messageType === 0 ? 'user-message' : 'ai-message'}`}>
                      <div className={msg.messageType === 0 ? 'user-avatar' : 'ai-avatar'}>
                        {msg.messageType === 0 ? (
                          '👤'
                        ) : (
                          currentSession.counselor?.avatar ? (
                            <img
                              src={currentSession.counselor.avatar}
                              alt={currentSession.counselor.name}
                              style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}
                            />
                          ) : (
                            '🤖'
                          )
                        )}
                      </div>
                      <div className="message-content">
                        {/* 附件显示 */}
                        {msg.attachments && msg.attachments.length > 0 && (
                          <div className="message-attachments">
                            {msg.attachments.map((attachment: any, index: number) => (
                              <div key={index} className="message-attachment">
                                {attachment.type === 'image' ? (
                                  <div className="message-image">
                                    <img src={attachment.base64} alt={attachment.name} />
                                  </div>
                                ) : (
                                  <div className="message-file">
                                    <div className="message-file-icon">
                                      <FileOutlined />
                                    </div>
                                    <div className="message-file-info">
                                      <div className="message-file-name">{attachment.name}</div>
                                      <div className="message-file-size">{formatFileSize(attachment.size)}</div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        {/* 文本内容 */}
                        {msg.content && (
                          <div className="message-text">
                            {msg.messageType === 1 ? (
                              <MarkdownRenderer content={msg.content} />
                            ) : (
                              <div>
                                {msg.content}
                                {sending && msg.id < 0 && (
                                  <div style={{ 
                                    fontSize: '12px', 
                                    opacity: 0.7, 
                                    marginTop: '4px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '4px'
                                  }}>
                                    <div style={{
                                      width: '8px',
                                      height: '8px',
                                      borderRadius: '50%',
                                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                      animation: 'pulse 1.5s infinite'
                                    }}></div>
                                    发送中...
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}

                        <div className="message-time">
                          {(() => {
                            const date = new Date(msg.createTime)
                            const now = new Date()
                            const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
                            
                            if (diffInMinutes < 1) {
                              return '刚刚'
                            } else if (diffInMinutes < 60) {
                              return `${diffInMinutes}分钟前`
                            } else if (diffInMinutes < 1440) { // 24小时
                              const hours = Math.floor(diffInMinutes / 60)
                              return `${hours}小时前`
                            } else {
                              return date.toLocaleDateString() + ' ' + date.toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })
                            }
                          })()}
                        </div>
                      </div>
                    </div>
                  ))}


              </div>

              <div className="chat-input">
                {/* 附件预览区域 */}
                {attachments.length > 0 && (
                  <div className="attachments-preview">
                    {attachments.map(attachment => (
                      <div key={attachment.id} className="attachment-item">
                        {attachment.type === 'image' ? (
                          <div className="attachment-image">
                            <img src={attachment.preview} alt={attachment.name} />
                            <div className="attachment-overlay">
                              <span className="attachment-name">{attachment.name}</span>
                              <Button
                                type="text"
                                size="small"
                                icon={<CloseOutlined />}
                                onClick={() => removeAttachment(attachment.id)}
                                className="remove-attachment-btn"
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="attachment-file">
                            <div className="file-icon">
                              <FileOutlined />
                            </div>
                            <div className="file-info">
                              <div className="file-name">{attachment.name}</div>
                              <div className="file-size">{formatFileSize(attachment.size)}</div>
                            </div>
                            <Button
                              type="text"
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={() => removeAttachment(attachment.id)}
                              className="remove-attachment-btn"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                <div className="input-wrapper">
                  <div className="input-actions">
                    <Tooltip title="上传图片">
                      <Button
                        type="text"
                        icon={<PictureOutlined />}
                        onClick={() => imageInputRef.current?.click()}
                        className="upload-btn"
                        disabled={sending}
                      />
                    </Tooltip>
                    <Tooltip title="上传文件">
                      <Button
                        type="text"
                        icon={<PaperClipOutlined />}
                        onClick={() => fileInputRef.current?.click()}
                        className="upload-btn"
                        disabled={sending}
                      />
                    </Tooltip>
                    <Popover
                      content={renderEmojiPicker()}
                      title="选择表情"
                      trigger="click"
                      open={emojiVisible}
                      onOpenChange={setEmojiVisible}
                      placement="topLeft"
                      overlayClassName="emoji-popover"
                    >
                      <Tooltip title="表情">
                        <Button
                          type="text"
                          icon={<SmileOutlined />}
                          className="upload-btn emoji-btn"
                          disabled={sending}
                        />
                      </Tooltip>
                    </Popover>
                  </div>

                  <Input.TextArea
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入您的问题..."
                    autoSize={{ minRows: 1, maxRows: 4 }}
                    className="message-input"
                    disabled={sending}
                  />

                  <Button
                    type="primary"
                    className="send-btn"
                    onClick={handleSendMessage}
                    loading={sending}
                    disabled={(!messageInput.trim() && attachments.length === 0) || sending}
                  >
                    {sending ? '正在回复...' : '发送'}
                  </Button>
                </div>

                <div className="input-tip">
                  按 Enter 发送，Shift + Enter 换行 · 支持图片和文件上传 · Ctrl + V 粘贴截图
                  {showPasteHint && (
                    <span className="paste-hint">
                      📋 检测到粘贴操作，如有图片将自动添加
                    </span>
                  )}
                </div>

                {/* 隐藏的文件输入框 */}
                <input
                  ref={imageInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  style={{ display: 'none' }}
                  onChange={handleImageSelect}
                />
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  style={{ display: 'none' }}
                  onChange={handleFileSelect}
                />
              </div>
            </div>
          ) : (
            <div className="welcome-screen">
              <div className="welcome-content">
                <div className="welcome-icon">
                  <MessageOutlined />
                </div>
                <h3 className="welcome-title">欢迎来到不晚心理</h3>
                <p className="welcome-desc">
                  我是您的心理咨询师小晚，很高兴为您提供专业的心理支持服务。
                  请选择一个会话开始咨询，或创建新的会话。
                </p>
                <Button
                  type="primary"
                  size="large"
                  icon={<PlusOutlined />}
                  onClick={() => setNewSessionModalVisible(true)}
                  className="start-chat-btn"
                >
                  开始新的咨询
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 新建会话模态框 - 只在需要时渲染 */}
      {newSessionModalVisible && (
        <NewSessionModal
          visible={true}
          onCancel={() => setNewSessionModalVisible(false)}
          onOk={handleCreateSession}
          loading={loading}
        />
      )}
    </div>
  )

}

export default Chat

server:
  port: 8080
  servlet:
    context-path: /
  # 请求超时配置
  tomcat:
    connection-timeout: 60000 # 60秒连接超时
    threads:
      max: 200
      min-spare: 10
  # 全局请求超时配置
  timeout:
    request: 60000 # 60秒请求超时

spring:
  application:
    name: buwan-psychology-platform
  
  # 数据库配置
  datasource:
    url: *****************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    open-in-view: false

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 5000ms

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# 日志配置
logging:
  level:
    com.buwan.psychology: DEBUG
    com.buwan.psychology.service.GeminiService: DEBUG
    com.buwan.psychology.controller.ChatController: DEBUG
    org.springframework.security: INFO
    org.springframework.web.reactive: DEBUG
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO
    reactor.netty: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/buwan-psychology.log

# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: buwan-psychology-platform-jwt-secret-key-2024-very-long-secure-key-for-hs512-algorithm-minimum-512-bits-required
    expiration: 86400000 # 24小时
  
  # AI服务配置
  ai:
    api-key: ${AI_API_KEY:your-ai-api-key}
    base-url: ${AI_BASE_URL:https://api.openai.com/v1}
    model: gpt-3.5-turbo
    max-tokens: 1000
    temperature: 0.7

# Gemini API配置
gemini:
  api:
    key: ${GEMINI_API_KEY:sk-lipanpan}
    base-url: http://*************:8000
    model: gemini-2.5-flash
    timeout: 60000 # 60秒超时
  # 开发模式：启用模拟服务（不调用真实API）
  mock:
    enabled: ${GEMINI_MOCK_ENABLED:false}
  
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:./uploads}
    max-size: 10MB
  
  # 安全配置
  security:
    cors:
      allowed-origins: 
        - http://localhost:3000
        - http://localhost:5173
        - http://127.0.0.1:3000
        - http://127.0.0.1:5173
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized



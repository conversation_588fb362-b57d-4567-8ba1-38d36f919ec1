package com.buwan.psychology.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心理咨询师实体类
 */
@Entity
@Table(name = "counselors")
@Data
@EqualsAndHashCode(callSuper = false)
public class Counselor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 咨询师姓名
     */
    @Column(nullable = false, length = 50)
    private String name;

    /**
     * 职业资格/职称
     */
    @Column(length = 100)
    private String title;

    /**
     * 专业领域（JSON数组格式存储多个专业领域）
     */
    @Column(columnDefinition = "TEXT")
    private String specializations;

    /**
     * 个人简介
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 头像URL (圆形头像，用于聊天界面)
     */
    @Column(length = 500)
    private String avatar;

    /**
     * 专业照片URL (全身像，用于专家团队页面展示)
     */
    @Column(length = 500)
    private String photo;

    /**
     * 从业年限
     */
    @Column
    private Integer experience;

    /**
     * 用户评分（1-5分）
     */
    @Column
    private Double rating;

    /**
     * 咨询次数
     */
    @Column
    private Integer consultationCount = 0;

    /**
     * 是否可用/在线
     */
    @Column
    private Boolean isAvailable = true;

    /**
     * 工作时间描述
     */
    @Column(length = 200)
    private String workingHours;

    /**
     * 咨询费用（每小时，单位：元）
     */
    @Column
    private Double consultationFee;

    /**
     * 个人标签（如温和、专业、耐心等）
     */
    @Column(length = 500)
    private String tags;

    /**
     * 教育背景
     */
    @Column(columnDefinition = "TEXT")
    private String education;

    /**
     * 执业证书编号
     */
    @Column(length = 100)
    private String licenseNumber;

    /**
     * 咨询理念
     */
    @Column(columnDefinition = "TEXT")
    private String philosophy;

    /**
     * AI角色设定/提示词
     * 用于定义AI扮演该咨询师时的角色、性格、专业背景和回复风格
     */
    @Column(columnDefinition = "TEXT")
    private String rolePrompt;

    /**
     * 欢迎语
     * 咨询师在新会话开始时向用户发送的欢迎消息
     */
    @Column(columnDefinition = "TEXT")
    private String welcomeMessage;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    /**
     * 与咨询会话的关联关系
     */
    @OneToMany(mappedBy = "counselor", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<ConsultationSession> consultationSessions;

    /**
     * 专业领域枚举
     */
    public enum Specialization {
        ANXIETY_DISORDERS("焦虑障碍"),
        DEPRESSION("抑郁障碍"),
        ADOLESCENT_PSYCHOLOGY("青少年心理"),
        MARRIAGE_FAMILY("婚姻家庭"),
        WORKPLACE_PSYCHOLOGY("职场心理"),
        TRAUMA_THERAPY("创伤治疗"),
        ADDICTION_THERAPY("成瘾治疗"),
        PERSONALITY_DISORDERS("人格障碍"),
        CHILD_PSYCHOLOGY("儿童心理"),
        ELDERLY_PSYCHOLOGY("老年心理"),
        GENERAL_COUNSELING("综合咨询");

        private final String displayName;

        Specialization(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}

import axios from 'axios'
import { getCurrentToken, clearAuth } from '@/utils/auth'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'

// 创建带认证的axios实例
const createAuthenticatedRequest = () => {
  const token = getCurrentToken()

  if (!token) {
    // Token无效或过期，直接跳转到token过期页面
    clearAuth()
    window.location.href = '/token-expired'
    throw new Error('认证已过期，请重新登录')
  }

  const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  })

  // 添加响应拦截器处理token过期
  api.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401 || error.response?.status === 403) {
        // Token过期或无效，清除本地存储并跳转到token过期页面
        clearAuth()
        window.location.href = '/token-expired'
      }
      return Promise.reject(error)
    }
  )

  return api
}

// 用户API接口
export const userApi = {
  // 获取用户详细信息
  getProfile: async () => {
    try {
      const api = createAuthenticatedRequest()
      const response = await api.get('/user/profile')
      return {
        success: true,
        data: response.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取用户信息失败'
      }
    }
  },

  // 更新用户信息
  updateProfile: async (profileData: any) => {
    try {
      const api = createAuthenticatedRequest()
      const response = await api.post('/user/profile', profileData)
      return {
        success: true,
        data: response.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '更新用户信息失败'
      }
    }
  },

  // 上传头像
  uploadAvatar: async (file: File) => {
    try {
      const formData = new FormData()
      formData.append('avatar', file)

      const api = createAuthenticatedRequest()
      const response = await api.post('/user/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return {
        success: true,
        data: response.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '头像上传失败'
      }
    }
  }
}

// 用户统计数据API
export const userStatsApi = {
  // 获取用户统计概览
  getStats: async () => {
    try {
      const api = createAuthenticatedRequest()
      const response = await api.get('/user/stats/overview')
      return {
        success: true,
        data: response.data.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取统计数据失败'
      }
    }
  },

  // 获取最近咨询会话
  getRecentSessions: async (limit: number = 5) => {
    try {
      const api = createAuthenticatedRequest()
      const response = await api.get('/user/stats/recent-sessions', {
        params: { limit }
      })
      return {
        success: true,
        data: response.data.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取最近咨询会话失败'
      }
    }
  },

  // 获取最近评估记录
  getRecentAssessments: async (limit: number = 5) => {
    try {
      const api = createAuthenticatedRequest()
      const response = await api.get('/user/stats/recent-assessments', {
        params: { limit }
      })
      return {
        success: true,
        data: response.data.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取最近评估记录失败'
      }
    }
  },

  // 获取用户活跃度统计
  getActivityStats: async () => {
    try {
      const api = createAuthenticatedRequest()
      const response = await api.get('/user/stats/activity')
      return {
        success: true,
        data: response.data.data
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || '获取活跃度统计失败'
      }
    }
  }
}

export default userApi

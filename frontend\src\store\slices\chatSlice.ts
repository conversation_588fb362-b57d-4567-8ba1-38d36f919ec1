import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { chatApi } from '@/services/api'

export interface ChatMessage {
  id: number
  sessionId: number
  userId: number
  content: string
  messageType: number // 0-用户消息，1-AI回复，2-系统消息
  emotionAnalysis?: string
  keywords?: string
  confidence?: number
  hasRiskSignal?: boolean
  riskLevel?: number
  createTime: string
}

export interface Counselor {
  id: number
  name: string
  title?: string
  avatar?: string
  specializations?: string
  rating?: number
  experience?: number
  welcomeMessage?: string
}

export interface ChatSession {
  id: number
  userId: number
  title: string
  description?: string
  sessionType: number
  status: number
  summary?: string
  aiAnalysis?: string
  recommendations?: string
  urgencyLevel?: number
  startTime?: string
  endTime?: string
  createTime: string
  updateTime: string
  counselorId?: number
  counselor?: Counselor
}

interface ChatState {
  sessions: ChatSession[]
  currentSession: ChatSession | null
  messages: ChatMessage[]
  loading: boolean
  sending: boolean
  error: string | null
}

const initialState: ChatState = {
  sessions: [],
  currentSession: null,
  messages: [],
  loading: false,
  sending: false,
  error: null,
}

// 获取会话列表
export const fetchSessions = createAsyncThunk(
  'chat/fetchSessions',
  async (userId: number) => {
    const response = await chatApi.getSessions(userId)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 创建新会话
export const createSession = createAsyncThunk(
  'chat/createSession',
  async (sessionData: {
    userId: number;
    title: string;
    description?: string;
    sessionType: number;
    counselorId?: number;
    autoMatch?: boolean
  }) => {
    const response = await chatApi.createSession(sessionData)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 获取会话消息
export const fetchMessages = createAsyncThunk(
  'chat/fetchMessages',
  async (sessionId: number) => {
    const response = await chatApi.getMessages(sessionId)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 清空会话消息
export const clearSessionMessages = createAsyncThunk(
  'chat/clearSessionMessages',
  async ({ sessionId, userId }: { sessionId: number; userId: number }) => {
    const response = await chatApi.clearMessages(sessionId, userId)
    if (response.success) {
      return sessionId
    }
    throw new Error(response.message)
  }
)

// 发送消息（仅用于后端API调用，不直接操作状态）
export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ sessionId, content, attachments }: {
    sessionId: number;
    content: string;
    attachments?: any[]
  }) => {
    const response = await chatApi.sendMessage(sessionId, { content, attachments })
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 流式发送消息
export const sendStreamMessage = createAsyncThunk(
  'chat/sendStreamMessage',
  async ({ sessionId, content, attachments }: {
    sessionId: number;
    content: string;
    attachments?: any[]
  }) => {
    // 这里返回sessionId，实际的流式处理在组件中进行
    return { sessionId, content, attachments }
  }
)

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<ChatSession | null>) => {
      state.currentSession = action.payload
    },
    clearMessages: (state) => {
      state.messages = []
    },
    clearError: (state) => {
      state.error = null
    },
    addMessage: (state, action: PayloadAction<ChatMessage>) => {
      state.messages.push(action.payload)
    },
    setStreamingStatus: (state, action: PayloadAction<boolean>) => {
      state.sending = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取会话列表
      .addCase(fetchSessions.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchSessions.fulfilled, (state, action) => {
        state.loading = false
        state.sessions = action.payload
      })
      .addCase(fetchSessions.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '获取会话列表失败'
      })
      // 创建会话
      .addCase(createSession.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createSession.fulfilled, (state, action) => {
        state.loading = false
        state.sessions.unshift(action.payload)
        // 不自动设置当前会话，避免页面结构变化
        // state.currentSession = action.payload
      })
      .addCase(createSession.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '创建会话失败'
      })
      // 获取消息
      .addCase(fetchMessages.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.loading = false
        state.messages = action.payload
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '获取消息失败'
      })
      // 发送消息（仅处理API调用状态，消息显示由组件控制）
      .addCase(sendMessage.pending, (state) => {
        state.sending = true
        state.error = null
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.sending = false
        // 不直接添加消息，由组件通过fetchMessages获取最新消息
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.sending = false
        state.error = action.error.message || '发送消息失败'
      })
      // 清空消息
      .addCase(clearSessionMessages.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(clearSessionMessages.fulfilled, (state) => {
        state.loading = false
        state.messages = []
      })
      .addCase(clearSessionMessages.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '清空消息失败'
      })
  },
})

export const { setCurrentSession, clearMessages, clearError, addMessage, setStreamingStatus } = chatSlice.actions
export default chatSlice.reducer

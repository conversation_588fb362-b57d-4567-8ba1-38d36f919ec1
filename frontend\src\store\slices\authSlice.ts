import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { message } from 'antd'
import { authApi } from '@/services/api'

export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  gender?: number
  age?: number
  phone?: string
  userType: number
  status: number
  lastLoginTime?: string
  createTime: string
  updateTime: string
  bio?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
}

// 检查token是否过期
const isTokenExpired = (token: string | null): boolean => {
  if (!token) return true

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    return true
  }
}

// 清理过期的认证信息
const clearExpiredAuth = () => {
  const token = localStorage.getItem('token')
  if (isTokenExpired(token)) {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    return { user: null, token: null, isAuthenticated: false }
  }
  return {
    user: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!) : null,
    token: localStorage.getItem('token'),
    isAuthenticated: !!localStorage.getItem('token')
  }
}

const authData = clearExpiredAuth()

const initialState: AuthState = {
  user: authData.user,
  token: authData.token,
  isAuthenticated: authData.isAuthenticated,
  loading: false,
  error: null,
}

// 异步登录
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: { usernameOrEmail: string; password: string }) => {
    const response = await authApi.login(credentials)
    if (response.success) {
      localStorage.setItem('token', response.data.token || '')
      localStorage.setItem('user', JSON.stringify(response.data.user))
      return response.data.user
    }
    throw new Error(response.message)
  }
)

// 异步注册
export const register = createAsyncThunk(
  'auth/register',
  async (userData: Partial<User> & { password: string }) => {
    const response = await authApi.register(userData)
    if (response.success) {
      return response.data
    }
    throw new Error(response.message)
  }
)

// 异步登出
export const logout = createAsyncThunk('auth/logout', async () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
})

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload }
        localStorage.setItem('user', JSON.stringify(state.user))
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // 登录
      .addCase(login.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false
        state.user = action.payload
        state.isAuthenticated = true
        state.error = null
        message.success('登录成功')
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '登录失败'
        message.error(state.error)
      })
      // 注册
      .addCase(register.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(register.fulfilled, (state) => {
        state.loading = false
        state.error = null
        message.success('注册成功，请登录')
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '注册失败'
        message.error(state.error)
      })
      // 登出
      .addCase(logout.fulfilled, (state) => {
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.error = null
        message.success('已退出登录')
      })
  },
})

export const { clearError, updateUser } = authSlice.actions
export default authSlice.reducer

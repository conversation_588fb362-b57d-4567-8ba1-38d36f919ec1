package com.buwan.psychology;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 不晚心理平台主应用类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableTransactionManagement
public class PsychologyPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(PsychologyPlatformApplication.class, args);
        System.out.println("=================================");
        System.out.println("不晚心理平台启动成功！");
        System.out.println("API文档地址: http://localhost:8080/swagger-ui.html");
        System.out.println("=================================");
    }
}

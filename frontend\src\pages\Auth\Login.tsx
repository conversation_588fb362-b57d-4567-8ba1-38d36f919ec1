import React from 'react'
import { Form, Input, Button, Card, Typography, Divider, Space } from 'antd'
import { UserOutlined, LockOutlined, HeartOutlined } from '@ant-design/icons'
import { Link, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { login } from '@/store/slices/authSlice'
import './Auth.css'

const { Title, Text } = Typography

interface LoginForm {
  usernameOrEmail: string
  password: string
}

const Login: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()
  const { loading } = useSelector((state: RootState) => state.auth)

  const onFinish = async (values: LoginForm) => {
    try {
      await dispatch(login(values)).unwrap()
      navigate('/chat')
    } catch (error) {
      // 错误已在slice中处理
    }
  }

  return (
    <div className="auth-container">
      <div className="auth-content">
        <Card className="auth-card">
          <div className="auth-brand">
            <div className="auth-brand-icon">
              <HeartOutlined />
            </div>
            <span className="auth-brand-text">不晚心理</span>
          </div>

          <div className="auth-header">
            <Title level={2} className="auth-title">
              欢迎回来
            </Title>
            <Text className="auth-subtitle">
              登录您的账户，继续您的心理健康之旅
            </Text>
          </div>

          <Form
            name="login"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
            className="auth-form"
          >
            <Form.Item
              name="usernameOrEmail"
              rules={[
                { required: true, message: '请输入用户名或邮箱' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名或邮箱"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                className="auth-input"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="auth-button"
                block
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          <Divider className="auth-divider">
            <Text type="secondary">其他选项</Text>
          </Divider>

          <div className="auth-links">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div className="auth-link-item">
                <Text>还没有账户？</Text>
                <Link to="/register" className="auth-link">
                  立即注册
                </Link>
              </div>
              
              <div className="auth-link-item">
                <Link to="/forgot-password" className="auth-link">
                  忘记密码？
                </Link>
              </div>
            </Space>
          </div>

          <div className="auth-footer">
            <Text type="secondary" className="auth-footer-text">
              登录即表示您同意我们的
              <Link to="/terms" className="auth-link"> 服务条款 </Link>
              和
              <Link to="/privacy" className="auth-link"> 隐私政策</Link>
            </Text>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default Login

import React, { useState, useEffect } from 'react'
import {
  Typography,
  Button,
  Form,
  Input,
  DatePicker,
  Radio,
  Avatar,
  message,
  Spin
} from 'antd'
import {
  UserOutlined,
  EditOutlined,
  SaveOutlined,
  PhoneOutlined,
  MailOutlined,
  TrophyOutlined
} from '@ant-design/icons'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { userApi } from '@/api/user'
import dayjs from 'dayjs'
import './index.css'

const { TextArea } = Input

// 用户信息接口
interface UserProfile {
  id?: number
  username?: string
  email?: string
  phone?: string
  avatar?: string
  realName?: string
  gender?: string
  birthDate?: string
  age?: number
  maritalStatus?: string
  education?: string
  occupation?: string
  income?: string
  location?: {
    province?: string
    city?: string
    district?: string
  }
  emergencyContact?: {
    name?: string
    relationship?: string
    phone?: string
  }
  medicalHistory?: string[]
  psychologicalHistory?: string[]
  currentMedications?: string
  allergies?: string
  lifestyle?: {
    smoking?: boolean
    drinking?: boolean
    exercise?: string
    sleep?: string
  }
  personalityTraits?: string[]
  interests?: string[]
  stressFactors?: string[]
  copingMethods?: string[]
  goals?: string
  additionalInfo?: string
  privacySettings?: {
    showRealName?: boolean
    showAge?: boolean
    showLocation?: boolean
    allowDataSharing?: boolean
  }
}

const Profile: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth)
  const [form] = Form.useForm()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [dataLoaded, setDataLoaded] = useState(false)

  const [userProfile, setUserProfile] = useState<UserProfile>({})

  useEffect(() => {
    loadUserProfile()
  }, [])

  const loadUserProfile = async () => {
    setLoading(true)
    try {
      const response = await userApi.getProfile()

      if (response.success && response.data) {
        console.log('API返回的原始数据:', response.data)

        // 修复：response.data 本身又包含了一个 data 字段
        // 真正的用户数据在 response.data.data 中
        const userData = response.data.data || response.data
        console.log('真正的用户数据:', userData)

        setUserProfile(userData)

        // 直接使用真正的用户数据，只处理特殊字段
        const formData = {
          ...userData, // 先复制所有原始数据
          // 处理日期字段
          birthDate: userData.birthDate ? dayjs(userData.birthDate) : null,
          // 处理性别字段：数字转字符串 (0-其他，1-男，2-女)
          gender: userData.gender === 1 ? 'male' : userData.gender === 2 ? 'female' : 'other',
          // 确保嵌套对象存在
          location: userData.location || {},
          emergencyContact: userData.emergencyContact || {},
          lifestyle: userData.lifestyle || {},
          privacySettings: userData.privacySettings || {}
        }

        console.log('处理后的表单数据:', formData)
        form.setFieldsValue(formData)

        // 验证设置是否成功
        setTimeout(() => {
          console.log('表单当前值:', form.getFieldsValue())
        }, 100)

        setDataLoaded(true)
      } else {
        console.log('API返回success但没有data，使用Redux中的用户信息:', user)
        // 如果没有详细资料，使用基本用户信息
        const basicProfile: UserProfile = {
          username: user?.username || '',
          email: user?.email || '',
          realName: user?.username || '', // 使用username作为默认realName
          privacySettings: {
            showRealName: true,
            showAge: true,
            showLocation: true,
            allowDataSharing: false
          }
        }
        setUserProfile(basicProfile)
        form.setFieldsValue(basicProfile)
        setDataLoaded(true)
      }
    } catch (error) {
      message.error('加载用户信息失败')
      setDataLoaded(true)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (values: any) => {
    setLoading(true)
    try {
      // 处理日期格式和数据类型转换
      const formData = {
        ...values,
        birthDate: values.birthDate ? values.birthDate.format('YYYY-MM-DD') : null,
        age: values.birthDate ? dayjs().diff(values.birthDate, 'year') : null,
        // 处理性别字段：字符串转数字 (male->1, female->2, other->0)
        gender: values.gender === 'male' ? 1 : values.gender === 'female' ? 2 : 0
      }

      // 调用API保存用户信息
      const response = await userApi.updateProfile(formData)

      if (response.success) {
        setUserProfile({ ...userProfile, ...formData })
        setIsEditing(false)
        message.success('个人信息保存成功')
      } else {
        message.error(response.message || '保存失败，请重试')
      }
    } catch (error) {
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setIsEditing(false)
    loadUserProfile()
  }



  if (!dataLoaded) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载用户资料中...</div>
      </div>
    )
  }

  return (
    <div className="profile-page">
      <Spin spinning={loading} tip="加载中...">
        {/* 全新的头部设计 */}
        <div className="profile-hero">
          <div className="hero-background">
            <div className="bg-pattern"></div>
            <div className="bg-gradient"></div>
          </div>

          <div className="hero-content">
            <div className="profile-card">
              <div className="avatar-section">
                <div className="avatar-container">
                  <Avatar
                    size={120}
                    src={userProfile.avatar}
                    icon={<UserOutlined />}
                    className="main-avatar"
                  />
                  <div className="avatar-ring"></div>
                </div>
              </div>

              <div className="profile-details">
                <h1 className="profile-name">
                  {userProfile.realName || userProfile.username || '请设置您的姓名'}
                </h1>
                <p className="profile-subtitle">
                  {[
                    userProfile.age ? `${userProfile.age}岁` : '',
                    userProfile.gender === 'male' || (userProfile.gender as any) === 1 ? '男' :
                    userProfile.gender === 'female' || (userProfile.gender as any) === 2 ? '女' : '',
                    userProfile.occupation || ''
                  ].filter(Boolean).join(' · ') || '完善个人信息，让咨询师更好地了解您'}
                </p>

                <div className="contact-info">
                  {userProfile.email && (
                    <div className="contact-item">
                      <MailOutlined />
                      <span>{userProfile.email}</span>
                    </div>
                  )}
                  {userProfile.phone && (
                    <div className="contact-item">
                      <PhoneOutlined />
                      <span>{userProfile.phone}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="profile-actions">
                {!isEditing ? (
                  <Button
                    type="primary"
                    size="large"
                    icon={<EditOutlined />}
                    onClick={() => setIsEditing(true)}
                    className="primary-action"
                  >
                    编辑资料
                  </Button>
                ) : (
                  <div className="edit-actions">
                    <Button
                      size="large"
                      onClick={handleCancel}
                      className="secondary-action"
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      size="large"
                      icon={<SaveOutlined />}
                      onClick={() => form.submit()}
                      loading={loading}
                      className="primary-action"
                    >
                      保存更改
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 全新的表单区域设计 */}
        <div className="form-section">
          <div className="section-container">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              className="profile-form"
            >
              <div className="form-grid">
                {/* 个人信息卡片 */}
                <div className="form-card">
                  <div className="card-header">
                    <div className="header-icon">
                      <UserOutlined />
                    </div>
                    <div className="header-content">
                      <h3>个人信息</h3>
                      <p>基本个人资料信息</p>
                    </div>
                  </div>

                  <div className="card-body">
                    <div className="form-row">
                      <Form.Item
                        name="realName"
                        label="真实姓名"
                        rules={[
                          { required: true, message: '请输入您的真实姓名' },
                          { min: 2, message: '姓名至少需要2个字符' },
                          { max: 20, message: '姓名不能超过20个字符' }
                        ]}
                      >
                        <Input
                          placeholder="请输入您的真实姓名"
                          disabled={!isEditing}
                          size="large"
                          className="form-input"
                        />
                      </Form.Item>
                    </div>

                    <div className="form-row-split">
                      <Form.Item
                        name="gender"
                        label="性别"
                        rules={[{ required: true, message: '请选择您的性别' }]}
                      >
                        <Radio.Group disabled={!isEditing} size="large" className="radio-group">
                          <Radio.Button value="male">男</Radio.Button>
                          <Radio.Button value="female">女</Radio.Button>
                          <Radio.Button value="other">其他</Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      <Form.Item
                        name="birthDate"
                        label="出生日期"
                        rules={[{ required: true, message: '请选择您的出生日期' }]}
                      >
                        <DatePicker
                          placeholder="选择出生日期"
                          disabled={!isEditing}
                          size="large"
                          format="YYYY-MM-DD"
                          className="form-input"
                          disabledDate={(current) => current && current > dayjs().endOf('day')}
                        />
                      </Form.Item>
                    </div>

                    <div className="form-row">
                      <Form.Item
                        name="occupation"
                        label="职业"
                        rules={[
                          { required: true, message: '请输入您的职业' },
                          { max: 50, message: '职业描述不能超过50个字符' }
                        ]}
                      >
                        <Input
                          placeholder="请输入您的职业"
                          disabled={!isEditing}
                          size="large"
                          className="form-input"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>

                {/* 联系方式卡片 */}
                <div className="form-card">
                  <div className="card-header">
                    <div className="header-icon">
                      <PhoneOutlined />
                    </div>
                    <div className="header-content">
                      <h3>联系方式</h3>
                      <p>联系信息和紧急联系人</p>
                    </div>
                  </div>

                  <div className="card-body">
                    <div className="form-row">
                      <Form.Item
                        name="email"
                        label="邮箱地址"
                        rules={[
                          { required: true, message: '请输入您的邮箱地址' },
                          { type: 'email', message: '请输入正确的邮箱格式' }
                        ]}
                      >
                        <Input
                          prefix={<MailOutlined className="input-icon" />}
                          placeholder="请输入邮箱地址"
                          disabled={!isEditing}
                          size="large"
                          className="form-input"
                        />
                      </Form.Item>
                    </div>

                    <div className="form-row">
                      <Form.Item
                        name="phone"
                        label="手机号码"
                        rules={[
                          { required: true, message: '请输入您的手机号码' },
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }
                        ]}
                      >
                        <Input
                          prefix={<PhoneOutlined className="input-icon" />}
                          placeholder="请输入11位手机号码"
                          disabled={!isEditing}
                          size="large"
                          maxLength={11}
                          className="form-input"
                        />
                      </Form.Item>
                    </div>

                    <div className="form-row-split">
                      <Form.Item
                        name={['emergencyContact', 'name']}
                        label="紧急联系人"
                        rules={[
                          { min: 2, message: '姓名至少需要2个字符' },
                          { max: 20, message: '姓名不能超过20个字符' }
                        ]}
                      >
                        <Input
                          placeholder="联系人姓名"
                          disabled={!isEditing}
                          size="large"
                          maxLength={20}
                          className="form-input"
                        />
                      </Form.Item>

                      <Form.Item
                        name={['emergencyContact', 'phone']}
                        label="联系电话"
                        rules={[
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }
                        ]}
                      >
                        <Input
                          placeholder="联系电话"
                          disabled={!isEditing}
                          size="large"
                          maxLength={11}
                          className="form-input"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>

                {/* 咨询目标卡片 */}
                <div className="form-card full-width">
                  <div className="card-header">
                    <div className="header-icon">
                      <TrophyOutlined />
                    </div>
                    <div className="header-content">
                      <h3>咨询目标</h3>
                      <p>请描述您希望通过心理咨询达到的目标</p>
                    </div>
                  </div>

                  <div className="card-body">
                    <div className="form-row">
                      <Form.Item
                        name="goals"
                        label="咨询目标"
                        rules={[
                          { max: 500, message: '咨询目标描述不能超过500个字符' }
                        ]}
                      >
                        <TextArea
                          placeholder="例如：缓解焦虑情绪、改善人际关系、提升自信心、处理工作压力等..."
                          rows={4}
                          disabled={!isEditing}
                          showCount
                          maxLength={500}
                          className="form-textarea"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </Spin>
    </div>
  )
}

export default Profile

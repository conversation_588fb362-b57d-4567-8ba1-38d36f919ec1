# 不晚心理 - AI心理健康平台

## 项目简介

不晚心理是一个基于人工智能的心理健康服务平台，致力于为用户提供专业的心理咨询、心理辅导和心理健康评估服务。特别关注青少年心理健康，通过AI技术提供24/7的心理支持服务。

## 核心功能

### 🤖 AI心理咨询
- 智能对话系统，提供专业心理咨询建议
- 多轮对话记录和上下文理解
- 个性化心理健康方案推荐

### 📊 心理健康评估
- 标准化心理测评量表
- 智能分析和报告生成
- 长期心理健康趋势跟踪

### 👥 青少年专项服务
- 针对青少年心理特点的专门服务
- 焦虑、抑郁、学习压力等问题的专业指导
- 家长和学校的协同支持

### 📱 用户管理
- 安全的用户注册和登录系统
- 个人心理档案管理
- 隐私保护和数据安全

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.x
- **安全**: Spring Security
- **数据库**: MySQL + Redis
- **AI集成**: OpenAI API / 其他AI服务
- **构建工具**: Maven

### 前端技术栈
- **框架**: React 18 + TypeScript
- **UI组件**: Ant Design
- **状态管理**: Redux Toolkit
- **路由**: React Router
- **构建工具**: Vite

## 项目结构

```
buwan/
├── backend/                 # Java Spring Boot后端
│   ├── src/main/java/       # Java源代码
│   ├── src/main/resources/  # 配置文件和资源
│   ├── src/test/           # 测试代码
│   └── pom.xml             # Maven配置
├── frontend/               # React前端
│   ├── src/                # 前端源代码
│   ├── public/             # 静态资源
│   └── package.json        # npm配置
├── docs/                   # 项目文档
└── README.md              # 项目说明
```

## 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- Node.js 18+
- MySQL 8.0+ (localhost:3336, root/123456)
- Redis 6.0+ (localhost:6379, 无密码)

### 一键启动（推荐）
```bash
# Windows - 双击运行或命令行执行
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

脚本会自动：
1. ✅ 检查环境和服务
2. ✅ 初始化数据库（如果需要）
3. ✅ 启动后端服务
4. ✅ 启动前端服务

### 停止服务
```bash
# Windows
stop.bat

# Linux/Mac
./stop.sh
```

### 访问应用
- **前端**: http://localhost:3000
- **后端**: http://localhost:8080
- **管理员**: <EMAIL> / admin123
- **设计展示**: http://localhost:3000/style-demo
- **配色测试**: http://localhost:3000/color-test
- **按钮测试**: http://localhost:3000/button-test
- **首页按钮修复测试**: http://localhost:3000/home-button-test
- **认证页面设计测试**: http://localhost:3000/auth-button-test

### 测试前端
如果前端启动有问题，可以单独测试：
```cmd
test-frontend.bat
```

### 🎨 设计系统特色
- **现代化配色**: 采用渐变色和毛玻璃效果
- **统一的设计语言**: CSS变量统一管理颜色和样式
- **响应式设计**: 完美适配桌面和移动端
- **流畅动画**: 使用CSS3动画提升用户体验
- **无障碍设计**: 支持深色模式和减少动画偏好

## 开发规范

- 遵循RESTful API设计原则
- 使用统一的代码风格和注释规范
- 重视用户隐私和数据安全
- 确保AI服务的专业性和准确性

## 联系我们

如有问题或建议，请联系开发团队。

---

**注意**: 本项目涉及心理健康服务，请确保所有功能都符合相关法律法规和医疗伦理要求。

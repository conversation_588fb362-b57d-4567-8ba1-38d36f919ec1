import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Avatar,
  Rate,
  Tag,
  Button,
  Spin,
  message,
  Select,
  Input,
  Empty,
  Typography,
  Divider
} from 'antd'
import {
  UserOutlined,
  SearchOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  MessageOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons'
import { counselorApi } from '../../services/api'
import './index.css'

const { Option } = Select
const { Search } = Input
const { Title, Text, Paragraph } = Typography

interface Counselor {
  id: number
  name: string
  title: string
  specializations: string
  description: string
  avatar: string
  photo?: string
  experience: number
  rating: number
  consultationCount: number
  isAvailable: boolean
  workingHours: string
  tags: string
  consultationFee?: number
}

const Counselors: React.FC = () => {
  const [counselors, setCounselors] = useState<Counselor[]>([])
  const [filteredCounselors, setFilteredCounselors] = useState<Counselor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchText, setSearchText] = useState('')
  const [selectedSpecialization, setSelectedSpecialization] = useState<string>('')

  // 专业领域选项
  const specializations = [
    { value: '', label: '全部专业' },
    { value: '焦虑障碍', label: '焦虑障碍' },
    { value: '抑郁障碍', label: '抑郁障碍' },
    { value: '青少年心理', label: '青少年心理' },
    { value: '婚姻家庭', label: '婚姻家庭' },
    { value: '职场心理', label: '职场心理' },
    { value: '创伤治疗', label: '创伤治疗' },
    { value: '成瘾治疗', label: '成瘾治疗' },
    { value: '人格障碍', label: '人格障碍' },
    { value: '儿童心理', label: '儿童心理' },
    { value: '老年心理', label: '老年心理' },
    { value: '综合咨询', label: '综合咨询' }
  ]

  useEffect(() => {
    fetchCounselors()
  }, [])

  useEffect(() => {
    filterCounselors()
  }, [counselors, searchText, selectedSpecialization])

  const fetchCounselors = async () => {
    try {
      setLoading(true)
      console.log('开始获取咨询师列表...')
      const response = await counselorApi.getAllCounselors()
      console.log('API响应:', response)

      if (response.success && response.data) {
        console.log('设置咨询师数据:', response.data)
        setCounselors(response.data)
      } else {
        console.error('API返回错误:', response.message)
        message.error(response.message || '获取咨询师列表失败')
      }
    } catch (error) {
      console.error('获取咨询师列表失败:', error)
      message.error('获取咨询师列表失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  const parseSpecializations = (specializations: string): string[] => {
    if (!specializations) return []
    try {
      return JSON.parse(specializations)
    } catch {
      return specializations.split(',').map(s => s.trim()).filter(s => s)
    }
  }

  const parseTags = (tags: string): string[] => {
    if (!tags) return []
    try {
      return JSON.parse(tags)
    } catch {
      return tags.split(',').map(s => s.trim()).filter(s => s)
    }
  }

  const filterCounselors = () => {
    let filtered = counselors

    // 按专业领域筛选
    if (selectedSpecialization) {
      filtered = filtered.filter(counselor => {
        const specs = parseSpecializations(counselor.specializations)
        return specs.includes(selectedSpecialization)
      })
    }

    // 按姓名搜索
    if (searchText) {
      const searchLower = searchText.toLowerCase()
      filtered = filtered.filter(counselor =>
        counselor.name.toLowerCase().includes(searchLower) ||
        counselor.title?.toLowerCase().includes(searchLower) ||
        counselor.description?.toLowerCase().includes(searchLower)
      )
    }

    console.log('筛选结果:', {
      原始数量: counselors.length,
      筛选后数量: filtered.length,
      搜索词: searchText,
      专业领域: selectedSpecialization
    })
    setFilteredCounselors(filtered)
  }

  const handleConsultation = (counselor: Counselor) => {
    message.info(`即将与 ${counselor.name} 开始咨询`)
    // TODO: 跳转到聊天页面
  }

  return (
    <div className="counselors-container">
      {/* 页面标题 */}
      <div className="counselors-hero">
        <div className="counselors-hero-content">
          <SafetyCertificateOutlined className="hero-icon" />
          <Title level={1} className="hero-title">
            专业心理咨询师团队
          </Title>
          <Paragraph className="hero-subtitle">
            汇聚国内顶尖心理健康专家，为您提供专业、温暖、有效的心理咨询服务
          </Paragraph>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="counselors-main-content">
        {/* 搜索和筛选 */}
        <Card className="search-card">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={16}>
              <Search
                placeholder="搜索咨询师姓名、专业领域..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
            </Col>
            <Col xs={24} md={8}>
              <Select
                placeholder="选择专业领域"
                allowClear
                size="large"
                style={{ width: '100%' }}
                value={selectedSpecialization}
                onChange={setSelectedSpecialization}
              >
                {specializations.map(spec => (
                  <Option key={spec.value} value={spec.value}>
                    {spec.label}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>
        </Card>

        {/* 咨询师列表 */}
        <Spin spinning={loading} size="large">
          {filteredCounselors.length === 0 && !loading ? (
            <div className="empty-state">
              <Empty
                description="暂无符合条件的咨询师"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          ) : (
            <Row gutter={[24, 24]} className="counselors-grid">
              {filteredCounselors.map(counselor => (
                <Col xs={24} sm={12} md={8} lg={8} xl={8} key={counselor.id}>
                  <Card
                    hoverable
                    className="counselor-card"
                    cover={
                      <div className="counselor-cover">
                        {counselor.photo ? (
                          <img
                            src={counselor.photo}
                            alt={counselor.name}
                            className="counselor-photo"
                          />
                        ) : (
                          <div className="counselor-avatar-container">
                            <Avatar
                              size={80}
                              src={counselor.avatar}
                              icon={<UserOutlined />}
                              style={{ border: '4px solid white' }}
                            />
                          </div>
                        )}
                        <div className={`status-badge ${counselor.isAvailable ? 'status-online' : 'status-offline'}`}>
                          {counselor.isAvailable ? '在线' : '离线'}
                        </div>
                      </div>
                    }
                  >
                    <div className="counselor-info">
                      {/* 基本信息 */}
                      <div className="counselor-header">
                        <Title level={4} className="counselor-name">
                          {counselor.name}
                        </Title>
                        <Text type="secondary" className="counselor-title">
                          {counselor.title}
                        </Text>
                      </div>

                      {/* 评分 */}
                      <div className="rating-section">
                        <Rate
                          disabled
                          defaultValue={counselor.rating}
                          allowHalf
                          className="counselor-rating"
                        />
                        <Text type="secondary" className="rating-text">
                          ({counselor.rating})
                        </Text>
                      </div>

                      {/* 统计信息 */}
                      <div className="stats-section">
                        <div className="stat-item">
                          <ClockCircleOutlined className="stat-icon" />
                          <span>{counselor.experience}年经验</span>
                        </div>
                        <div className="stat-item">
                          <TeamOutlined className="stat-icon" />
                          <span>{counselor.consultationCount}次咨询</span>
                        </div>
                      </div>

                      {/* 专业领域 */}
                      <div className="specializations">
                        {parseSpecializations(counselor.specializations).slice(0, 2).map((spec, index) => (
                          <Tag
                            key={index}
                            color="blue"
                            className="spec-tag"
                          >
                            {spec}
                          </Tag>
                        ))}
                      </div>

                      {/* 描述 */}
                      <Paragraph
                        ellipsis={{ rows: 2 }}
                        className="counselor-description"
                      >
                        {counselor.description}
                      </Paragraph>

                      <Divider className="card-divider" />

                      {/* 咨询按钮 */}
                      <Button
                        type="primary"
                        size="large"
                        block
                        icon={<MessageOutlined />}
                        onClick={() => handleConsultation(counselor)}
                        disabled={!counselor.isAvailable}
                        className="consult-button"
                      >
                        {counselor.isAvailable ? '立即咨询' : '暂不可用'}
                      </Button>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Spin>
      </div>
    </div>
  )
}

export default Counselors

@echo off
chcp 65001 > nul
echo ================================
echo Buwan Psychology Platform Setup
echo ================================

echo.
echo Database Config: localhost:3336, root/123456
echo Redis Config: localhost:6379, no password
echo.

echo [1/4] Checking MySQL service...
mysql -h localhost -P 3336 -u root -p123456 -e "SELECT VERSION();" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Cannot connect to MySQL!
    echo Please check:
    echo - MySQL service is running
    echo - Port 3336 is correct
    echo - Username/password: root/123456
    pause
    exit /b 1
)
echo MySQL service OK

echo.
echo [2/4] Checking/Initializing database...
mysql -h localhost -P 3336 -u root -p123456 -e "USE buwan_psychology; SELECT 'Database exists' as status;" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Database not found, initializing...
    mysql -h localhost -P 3336 -u root -p123456 < docs/database.sql
    if %ERRORLEVEL% EQU 0 (
        echo Database initialized successfully!
        echo Default admin: <EMAIL> / admin123
    ) else (
        echo ERROR: Database initialization failed!
        pause
        exit /b 1
    )
) else (
    echo Database already exists
)

echo.
echo [3/4] Starting backend service...
cd backend
echo Installing dependencies and starting Spring Boot...
start "Buwan Backend" cmd /k "mvn spring-boot:run"

echo.
echo Waiting for backend to start...
timeout /t 15 /nobreak > nul

echo.
echo [4/4] Starting frontend service...
cd ..\frontend

echo Checking if port 3000 is available...
netstat -ano | findstr :3000 > nul
if %ERRORLEVEL% EQU 0 (
    echo Port 3000 is in use, killing existing processes...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
        taskkill /F /PID %%a > nul 2>&1
    )
    timeout /t 2 /nobreak > nul
)

if not exist node_modules (
    echo Installing frontend dependencies...
    call npm install
)
echo Starting React development server...
start "Buwan Frontend" cmd /k "npm run dev"

echo.
echo ================================
echo Setup Complete!
echo ================================
echo Backend API: http://localhost:8080
echo Frontend App: http://localhost:3000
echo Admin Login: <EMAIL> / admin123
echo ================================
echo.
echo Both services are starting in separate windows.
echo Close this window when done.
echo.
pause

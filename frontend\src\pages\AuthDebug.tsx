import React from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { Card, Typography, Space, Button } from 'antd'

const { Title, Text, Paragraph } = Typography

const AuthDebug: React.FC = () => {
  const { user, token, isAuthenticated, loading, error } = useSelector((state: RootState) => state.auth)

  const checkLocalStorage = () => {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    
    console.log('LocalStorage token:', storedToken)
    console.log('LocalStorage user:', storedUser)
    console.log('Redux state:', { user, token, isAuthenticated, loading, error })
  }

  const clearStorage = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    window.location.reload()
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>认证状态调试</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="Redux 状态">
          <Space direction="vertical">
            <Text><strong>isAuthenticated:</strong> {isAuthenticated ? '✅ true' : '❌ false'}</Text>
            <Text><strong>loading:</strong> {loading ? 'true' : 'false'}</Text>
            <Text><strong>error:</strong> {error || 'null'}</Text>
            <Text><strong>token:</strong> {token ? `${token.substring(0, 20)}...` : 'null'}</Text>
            <Text><strong>user:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</Text>
          </Space>
        </Card>

        <Card title="LocalStorage 状态">
          <Space direction="vertical">
            <Text><strong>token:</strong> {localStorage.getItem('token') ? `${localStorage.getItem('token')!.substring(0, 20)}...` : 'null'}</Text>
            <Text><strong>user:</strong> {localStorage.getItem('user') || 'null'}</Text>
          </Space>
        </Card>

        <Card title="调试工具">
          <Space>
            <Button onClick={checkLocalStorage}>检查控制台日志</Button>
            <Button danger onClick={clearStorage}>清除存储并刷新</Button>
          </Space>
        </Card>

        <Card title="用户信息详情">
          {user ? (
            <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
              {JSON.stringify(user, null, 2)}
            </pre>
          ) : (
            <Text type="secondary">无用户信息</Text>
          )}
        </Card>
      </Space>
    </div>
  )
}

export default AuthDebug

# 不晚心理平台 API 文档

## 概述

不晚心理平台提供RESTful API，支持用户管理、AI心理咨询、心理评估等功能。

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Token
- **数据格式**: JSON

## 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

## 认证相关 API

### 用户注册

**POST** `/users/register`

请求体：
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "测试用户",
  "age": 25,
  "gender": 1
}
```

响应：
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "userType": 0,
    "status": 0,
    "createTime": "2024-01-01T10:00:00"
  }
}
```

### 用户登录

**POST** `/users/login`

请求体：
```json
{
  "usernameOrEmail": "testuser",
  "password": "password123"
}
```

响应：
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

## 用户管理 API

### 获取用户信息

**GET** `/users/{id}`

响应：
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "age": 25,
    "gender": 1,
    "userType": 0,
    "status": 0
  }
}
```

### 更新用户信息

**PUT** `/users/{id}`

请求体：
```json
{
  "nickname": "新昵称",
  "age": 26,
  "bio": "个人简介"
}
```

### 修改密码

**POST** `/users/{id}/change-password`

请求体：
```json
{
  "oldPassword": "oldpassword",
  "newPassword": "newpassword"
}
```

## 心理咨询 API

### 获取咨询会话列表

**GET** `/chat/sessions?userId={userId}`

响应：
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "userId": 1,
      "title": "焦虑问题咨询",
      "sessionType": 1,
      "status": 0,
      "startTime": "2024-01-01T10:00:00",
      "createTime": "2024-01-01T10:00:00"
    }
  ]
}
```

### 创建咨询会话

**POST** `/chat/sessions`

请求体：
```json
{
  "userId": 1,
  "title": "新的咨询会话",
  "sessionType": 0
}
```

### 获取会话消息

**GET** `/chat/sessions/{sessionId}/messages`

响应：
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "sessionId": 1,
      "userId": 1,
      "content": "我最近感到很焦虑",
      "messageType": 0,
      "createTime": "2024-01-01T10:00:00"
    },
    {
      "id": 2,
      "sessionId": 1,
      "userId": 1,
      "content": "我理解您的感受，焦虑是很常见的情绪反应...",
      "messageType": 1,
      "createTime": "2024-01-01T10:01:00"
    }
  ]
}
```

### 发送消息

**POST** `/chat/sessions/{sessionId}/messages`

请求体：
```json
{
  "content": "我想了解如何缓解焦虑"
}
```

## 心理评估 API

### 获取评估列表

**GET** `/assessments`

### 提交评估

**POST** `/assessments`

请求体：
```json
{
  "assessmentType": "anxiety",
  "answers": [
    {"questionId": 1, "answer": 3},
    {"questionId": 2, "answer": 2}
  ]
}
```

### 获取评估结果

**GET** `/assessments/results?userId={userId}`

## 管理员 API

### 获取青少年用户

**GET** `/users/teenagers`

### 获取活跃用户

**GET** `/users/active?days=30`

### 禁用/启用用户

**POST** `/users/{id}/disable`
**POST** `/users/{id}/enable`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证说明

大部分API需要在请求头中携带JWT Token：

```
Authorization: Bearer {token}
```

Token在用户登录成功后获得，有效期为24小时。

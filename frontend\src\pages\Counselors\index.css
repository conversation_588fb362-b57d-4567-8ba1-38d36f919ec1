/* 专家团队页面容器 */
.counselors-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 英雄区域 */
.counselors-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 0;
  margin-bottom: 40px;
  text-align: center;
  color: white;
}

.counselors-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.hero-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.hero-title {
  color: white !important;
  margin-bottom: 8px !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 16px !important;
  margin: 0 !important;
  line-height: 1.6 !important;
}

/* 主要内容区域 */
.counselors-main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 咨询师网格 */
.counselors-grid {
  margin-bottom: 40px;
  display: flex;
  flex-wrap: wrap;
}

.counselors-grid .ant-row {
  width: 100%;
  margin: 0 !important;
}

.counselors-grid .ant-col {
  margin-bottom: 24px;
}

/* 咨询师卡片 */
.counselor-card {
  height: 100%;
  min-height: 420px;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
}

.counselor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

/* 卡片封面 */
.counselor-cover {
  height: 160px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.counselor-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.counselor-avatar-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 状态徽章 */
.status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-online {
  background-color: #52c41a;
}

.status-offline {
  background-color: #d9d9d9;
}

/* 咨询师信息 */
.counselor-info {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.counselor-header {
  text-align: center;
  margin-bottom: 16px;
}

.counselor-name {
  margin: 0 0 4px 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #333 !important;
}

.counselor-title {
  font-size: 14px !important;
  color: #666 !important;
  margin: 0 !important;
}

/* 评分区域 */
.rating-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.counselor-rating {
  font-size: 14px;
}

.rating-text {
  font-size: 12px !important;
  color: #666 !important;
}

/* 统计信息 */
.stats-section {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.stat-icon {
  color: #1890ff;
  font-size: 14px;
}

/* 专业领域标签 */
.specializations {
  margin-bottom: 16px;
  text-align: center;
  min-height: 24px;
}

.spec-tag {
  margin: 2px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 描述文本 */
.counselor-description {
  font-size: 12px !important;
  color: #666 !important;
  line-height: 1.4 !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

/* 分割线 */
.card-divider {
  margin: 12px 0 !important;
}

/* 咨询按钮 */
.consult-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  height: 40px !important;
  margin-top: auto !important;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .counselors-main-content {
    padding: 0 20px;
  }
  
  .counselors-hero-content {
    padding: 0 20px;
  }
  
  .hero-title {
    font-size: 2.2rem !important;
  }
}

@media (max-width: 768px) {
  .counselors-main-content {
    padding: 0 16px;
  }
  
  .counselors-hero-content {
    padding: 0 16px;
  }
  
  .counselors-hero {
    padding: 40px 0;
    margin-bottom: 24px;
  }
  
  .hero-title {
    font-size: 1.8rem !important;
  }
  
  .hero-subtitle {
    font-size: 14px !important;
  }
  
  .counselor-card {
    min-height: 450px;
  }
  
  .counselor-cover {
    height: 160px;
  }
  
  .counselor-info {
    padding: 16px;
  }
  
  .counselor-name {
    font-size: 16px !important;
  }
  
  .counselor-title {
    font-size: 13px !important;
  }
  
  .stats-section {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .counselors-main-content {
    padding: 0 12px;
  }
  
  .counselors-hero-content {
    padding: 0 12px;
  }
  
  .counselors-hero {
    padding: 32px 0;
    margin-bottom: 20px;
  }
  
  .hero-title {
    font-size: 1.5rem !important;
  }
  
  .hero-subtitle {
    font-size: 13px !important;
  }
  
  .counselor-cover {
    height: 140px;
  }
  
  .counselor-info {
    padding: 12px;
  }
  
  .counselor-name {
    font-size: 15px !important;
  }
  
  .counselor-title {
    font-size: 12px !important;
  }
  
  .stats-section {
    padding: 8px;
  }
  
  .stat-item {
    font-size: 11px;
  }
  
  .spec-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 加载状态 */
.ant-spin-container {
  min-height: 300px;
}

/* 搜索框样式 */
.ant-input-search .ant-input-group .ant-input-group-addon .ant-btn {
  border-radius: 0 6px 6px 0;
}

.ant-input-search .ant-input {
  border-radius: 6px 0 0 6px;
}

/* 选择框样式 */
.ant-select-selector {
  border-radius: 6px !important;
}

/* 按钮样式 */
.ant-btn-primary {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

/* 标签样式 */
.ant-tag {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.ant-tag-blue {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* 评分样式 */
.ant-rate {
  font-size: 12px;
}

.ant-rate-star {
  margin-right: 2px;
}

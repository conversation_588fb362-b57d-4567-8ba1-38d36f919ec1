-- 插入用户详细资料测试数据
-- 为现有用户添加详细的个人信息

-- 为用户ID 1 添加详细资料
INSERT INTO user_profiles (
    user_id, real_name, birth_date, marital_status, education, occupation, income,
    province, city, district,
    emergency_contact_name, emergency_contact_relationship, emergency_contact_phone,
    medical_history, psychological_history, current_medications, allergies,
    smoking, drinking, exercise_frequency, sleep_duration,
    personality_traits, interests, stress_factors, coping_methods,
    goals, additional_info,
    show_real_name, show_age, show_location, allow_data_sharing,
    create_time, update_time
) VALUES (
    1, '张三', '1995-06-15', 'single', 'bachelor', '软件工程师', '10000-20000',
    '北京市', '北京市', '朝阳区',
    '张父', '父亲', '139****9999',
    '["无重大疾病史"]', '["曾有轻度焦虑"]', '无', '花粉过敏',
    false, false, '每周2-3次', '7-8小时',
    '["内向", "理性", "完美主义"]', '["阅读", "编程", "音乐"]', 
    '["工作压力", "人际关系"]', '["运动", "听音乐", "与朋友聊天"]',
    '改善工作压力，提高情绪管理能力', '希望能够更好地处理工作中的压力和焦虑情绪。',
    true, true, true, false,
    NOW(), NOW()
);

-- 为用户ID 2 添加详细资料（如果存在）
INSERT INTO user_profiles (
    user_id, real_name, birth_date, marital_status, education, occupation, income,
    province, city, district,
    emergency_contact_name, emergency_contact_relationship, emergency_contact_phone,
    medical_history, psychological_history, current_medications, allergies,
    smoking, drinking, exercise_frequency, sleep_duration,
    personality_traits, interests, stress_factors, coping_methods,
    goals, additional_info,
    show_real_name, show_age, show_location, allow_data_sharing,
    create_time, update_time
) 
SELECT 
    2, '李四', '1992-03-20', 'married', 'master', '产品经理', '15000-25000',
    '上海市', '上海市', '浦东新区',
    '李母', '母亲', '138****8888',
    '["无重大疾病史"]', '["无心理疾病史"]', '无', '无',
    false, true, '每周1-2次', '6-7小时',
    '["外向", "感性", "乐观"]', '["旅行", "摄影", "美食"]', 
    '["工作压力", "家庭关系"]', '["旅行", "与家人聊天", "看电影"]',
    '平衡工作与家庭生活，减少焦虑情绪', '希望能够更好地处理工作与家庭的平衡问题。',
    true, true, true, false,
    NOW(), NOW()
WHERE EXISTS (SELECT 1 FROM users WHERE id = 2);

-- 为用户ID 3 添加详细资料（如果存在）
INSERT INTO user_profiles (
    user_id, real_name, birth_date, marital_status, education, occupation, income,
    province, city, district,
    emergency_contact_name, emergency_contact_relationship, emergency_contact_phone,
    medical_history, psychological_history, current_medications, allergies,
    smoking, drinking, exercise_frequency, sleep_duration,
    personality_traits, interests, stress_factors, coping_methods,
    goals, additional_info,
    show_real_name, show_age, show_location, allow_data_sharing,
    create_time, update_time
) 
SELECT 
    3, '王五', '1988-11-08', 'divorced', 'college', '销售经理', '8000-15000',
    '广东省', '深圳市', '南山区',
    '王姐', '姐姐', '137****7777',
    '["高血压"]', '["曾有抑郁症"]', '降压药', '海鲜过敏',
    true, false, '很少运动', '5-6小时',
    '["敏感", "完美主义", "内向"]', '["读书", "听音乐", "园艺"]', 
    '["经济压力", "健康问题", "人际关系"]', '["冥想", "阅读", "听音乐"]',
    '改善情绪管理，建立更好的人际关系', '经历了离婚，希望能够重新建立对生活的信心。',
    false, false, true, true,
    NOW(), NOW()
WHERE EXISTS (SELECT 1 FROM users WHERE id = 3);

-- 为测试用户添加详细资料
INSERT INTO user_profiles (
    user_id, real_name, birth_date, marital_status, education, occupation, income,
    province, city, district,
    emergency_contact_name, emergency_contact_relationship, emergency_contact_phone,
    medical_history, psychological_history, current_medications, allergies,
    smoking, drinking, exercise_frequency, sleep_duration,
    personality_traits, interests, stress_factors, coping_methods,
    goals, additional_info,
    show_real_name, show_age, show_location, allow_data_sharing,
    create_time, update_time
) 
SELECT 
    u.id, '测试用户', '1990-01-01', 'single', 'bachelor', '测试工程师', '5000-10000',
    '北京市', '北京市', '海淀区',
    '测试联系人', '朋友', '136****6666',
    '["无重大疾病史"]', '["无心理疾病史"]', '无', '无',
    false, false, '每周3-4次', '8-9小时',
    '["理性", "乐观"]', '["测试", "学习"]', 
    '["学习压力"]', '["运动", "学习"]',
    '提高测试技能，保持心理健康', '这是一个测试用户的资料。',
    true, true, true, false,
    NOW(), NOW()
FROM users u 
WHERE u.username LIKE 'test%' OR u.email LIKE 'test%'
AND NOT EXISTS (SELECT 1 FROM user_profiles WHERE user_id = u.id);

-- 为其他没有详细资料的用户添加基础资料
INSERT INTO user_profiles (
    user_id, real_name, birth_date, marital_status, education, occupation, income,
    province, city, district,
    medical_history, psychological_history, current_medications, allergies,
    smoking, drinking, exercise_frequency, sleep_duration,
    personality_traits, interests, stress_factors, coping_methods,
    goals, additional_info,
    show_real_name, show_age, show_location, allow_data_sharing,
    create_time, update_time
) 
SELECT 
    u.id, u.username, '1990-01-01', 'single', 'bachelor', '未填写', '未填写',
    '未填写', '未填写', '未填写',
    '["无"]', '["无"]', '无', '无',
    false, false, '未填写', '未填写',
    '["未填写"]', '["未填写"]', 
    '["未填写"]', '["未填写"]',
    '完善个人信息', '请完善您的个人资料以获得更好的咨询服务。',
    true, true, true, false,
    NOW(), NOW()
FROM users u 
WHERE NOT EXISTS (SELECT 1 FROM user_profiles WHERE user_id = u.id);

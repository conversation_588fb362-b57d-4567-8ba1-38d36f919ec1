package com.buwan.psychology.controller;

import com.buwan.psychology.entity.User;
import com.buwan.psychology.service.UserService;
import com.buwan.psychology.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class UserController {

    private final UserService userService;
    private final JwtUtil jwtUtil;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<?> register(@Valid @RequestBody User user) {
        try {
            User registeredUser = userService.register(user);
            // 不返回密码
            registeredUser.setPassword(null);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "注册成功",
                "data", registeredUser
            ));
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String usernameOrEmail = loginRequest.get("usernameOrEmail");
            String password = loginRequest.get("password");
            
            return userService.authenticate(usernameOrEmail, password)
                .map(user -> {
                    // 生成JWT token
                    String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getEmail());

                    // 不返回密码
                    user.setPassword(null);

                    // 构建响应数据
                    Map<String, Object> responseData = new HashMap<>();
                    responseData.put("user", user);
                    responseData.put("token", token);

                    return ResponseEntity.ok(Map.of(
                        "success", true,
                        "message", "登录成功",
                        "data", responseData
                    ));
                })
                .orElse(ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "用户名或密码错误"
                )));
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "登录失败"
            ));
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getUserById(@PathVariable Long id) {
        try {
            return userService.findById(id)
                .map(user -> {
                    user.setPassword(null);
                    return ResponseEntity.ok(Map.of(
                        "success", true,
                        "data", user
                    ));
                })
                .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取用户信息失败"
            ));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateUser(@PathVariable Long id, @RequestBody User user) {
        try {
            user.setId(id);
            User updatedUser = userService.updateUser(user);
            updatedUser.setPassword(null);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "用户信息更新成功",
                "data", updatedUser
            ));
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/{id}/change-password")
    public ResponseEntity<?> changePassword(@PathVariable Long id, @RequestBody Map<String, String> passwordRequest) {
        try {
            String oldPassword = passwordRequest.get("oldPassword");
            String newPassword = passwordRequest.get("newPassword");
            
            userService.changePassword(id, oldPassword, newPassword);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "密码修改成功"
            ));
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 获取青少年用户列表（管理员功能）
     */
    @GetMapping("/teenagers")
    public ResponseEntity<?> getTeenageUsers() {
        try {
            List<User> teenageUsers = userService.getTeenageUsers();
            // 移除密码信息
            teenageUsers.forEach(user -> user.setPassword(null));
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", teenageUsers
            ));
        } catch (Exception e) {
            log.error("获取青少年用户列表失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取用户列表失败"
            ));
        }
    }

    /**
     * 获取活跃用户列表（管理员功能）
     */
    @GetMapping("/active")
    public ResponseEntity<?> getActiveUsers(@RequestParam(defaultValue = "30") int days) {
        try {
            List<User> activeUsers = userService.getActiveUsers(days);
            // 移除密码信息
            activeUsers.forEach(user -> user.setPassword(null));
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", activeUsers
            ));
        } catch (Exception e) {
            log.error("获取活跃用户列表失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取用户列表失败"
            ));
        }
    }

    /**
     * 禁用用户（管理员功能）
     */
    @PostMapping("/{id}/disable")
    public ResponseEntity<?> disableUser(@PathVariable Long id) {
        try {
            userService.disableUser(id);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "用户已禁用"
            ));
        } catch (Exception e) {
            log.error("禁用用户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 启用用户（管理员功能）
     */
    @PostMapping("/{id}/enable")
    public ResponseEntity<?> enableUser(@PathVariable Long id) {
        try {
            userService.enableUser(id);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "用户已启用"
            ));
        } catch (Exception e) {
            log.error("启用用户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }
}

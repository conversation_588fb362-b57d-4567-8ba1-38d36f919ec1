import React from 'react'
import MDEditor from '@uiw/react-md-editor'
import './index.css'

interface MarkdownRendererProps {
  content: string
  className?: string
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className = '' }) => {
  // 清理和预处理内容
  const cleanContent = React.useMemo(() => {
    if (!content) return ''
    
    // 移除可能的HTML实体
    let cleaned = content
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
    
    // 确保换行符正确
    cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
    
    return cleaned
  }, [content])

  return (
    <div className={`markdown-renderer ${className}`} data-color-mode="light">
      <MDEditor.Markdown 
        source={cleanContent}
        style={{ 
          backgroundColor: 'transparent',
          color: 'inherit',
          fontSize: 'inherit',
          lineHeight: 'inherit'
        }}
      />
    </div>
  )
}

export default MarkdownRenderer 
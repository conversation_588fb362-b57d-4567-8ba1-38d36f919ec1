Stack trace:
Frame         Function      Args
0007FFFFABC0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFABC0, 0007FFFF9AC0) msys-2.0.dll+0x1FE8E
0007FFFFABC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE98) msys-2.0.dll+0x67F9
0007FFFFABC0  000210046832 (000210286019, 0007FFFFAA78, 0007FFFFABC0, 000000000000) msys-2.0.dll+0x6832
0007FFFFABC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABC0  000210068E24 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEA0  00021006A225 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE62850000 ntdll.dll
7FFE60C70000 KERNEL32.DLL
7FFE601C0000 KERNELBASE.dll
7FFE60AC0000 USER32.dll
7FFE60070000 win32u.dll
7FFE60FE0000 GDI32.dll
7FFE600A0000 gdi32full.dll
7FFE5FD00000 msvcp_win.dll
7FFE5FB60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE610B0000 advapi32.dll
7FFE616B0000 msvcrt.dll
7FFE61DE0000 sechost.dll
7FFE5FF80000 bcrypt.dll
7FFE617B0000 RPCRT4.dll
7FFE5F290000 CRYPTBASE.DLL
7FFE5FC80000 bcryptPrimitives.dll
7FFE60980000 IMM32.DLL

package com.buwan.psychology.repository;

import com.buwan.psychology.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
    Optional<User> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 根据用户类型查找用户
     */
    List<User> findByUserType(Integer userType);

    /**
     * 查找青少年用户
     */
    @Query("SELECT u FROM User u WHERE u.userType = 1")
    List<User> findTeenageUsers();

    /**
     * 根据状态查找用户
     */
    List<User> findByStatus(Integer status);

    /**
     * 查找活跃用户（最近登录）
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime >= :since ORDER BY u.lastLoginTime DESC")
    List<User> findActiveUsers(@Param("since") LocalDateTime since);

    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = 0")
    long countActiveUsers();

    /**
     * 统计青少年用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.userType = 1 AND u.status = 0")
    long countTeenageUsers();

    /**
     * 根据年龄范围查找用户
     */
    @Query("SELECT u FROM User u WHERE u.age BETWEEN :minAge AND :maxAge AND u.status = 0")
    List<User> findByAgeRange(@Param("minAge") Integer minAge, @Param("maxAge") Integer maxAge);
}

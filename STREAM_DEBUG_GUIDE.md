# 流式响应调试指南

## 当前状态分析

根据日志显示，流式响应功能基本正常，但需要进一步优化和调试。

### 日志分析
```
2025-07-27 02:05:08 [reactor-http-nio-4] INFO  c.b.psychology.service.ChatService - 流式回复生成完成，会话ID: 7
2025-07-27 02:05:08 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/chat/sessions/7/messages/stream
```

这表明：
1. ✅ 流式响应服务正在工作
2. ✅ 安全配置允许访问流式端点
3. ✅ 响应生成完成

## 调试步骤

### 1. 启用详细日志
在 `application.yml` 中已配置详细日志：
```yaml
logging:
  level:
    com.buwan.psychology.service.GeminiService: DEBUG
    com.buwan.psychology.controller.ChatController: DEBUG
    org.springframework.web.reactive: DEBUG
    reactor.netty: DEBUG
```

### 2. 使用模拟模式测试
如果Gemini API有问题，可以启用模拟模式：
```bash
# 设置环境变量
export GEMINI_MOCK_ENABLED=true

# 或在application.yml中设置
gemini:
  mock:
    enabled: true
```

### 3. 前端调试
打开浏览器开发者工具，查看：
- **Network标签**: 检查SSE连接状态
- **Console标签**: 查看JavaScript日志
- **Application标签**: 检查EventSource连接

### 4. 测试流程

#### 4.1 基础连接测试
```bash
# 使用curl测试流式端点
curl -X POST http://localhost:8080/api/chat/sessions/7/messages/stream \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"content":"你好","userId":1}' \
  --no-buffer
```

#### 4.2 前端测试
1. 打开 http://localhost:3000/chat
2. 创建或选择会话
3. 发送消息
4. 观察流式响应效果

## 常见问题排查

### 问题1: 流式响应不显示
**症状**: 消息发送后没有实时显示
**排查**:
```javascript
// 在浏览器控制台检查
console.log('EventSource支持:', typeof EventSource !== 'undefined');
```

**解决方案**:
- 检查浏览器是否支持EventSource
- 确认网络连接正常
- 查看控制台错误信息

### 问题2: Gemini API调用失败
**症状**: 日志显示API调用错误
**排查**:
```bash
# 检查API密钥
echo $GEMINI_API_KEY

# 测试API连接
curl -H "x-goog-api-key: $GEMINI_API_KEY" \
  "https://generativelanguage.googleapis.com/v1beta/models"
```

**解决方案**:
- 验证API密钥正确性
- 检查网络连接
- 启用模拟模式进行测试

### 问题3: 安全配置阻止访问
**症状**: 403 Forbidden错误
**解决方案**:
确认SecurityConfig中包含：
```java
.requestMatchers("/api/chat/**").permitAll()
```

### 问题4: CORS问题
**症状**: 跨域请求被阻止
**解决方案**:
检查CORS配置：
```java
configuration.setAllowedOriginPatterns(Arrays.asList(
    "http://localhost:3000",
    "http://localhost:5173"
));
```

## 性能监控

### 1. 后端监控
```bash
# 查看实时日志
tail -f backend/logs/buwan-psychology.log | grep -E "(流式|Stream|Gemini)"

# 监控内存使用
jps -l | grep psychology
jstat -gc [PID] 1s
```

### 2. 前端监控
```javascript
// 在浏览器控制台监控
const originalLog = console.log;
console.log = function(...args) {
    if (args.some(arg => typeof arg === 'string' && arg.includes('流式'))) {
        originalLog.apply(console, ['[STREAM]', ...args]);
    }
    originalLog.apply(console, args);
};
```

## 优化建议

### 1. 后端优化
- 实现连接池管理
- 添加请求限流
- 优化内存使用

### 2. 前端优化
- 实现断线重连
- 添加加载状态指示
- 优化大量文本渲染

### 3. 网络优化
- 启用gzip压缩
- 配置CDN加速
- 优化SSE连接

## 测试用例

### 1. 基础功能测试
```javascript
// 测试消息发送
const testMessage = "请用Markdown格式回复一个包含代码块、表格和列表的示例";

// 测试附件上传
const testWithImage = {
    content: "请分析这张图片",
    attachments: [/* base64图片数据 */]
};
```

### 2. 压力测试
```bash
# 并发测试
for i in {1..10}; do
    curl -X POST http://localhost:8080/api/chat/sessions/7/messages/stream \
      -H "Content-Type: application/json" \
      -d '{"content":"测试消息'$i'","userId":1}' &
done
```

### 3. 长时间运行测试
```javascript
// 连续发送消息测试
let messageCount = 0;
const sendTestMessage = () => {
    messageCount++;
    // 发送消息逻辑
    console.log(`发送第${messageCount}条测试消息`);
    
    if (messageCount < 100) {
        setTimeout(sendTestMessage, 5000); // 每5秒发送一条
    }
};
```

## 故障恢复

### 1. 服务重启
```bash
# 重启后端服务
./mvnw spring-boot:run

# 重启前端服务
npm run dev
```

### 2. 清理缓存
```bash
# 清理浏览器缓存
# Chrome: Ctrl+Shift+Delete

# 清理后端缓存
rm -rf backend/target/
./mvnw clean compile
```

### 3. 数据库检查
```sql
-- 检查最近的消息记录
SELECT * FROM chat_messages 
WHERE create_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY create_time DESC;

-- 检查会话状态
SELECT * FROM consultation_sessions 
WHERE id = 7;
```

## 联系支持

如果问题持续存在，请提供：
1. 完整的错误日志
2. 浏览器控制台截图
3. 网络请求详情
4. 系统环境信息

---

**注意**: 在生产环境中，请确保关闭详细日志以避免性能影响。

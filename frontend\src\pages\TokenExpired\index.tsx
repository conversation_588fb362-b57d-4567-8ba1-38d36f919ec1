import React, { useEffect } from 'react'
import { Card, Button, Typography, Space } from 'antd'
import { ExclamationCircleOutlined, LoginOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { clearAuth } from '@/utils/auth'

const { Title, Paragraph } = Typography

const TokenExpired: React.FC = () => {
  const navigate = useNavigate()

  useEffect(() => {
    // 清除过期的认证信息
    clearAuth()
  }, [])

  const handleGoToLogin = () => {
    navigate('/login')
  }

  const handleGoToHome = () => {
    navigate('/')
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card 
        style={{ 
          maxWidth: '500px', 
          width: '100%',
          textAlign: 'center',
          borderRadius: '16px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <ExclamationCircleOutlined 
            style={{ 
              fontSize: '64px', 
              color: '#faad14',
              marginBottom: '16px'
            }} 
          />
          
          <Title level={2} style={{ margin: 0, color: '#1a1a1a' }}>
            登录已过期
          </Title>
          
          <Paragraph style={{ fontSize: '16px', color: '#666', margin: 0 }}>
            为了保护您的账户安全，您的登录状态已过期。
            <br />
            请重新登录以继续使用服务。
          </Paragraph>
          
          <Space size="middle" style={{ marginTop: '24px' }}>
            <Button 
              type="primary" 
              size="large"
              icon={<LoginOutlined />}
              onClick={handleGoToLogin}
              style={{
                borderRadius: '8px',
                height: '48px',
                padding: '0 32px',
                fontSize: '16px'
              }}
            >
              重新登录
            </Button>
            
            <Button 
              size="large"
              onClick={handleGoToHome}
              style={{
                borderRadius: '8px',
                height: '48px',
                padding: '0 32px',
                fontSize: '16px'
              }}
            >
              返回首页
            </Button>
          </Space>
        </Space>
      </Card>
    </div>
  )
}

export default TokenExpired

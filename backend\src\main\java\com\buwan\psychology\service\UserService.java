package com.buwan.psychology.service;

import com.buwan.psychology.entity.User;
import com.buwan.psychology.entity.UserProfile;
import com.buwan.psychology.repository.UserRepository;
import com.buwan.psychology.repository.UserProfileRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserProfileRepository userProfileRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 用户注册
     */
    public User register(User user) {
        // 检查用户名和邮箱是否已存在
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        // 明文密码（开发环境）
        // user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认值
        if (user.getUserType() == null) {
            user.setUserType(0); // 默认普通用户
        }
        if (user.getStatus() == null) {
            user.setStatus(0); // 默认正常状态
        }

        // 根据年龄自动判断是否为青少年用户
        if (user.getAge() != null && user.getAge() < 18) {
            user.setUserType(1); // 青少年用户
        }

        User savedUser = userRepository.save(user);
        log.info("用户注册成功: {}", savedUser.getUsername());

        // 同步创建用户详细资料
        createInitialUserProfile(savedUser);

        return savedUser;
    }

    /**
     * 用户登录验证
     */
    public Optional<User> authenticate(String usernameOrEmail, String password) {
        Optional<User> userOpt = userRepository.findByUsernameOrEmail(usernameOrEmail);
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (password.equals(user.getPassword()) && user.isEnabled()) {
                // 更新最后登录时间
                user.setLastLoginTime(LocalDateTime.now());
                userRepository.save(user);
                log.info("用户登录成功: {}", user.getUsername());
                return Optional.of(user);
            }
        }
        
        log.warn("用户登录失败: {}", usernameOrEmail);
        return Optional.empty();
    }

    /**
     * 根据ID查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 根据用户名查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 更新用户信息
     */
    public User updateUser(User user) {
        User existingUser = userRepository.findById(user.getId())
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 更新允许修改的字段
        if (user.getNickname() != null) {
            existingUser.setNickname(user.getNickname());
        }
        if (user.getAvatar() != null) {
            existingUser.setAvatar(user.getAvatar());
        }
        if (user.getGender() != null) {
            existingUser.setGender(user.getGender());
        }
        if (user.getAge() != null) {
            existingUser.setAge(user.getAge());
            // 根据年龄更新用户类型
            if (user.getAge() < 18 && existingUser.getUserType() == 0) {
                existingUser.setUserType(1);
            }
        }
        if (user.getPhone() != null) {
            existingUser.setPhone(user.getPhone());
        }
        if (user.getBio() != null) {
            existingUser.setBio(user.getBio());
        }

        User updatedUser = userRepository.save(existingUser);
        log.info("用户信息更新成功: {}", updatedUser.getUsername());
        return updatedUser;
    }

    /**
     * 修改密码
     */
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!oldPassword.equals(user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        user.setPassword(newPassword);
        userRepository.save(user);
        log.info("用户密码修改成功: {}", user.getUsername());
    }

    /**
     * 获取青少年用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getTeenageUsers() {
        return userRepository.findTeenageUsers();
    }

    /**
     * 获取活跃用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getActiveUsers(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return userRepository.findActiveUsers(since);
    }

    /**
     * 禁用用户
     */
    public void disableUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        user.setStatus(1);
        userRepository.save(user);
        log.info("用户已禁用: {}", user.getUsername());
    }

    /**
     * 启用用户
     */
    public void enableUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        user.setStatus(0);
        userRepository.save(user);
        log.info("用户已启用: {}", user.getUsername());
    }

    /**
     * 创建初始用户详细资料
     */
    private void createInitialUserProfile(User user) {
        try {
            UserProfile profile = new UserProfile();
            profile.setUserId(user.getId());

            // 从User实体中复制基本信息到UserProfile，避免重复字段
            if (user.getNickname() != null && !user.getNickname().trim().isEmpty()) {
                profile.setRealName(user.getNickname());
            }

            // 根据年龄计算出生日期（大概值）
            if (user.getAge() != null && user.getAge() > 0) {
                int currentYear = java.time.LocalDate.now().getYear();
                int birthYear = currentYear - user.getAge();
                profile.setBirthDate(java.time.LocalDate.of(birthYear, 1, 1));
            }

            // 注意：不在UserProfile中重复存储email、phone等字段
            // 这些信息从User实体中获取，避免数据冗余

            // 设置默认的隐私设置
            profile.setShowRealName(true);
            profile.setShowAge(true);
            profile.setShowLocation(true);
            profile.setAllowDataSharing(false);

            // 设置默认的生活方式
            profile.setSmoking(false);
            profile.setDrinking(false);

            // 设置默认的JSON字段
            profile.setMedicalHistory("[\"无重大疾病史\"]");
            profile.setPsychologicalHistory("[\"无心理疾病史\"]");
            profile.setPersonalityTraits("[\"待完善\"]");
            profile.setInterests("[\"待完善\"]");
            profile.setStressFactors("[\"待完善\"]");
            profile.setCopingMethods("[\"待完善\"]");

            // 设置默认目标
            profile.setGoals("完善个人信息，获得更好的咨询服务");
            profile.setAdditionalInfo("请完善您的个人资料以获得更个性化的心理咨询服务。");

            userProfileRepository.save(profile);
            log.info("为用户 {} 创建初始详细资料成功", user.getUsername());

        } catch (Exception e) {
            log.error("为用户 {} 创建初始详细资料失败", user.getUsername(), e);
            // 不抛出异常，避免影响用户注册流程
        }
    }
}

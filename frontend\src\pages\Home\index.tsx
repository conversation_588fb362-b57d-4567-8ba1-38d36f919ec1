import React from 'react'
import { But<PERSON>, Card, Row, Col, Typo<PERSON>, Space } from 'antd'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { 
  MessageOutlined, 
  HeartOutlined, 
  UserOutlined, 
  SafetyOutlined,
  RocketOutlined,
  TeamOutlined 
} from '@ant-design/icons'
import { RootState } from '@/store'
import './index.css'

const { Title, Paragraph } = Typography

const Home: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated } = useSelector((state: RootState) => state.auth)

  const features = [
    {
      icon: <MessageOutlined className="feature-icon" />,
      title: 'AI智能咨询',
      description: '24/7在线AI心理咨询师，随时为您提供专业的心理支持和建议'
    },
    {
      icon: <HeartOutlined className="feature-icon" />,
      title: '心理健康评估',
      description: '科学的心理测评工具，帮助您了解自己的心理健康状况'
    },
    {
      icon: <UserOutlined className="feature-icon" />,
      title: '青少年专项',
      description: '专门针对青少年心理特点设计的服务，关注成长中的心理健康'
    },
    {
      icon: <SafetyOutlined className="feature-icon" />,
      title: '隐私保护',
      description: '严格的隐私保护措施，确保您的个人信息和咨询内容安全'
    },
    {
      icon: <RocketOutlined className="feature-icon" />,
      title: '个性化方案',
      description: '基于AI分析为您量身定制的心理健康改善方案'
    },
    {
      icon: <TeamOutlined className="feature-icon" />,
      title: '专业团队',
      description: '由资深心理学专家指导的AI系统，确保服务的专业性'
    }
  ]

  const handleStartChat = () => {
    if (isAuthenticated) {
      navigate('/chat')
    } else {
      navigate('/login')
    }
  }

  return (
    <div className="home-container">
      {/* 英雄区域 */}
      <div className="hero-section">
        <div className="hero-content">
          <Title level={1} className="hero-title">
            不晚心理
          </Title>
          <Title level={2} className="hero-subtitle">
            AI驱动的心理健康平台
          </Title>
          <Paragraph className="hero-description">
            专业的心理咨询服务，24小时陪伴您的心理健康之旅。
            无论是焦虑、抑郁还是生活压力，我们都在这里为您提供支持。
          </Paragraph>
          <Space size="large" className="hero-actions">
            <Button
              size="large"
              onClick={handleStartChat}
              className="hero-btn-primary"
            >
              开始心理咨询
            </Button>
            <Button
              size="large"
              onClick={() => navigate('/assessment')}
              className="hero-btn-secondary"
            >
              心理健康评估
            </Button>
          </Space>
        </div>
      </div>

      {/* 特色功能 */}
      <div className="features-section">
        <div className="container">
          <Title level={2} className="section-title">
            为什么选择不晚心理？
          </Title>
          <Row gutter={[24, 24]}>
            {features.map((feature, index) => (
              <Col xs={24} sm={12} lg={8} key={index}>
                <Card className="feature-card" hoverable>
                  <div className="feature-content">
                    {feature.icon}
                    <Title level={4} className="feature-title">
                      {feature.title}
                    </Title>
                    <Paragraph className="feature-description">
                      {feature.description}
                    </Paragraph>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 统计数据 */}
      <div className="stats-section">
        <div className="container">
          <Row gutter={[24, 24]} className="stats-row">
            <Col xs={12} sm={6}>
              <div className="stat-item">
                <div className="stat-number">10,000+</div>
                <div className="stat-label">服务用户</div>
              </div>
            </Col>
            <Col xs={12} sm={6}>
              <div className="stat-item">
                <div className="stat-number">50,000+</div>
                <div className="stat-label">咨询会话</div>
              </div>
            </Col>
            <Col xs={12} sm={6}>
              <div className="stat-item">
                <div className="stat-number">24/7</div>
                <div className="stat-label">在线服务</div>
              </div>
            </Col>
            <Col xs={12} sm={6}>
              <div className="stat-item">
                <div className="stat-number">98%</div>
                <div className="stat-label">满意度</div>
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* 行动号召 */}
      <div className="cta-section">
        <div className="container">
          <div className="cta-content">
            <Title level={2} className="cta-title">
              开始您的心理健康之旅
            </Title>
            <Paragraph className="cta-description">
              心理健康同样重要，不要等到明天。现在就开始关爱自己的内心世界。
            </Paragraph>
            <Button
              size="large"
              onClick={handleStartChat}
              className="cta-button"
            >
              立即开始咨询
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home

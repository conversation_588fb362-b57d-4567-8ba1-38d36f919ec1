import React, { useEffect, useState } from 'react'
import { Modal, Button, Typography } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

const { Title, Paragraph } = Typography

interface TokenExpiredModalProps {
  visible: boolean
  onClose: () => void
}

const TokenExpiredModal: React.FC<TokenExpiredModalProps> = ({ visible, onClose }) => {
  const navigate = useNavigate()
  const [countdown, setCountdown] = useState(10)

  useEffect(() => {
    if (visible && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (visible && countdown === 0) {
      handleGoToLogin()
    }
  }, [visible, countdown])

  const handleGoToLogin = () => {
    onClose()
    navigate('/login')
  }

  return (
    <Modal
      open={visible}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ExclamationCircleOutlined style={{ color: '#faad14' }} />
          <span>登录已过期</span>
        </div>
      }
      footer={[
        <Button key="login" type="primary" onClick={handleGoToLogin}>
          立即登录
        </Button>
      ]}
      closable={false}
      maskClosable={false}
      centered
    >
      <div style={{ padding: '20px 0' }}>
        <Paragraph>
          您的登录状态已过期，为了保护您的账户安全，请重新登录。
        </Paragraph>
        <Paragraph type="secondary">
          {countdown > 0 ? `${countdown}秒后自动跳转到登录页面` : '正在跳转...'}
        </Paragraph>
      </div>
    </Modal>
  )
}

export default TokenExpiredModal

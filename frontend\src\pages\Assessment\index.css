/* 心理评估页面样式 */
.assessment-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 英雄区域 */
.assessment-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 0;
  margin-bottom: 40px;
  text-align: center;
  color: white;
}

.assessment-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.hero-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.hero-title {
  color: white !important;
  margin-bottom: 8px !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 16px !important;
  margin: 0 !important;
  line-height: 1.6 !important;
}

/* 主要内容区域 */
.assessment-main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 分类卡片 */
.category-card {
  margin-bottom: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.category-tab {
  border-radius: 20px !important;
  height: 36px !important;
  padding: 0 16px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.category-tab.active {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.category-count {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.8;
}

/* 评估网格 */
.assessments-grid {
  margin-bottom: 40px;
}

.assessments-grid .ant-col {
  margin-bottom: 24px;
}

/* 评估卡片 */
.assessment-card {
  height: 100%;
  min-height: 380px;
  transition: all 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
}

.assessment-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

.assessment-card .ant-card-body {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.assessment-card .ant-card-actions {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 卡片头部 */
.assessment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.assessment-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.assessment-badges {
  display: flex;
  gap: 4px;
}

.badge {
  font-size: 10px !important;
  padding: 2px 6px !important;
  border-radius: 8px !important;
  margin: 0 !important;
}

/* 卡片内容 */
.assessment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.assessment-title {
  color: #1a1a1a !important;
  margin-bottom: 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
}

.assessment-description {
  color: #666 !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  margin-bottom: 16px !important;
  flex: 1;
}

/* 标签 */
.assessment-tags {
  margin-bottom: 16px;
  min-height: 24px;
}

.assessment-tag {
  font-size: 11px !important;
  padding: 2px 8px !important;
  border-radius: 10px !important;
  margin: 2px 4px 2px 0 !important;
  background: #f0f2f5 !important;
  color: #666 !important;
  border: none !important;
}

/* 元信息 */
.assessment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.meta-icon {
  color: #1890ff;
  font-size: 14px;
}

/* 评估类型 */
.assessment-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-count {
  font-size: 12px !important;
}

/* 开始按钮 */
.start-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  height: 36px !important;
  width: 100% !important;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .assessment-main-content {
    padding: 0 20px;
  }
  
  .assessment-hero-content {
    padding: 0 20px;
  }
  
  .hero-title {
    font-size: 2.2rem !important;
  }
}

@media (max-width: 768px) {
  .assessment-main-content {
    padding: 0 16px;
  }
  
  .assessment-hero-content {
    padding: 0 16px;
  }
  
  .assessment-hero {
    padding: 40px 0;
    margin-bottom: 24px;
  }
  
  .hero-title {
    font-size: 1.8rem !important;
  }
  
  .hero-subtitle {
    font-size: 14px !important;
  }
  
  .category-tabs {
    justify-content: center;
  }
  
  .category-tab {
    font-size: 12px !important;
    height: 32px !important;
    padding: 0 12px !important;
  }
  
  .assessment-card {
    min-height: 350px;
  }
  
  .assessment-card .ant-card-body {
    padding: 16px;
  }
  
  .assessment-title {
    font-size: 15px !important;
  }
  
  .assessment-description {
    font-size: 12px !important;
  }
}

@media (max-width: 576px) {
  .assessment-main-content {
    padding: 0 12px;
  }
  
  .assessment-hero-content {
    padding: 0 12px;
  }
  
  .assessment-hero {
    padding: 32px 0;
    margin-bottom: 20px;
  }
  
  .hero-title {
    font-size: 1.5rem !important;
  }
  
  .hero-subtitle {
    font-size: 13px !important;
  }
  
  .category-tabs {
    gap: 8px;
  }
  
  .category-tab {
    font-size: 11px !important;
    height: 28px !important;
    padding: 0 10px !important;
  }
  
  .assessment-card {
    min-height: 320px;
  }
  
  .assessment-card .ant-card-body {
    padding: 12px;
  }
  
  .assessment-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .assessment-title {
    font-size: 14px !important;
  }
  
  .assessment-description {
    font-size: 11px !important;
  }
  
  .assessment-tag {
    font-size: 10px !important;
    padding: 1px 6px !important;
  }
  
  .meta-item {
    font-size: 11px;
  }
}

/* 加载状态 */
.ant-spin-container {
  min-height: 300px;
}

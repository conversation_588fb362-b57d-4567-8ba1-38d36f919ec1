package com.buwan.psychology.service;

import com.buwan.psychology.dto.gemini.GeminiRequest;
import com.buwan.psychology.dto.gemini.GeminiResponse;
import com.buwan.psychology.entity.ChatMessage;
import com.buwan.psychology.entity.Counselor;
import com.buwan.psychology.entity.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeminiService {

    private final WebClient geminiWebClient;
    private final ObjectMapper objectMapper;

    @Value("${gemini.model:gemini-2.5-flash}")
    private String model;

    private static final String SYSTEM_PROMPT = """
            你是一位专业的心理咨询师，名叫小晚。你具有以下特质：
            
            1. 专业性：具备心理学专业知识，能够识别和理解各种心理问题
            2. 同理心：能够理解和感受来访者的情感状态
            3. 耐心：给予来访者充分的时间表达自己
            4. 保密性：严格保护来访者的隐私
            5. 非评判性：不对来访者的想法和行为进行道德评判
            
            请用温暖、专业的语气回应来访者，提供有建设性的建议和支持。
            如果遇到严重的心理危机，请建议寻求专业医疗帮助。
            """;

    /**
     * 生成AI回复 - 非流式
     */
    public Mono<String> generateResponse(List<ChatMessage> conversationHistory, String userMessage, List<ChatMessage.Attachment> attachments) {
        try {
            GeminiRequest request = buildGeminiRequest(conversationHistory, userMessage, attachments, false);
            
            return geminiWebClient
                    .post()
                    .uri("/v1beta/models/{model}:generateContent", model)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(GeminiResponse.class)
                    .map(this::extractTextFromResponse)
                    .doOnError(error -> log.error("Gemini API调用失败", error))
                    .onErrorReturn("抱歉，我现在无法回复您的消息，请稍后再试。");
                    
        } catch (Exception e) {
            log.error("构建Gemini请求失败", e);
            return Mono.just("抱歉，处理您的消息时出现了问题。");
        }
    }

    /**
     * 生成AI回复 - 流式（带咨询师和用户信息）
     */
    public Flux<String> generateStreamResponse(List<ChatMessage> conversationHistory, String userMessage,
                                             List<ChatMessage.Attachment> attachments, Counselor counselor, User user, Integer sessionType) {
        return generateStreamResponse(conversationHistory, userMessage, attachments, counselor, user, sessionType, null, null);
    }

    /**
     * 生成AI回复 - 流式（带会话信息）
     */
    public Flux<String> generateStreamResponse(List<ChatMessage> conversationHistory, String userMessage,
                                             List<ChatMessage.Attachment> attachments, Counselor counselor, User user,
                                             Integer sessionType, String sessionTitle, String sessionDescription) {
        try {
            GeminiRequest request = buildGeminiRequest(conversationHistory, userMessage, attachments, true,
                    counselor, user, sessionType, sessionTitle, sessionDescription);

            log.info("开始调用Gemini流式API，模型: {}, 咨询师: {}, 会话: {}",
                    model, counselor != null ? counselor.getName() : "默认", sessionTitle);

            return geminiWebClient
                    .post()
                    .uri("/v1beta/models/{model}:streamGenerateContent", model)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToFlux(String.class)
                    .doOnNext(chunk -> log.debug("收到Gemini响应块: {}", chunk.length() > 100 ? chunk.substring(0, 100) + "..." : chunk))
                    .map(this::parseStreamResponse)
                    .filter(text -> text != null && !text.trim().isEmpty())
                    .doOnComplete(() -> log.info("Gemini流式响应完成"))
                    .doOnError(error -> log.error("Gemini流式API调用失败", error))
                    .onErrorResume(error -> {
                        log.error("Gemini API调用出错，返回错误消息", error);
                        return Flux.just("抱歉，我现在无法回复您的消息，请稍后再试。可能的原因：" + error.getMessage());
                    });

        } catch (Exception e) {
            log.error("构建Gemini流式请求失败", e);
            return Flux.just("抱歉，我无法生成回复。错误详情：" + e.getMessage());
        }
    }

    /**
     * 生成AI回复 - 流式（兼容旧版本）
     */
    public Flux<String> generateStreamResponse(List<ChatMessage> conversationHistory, String userMessage, List<ChatMessage.Attachment> attachments) {
        try {
            GeminiRequest request = buildGeminiRequest(conversationHistory, userMessage, attachments, true);

            log.info("开始调用Gemini流式API，模型: {}", model);

            return geminiWebClient
                    .post()
                    .uri("/v1beta/models/{model}:streamGenerateContent", model)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToFlux(String.class)
                    .doOnNext(chunk -> log.debug("收到Gemini响应块: {}", chunk.length() > 100 ? chunk.substring(0, 100) + "..." : chunk))
                    .map(this::parseStreamResponse)
                    .filter(text -> text != null && !text.trim().isEmpty())
                    .doOnComplete(() -> log.info("Gemini流式响应完成"))
                    .doOnError(error -> log.error("Gemini流式API调用失败", error))
                    .onErrorResume(error -> {
                        log.error("Gemini API调用出错，返回错误消息", error);
                        return Flux.just("抱歉，我现在无法回复您的消息，请稍后再试。可能的原因：" + error.getMessage());
                    });

        } catch (Exception e) {
            log.error("构建Gemini流式请求失败", e);
            return Flux.just("抱歉，处理您的消息时出现了问题：" + e.getMessage());
        }
    }

    /**
     * 构建Gemini请求（带咨询师和用户信息）
     */
    private GeminiRequest buildGeminiRequest(List<ChatMessage> conversationHistory, String userMessage,
                                           List<ChatMessage.Attachment> attachments, boolean isStream,
                                           Counselor counselor, User user, Integer sessionType) {
        return buildGeminiRequest(conversationHistory, userMessage, attachments, isStream,
                counselor, user, sessionType, null, null);
    }

    /**
     * 构建Gemini请求（带会话信息）
     */
    private GeminiRequest buildGeminiRequest(List<ChatMessage> conversationHistory, String userMessage,
                                           List<ChatMessage.Attachment> attachments, boolean isStream,
                                           Counselor counselor, User user, Integer sessionType,
                                           String sessionTitle, String sessionDescription) {
        List<GeminiRequest.Content> contents = new ArrayList<>();

        // 构建动态系统提示
        String dynamicPrompt = buildDynamicPrompt(counselor, user, sessionType, sessionTitle, sessionDescription);
        contents.add(GeminiRequest.Content.builder()
                .parts(Arrays.asList(GeminiRequest.Part.builder()
                        .text(dynamicPrompt)
                        .build()))
                .role("user")
                .build());

        // 添加历史对话（最近10轮）
        int historyStart = Math.max(0, conversationHistory.size() - 20); // 最近10轮对话（用户+AI各10条）
        for (int i = historyStart; i < conversationHistory.size(); i++) {
            ChatMessage msg = conversationHistory.get(i);
            String role = msg.getMessageType() == 0 ? "user" : "model";

            List<GeminiRequest.Part> parts = new ArrayList<>();

            // 添加文本内容
            if (msg.getContent() != null && !msg.getContent().trim().isEmpty()) {
                parts.add(GeminiRequest.Part.builder()
                        .text(msg.getContent())
                        .build());
            }

            // 添加附件（仅用户消息）
            if (msg.getMessageType() == 0 && msg.getAttachments() != null) {
                for (ChatMessage.Attachment attachment : msg.getAttachments()) {
                    if (attachment.getType().equals("image")) {
                        parts.add(GeminiRequest.Part.builder()
                                .inlineData(GeminiRequest.InlineData.builder()
                                        .mimeType(getMimeTypeFromBase64(attachment.getBase64()))
                                        .data(extractBase64Data(attachment.getBase64()))
                                        .build())
                                .build());
                    }
                }
            }

            if (!parts.isEmpty()) {
                contents.add(GeminiRequest.Content.builder()
                        .parts(parts)
                        .role(role)
                        .build());
            }
        }

        // 添加当前用户消息
        List<GeminiRequest.Part> currentParts = new ArrayList<>();
        currentParts.add(GeminiRequest.Part.builder()
                .text(userMessage)
                .build());

        // 添加当前消息的附件
        if (attachments != null) {
            for (ChatMessage.Attachment attachment : attachments) {
                if (attachment.getType().equals("image")) {
                    currentParts.add(GeminiRequest.Part.builder()
                            .inlineData(GeminiRequest.InlineData.builder()
                                    .mimeType(getMimeTypeFromBase64(attachment.getBase64()))
                                    .data(extractBase64Data(attachment.getBase64()))
                                    .build())
                            .build());
                }
            }
        }

        contents.add(GeminiRequest.Content.builder()
                .parts(currentParts)
                .role("user")
                .build());

        return GeminiRequest.builder()
                .contents(contents)
                .generationConfig(GeminiRequest.GenerationConfig.builder()
                        .temperature(0.7)
                        .topK(40)
                        .topP(0.95)
                        .maxOutputTokens(2048)
                        .build())
                .build();
    }

    /**
     * 构建动态系统提示（兼容旧版本）
     */
    private String buildDynamicPrompt(Counselor counselor, User user, Integer sessionType) {
        return buildDynamicPrompt(counselor, user, sessionType, null, null);
    }

    /**
     * 构建动态系统提示（带会话信息）
     */
    private String buildDynamicPrompt(Counselor counselor, User user, Integer sessionType,
                                    String sessionTitle, String sessionDescription) {
        StringBuilder prompt = new StringBuilder();

        // 基础角色设定
        if (counselor != null && counselor.getRolePrompt() != null && !counselor.getRolePrompt().trim().isEmpty()) {
            // 使用咨询师的自定义角色设定
            prompt.append(counselor.getRolePrompt());
        } else {
            // 使用默认角色设定
            String counselorName = counselor != null ? counselor.getName() : "小晚";
            prompt.append("你是一位专业的心理咨询师，名叫").append(counselorName).append("。");

            if (counselor != null) {
                if (counselor.getTitle() != null) {
                    prompt.append("你的职业资格是").append(counselor.getTitle()).append("。");
                }
                if (counselor.getExperience() != null) {
                    prompt.append("你有").append(counselor.getExperience()).append("年的从业经验。");
                }
                if (counselor.getSpecializations() != null) {
                    prompt.append("你的专业领域包括").append(counselor.getSpecializations()).append("。");
                }
                if (counselor.getPhilosophy() != null) {
                    prompt.append("你的咨询理念是：").append(counselor.getPhilosophy()).append("。");
                }
            }
        }

        prompt.append("\n\n");

        // 用户信息
        if (user != null) {
            prompt.append("来访者信息：\n");
            if (user.getUsername() != null) {
                prompt.append("- 姓名：").append(user.getUsername()).append("\n");
            }
            if (user.getAge() != null) {
                prompt.append("- 年龄：").append(user.getAge()).append("岁\n");
            }
            if (user.getGender() != null) {
                prompt.append("- 性别：").append(user.getGender()).append("\n");
            }

            prompt.append("\n");
        }

        // 咨询类型信息
        if (sessionType != null) {
            String sessionTypeDesc = getSessionTypeDescription(sessionType);
            prompt.append("本次咨询类型：").append(sessionTypeDesc).append("\n\n");
        }

        // 会话信息
        if (sessionTitle != null || sessionDescription != null) {
            prompt.append("本次咨询会话信息：\n");
            if (sessionTitle != null && !sessionTitle.trim().isEmpty()) {
                prompt.append("- 咨询主题：").append(sessionTitle).append("\n");
            }
            if (sessionDescription != null && !sessionDescription.trim().isEmpty()) {
                prompt.append("- 详细情况：").append(sessionDescription).append("\n");
            }
            prompt.append("\n");
        }

        // 专业要求
        prompt.append("""
                请遵循以下专业原则：
                1. 专业性：运用心理学专业知识，识别和理解各种心理问题
                2. 同理心：理解和感受来访者的情感状态
                3. 耐心：给予来访者充分的时间表达自己
                4. 保密性：严格保护来访者的隐私
                5. 非评判性：不对来访者的想法和行为进行道德评判
                6. 建设性：提供有建设性的建议和支持

                请用温暖、专业的语气回应来访者。如果遇到严重的心理危机，请建议寻求专业医疗帮助。
                """);

        return prompt.toString();
    }

    /**
     * 获取会话类型描述
     */
    private String getSessionTypeDescription(Integer sessionType) {
        return switch (sessionType) {
            case 1 -> "焦虑问题咨询";
            case 2 -> "抑郁问题咨询";
            case 3 -> "学习压力咨询";
            case 4 -> "人际关系咨询";
            case 5 -> "其他心理问题咨询";
            default -> "一般心理咨询";
        };
    }

    /**
     * 构建Gemini请求（兼容旧版本）
     */
    private GeminiRequest buildGeminiRequest(List<ChatMessage> conversationHistory, String userMessage,
                                           List<ChatMessage.Attachment> attachments, boolean isStream) {
        List<GeminiRequest.Content> contents = new ArrayList<>();
        
        // 添加系统提示
        contents.add(GeminiRequest.Content.builder()
                .parts(Arrays.asList(GeminiRequest.Part.builder()
                        .text(SYSTEM_PROMPT)
                        .build()))
                .role("user")
                .build());

        // 添加历史对话（最近10轮）
        int historyStart = Math.max(0, conversationHistory.size() - 20); // 最近10轮对话（用户+AI各10条）
        for (int i = historyStart; i < conversationHistory.size(); i++) {
            ChatMessage msg = conversationHistory.get(i);
            String role = msg.getMessageType() == 0 ? "user" : "model";
            
            List<GeminiRequest.Part> parts = new ArrayList<>();
            
            // 添加文本内容
            if (msg.getContent() != null && !msg.getContent().trim().isEmpty()) {
                parts.add(GeminiRequest.Part.builder()
                        .text(msg.getContent())
                        .build());
            }
            
            // 添加附件（仅用户消息）
            if (msg.getMessageType() == 0 && msg.getAttachments() != null) {
                for (ChatMessage.Attachment attachment : msg.getAttachments()) {
                    if (attachment.getType().equals("image")) {
                        parts.add(GeminiRequest.Part.builder()
                                .inlineData(GeminiRequest.InlineData.builder()
                                        .mimeType(getMimeTypeFromBase64(attachment.getBase64()))
                                        .data(extractBase64Data(attachment.getBase64()))
                                        .build())
                                .build());
                    }
                }
            }
            
            if (!parts.isEmpty()) {
                contents.add(GeminiRequest.Content.builder()
                        .parts(parts)
                        .role(role)
                        .build());
            }
        }

        // 添加当前用户消息
        List<GeminiRequest.Part> currentParts = new ArrayList<>();
        
        // 添加文本
        if (userMessage != null && !userMessage.trim().isEmpty()) {
            currentParts.add(GeminiRequest.Part.builder()
                    .text(userMessage)
                    .build());
        }
        
        // 添加附件
        if (attachments != null) {
            for (ChatMessage.Attachment attachment : attachments) {
                if (attachment.getType().equals("image")) {
                    currentParts.add(GeminiRequest.Part.builder()
                            .inlineData(GeminiRequest.InlineData.builder()
                                    .mimeType(getMimeTypeFromBase64(attachment.getBase64()))
                                    .data(extractBase64Data(attachment.getBase64()))
                                    .build())
                            .build());
                }
            }
        }
        
        contents.add(GeminiRequest.Content.builder()
                .parts(currentParts)
                .role("user")
                .build());

        // 生成配置
        GeminiRequest.GenerationConfig config = GeminiRequest.GenerationConfig.builder()
                .temperature(0.7)
                .topK(40)
                .topP(0.95)
                .maxOutputTokens(2048)
                .build();

        // 安全设置
        List<GeminiRequest.SafetySetting> safetySettings = Arrays.asList(
                GeminiRequest.SafetySetting.builder()
                        .category("HARM_CATEGORY_HARASSMENT")
                        .threshold("BLOCK_MEDIUM_AND_ABOVE")
                        .build(),
                GeminiRequest.SafetySetting.builder()
                        .category("HARM_CATEGORY_HATE_SPEECH")
                        .threshold("BLOCK_MEDIUM_AND_ABOVE")
                        .build(),
                GeminiRequest.SafetySetting.builder()
                        .category("HARM_CATEGORY_SEXUALLY_EXPLICIT")
                        .threshold("BLOCK_MEDIUM_AND_ABOVE")
                        .build(),
                GeminiRequest.SafetySetting.builder()
                        .category("HARM_CATEGORY_DANGEROUS_CONTENT")
                        .threshold("BLOCK_MEDIUM_AND_ABOVE")
                        .build()
        );

        return GeminiRequest.builder()
                .contents(contents)
                .generationConfig(config)
                .safetySettings(safetySettings)
                .build();
    }

    /**
     * 从响应中提取文本
     */
    private String extractTextFromResponse(GeminiResponse response) {
        if (response.getCandidates() != null && !response.getCandidates().isEmpty()) {
            GeminiResponse.Candidate candidate = response.getCandidates().get(0);
            if (candidate.getContent() != null && candidate.getContent().getParts() != null) {
                String extractedText = candidate.getContent().getParts().stream()
                        .map(GeminiResponse.Part::getText)
                        .filter(text -> text != null && !text.trim().isEmpty())
                        .findFirst()
                        .orElse(null);
                
                if (extractedText != null) {
                    return extractedText;
                } else {
                    log.warn("候选回复中没有找到有效文本内容，候选数量: {}", response.getCandidates().size());
                }
            } else {
                log.warn("候选回复内容为空，候选数量: {}", response.getCandidates().size());
            }
        } else {
            log.warn("响应中没有候选回复");
        }
        return "抱歉，我无法生成回复。";
    }

    /**
     * 解析流式响应
     */
    private String parseStreamResponse(String chunk) {
        try {
            // 处理不同格式的响应
            String jsonData = chunk.trim();

            // 处理Server-Sent Events格式
            if (jsonData.startsWith("data: ")) {
                jsonData = jsonData.substring(6).trim();
            }

            // 跳过空数据或结束标记
            if (jsonData.isEmpty() || jsonData.equals("[DONE]")) {
                return "";
            }

            // 尝试解析JSON
            try {
                GeminiResponse response = objectMapper.readValue(jsonData, GeminiResponse.class);
                String text = extractTextFromResponse(response);
                if (text != null && !text.trim().isEmpty()) {
                    log.debug("解析到文本: {}", text.length() > 50 ? text.substring(0, 50) + "..." : text);
                }
                return text;
            } catch (JsonProcessingException e) {
                // 如果不是JSON格式，可能是纯文本响应
                log.debug("非JSON格式响应，尝试作为纯文本处理: {}", jsonData.length() > 100 ? jsonData.substring(0, 100) + "..." : jsonData);

                // 检查是否包含文本内容
                if (jsonData.contains("\"text\"")) {
                    // 简单的文本提取
                    int textStart = jsonData.indexOf("\"text\":\"") + 8;
                    int textEnd = jsonData.indexOf("\"", textStart);
                    if (textStart > 7 && textEnd > textStart) {
                        String extractedText = jsonData.substring(textStart, textEnd);
                        // 处理转义字符
                        extractedText = extractedText.replace("\\n", "\n")
                                                   .replace("\\t", "\t")
                                                   .replace("\\\"", "\"")
                                                   .replace("\\\\", "\\");
                        return extractedText;
                    }
                }

                return "";
            }
        } catch (Exception e) {
            log.warn("解析流式响应失败: {}", chunk.length() > 200 ? chunk.substring(0, 200) + "..." : chunk, e);
            return "";
        }
    }

    /**
     * 从base64字符串中提取MIME类型
     */
    private String getMimeTypeFromBase64(String base64) {
        if (base64.startsWith("data:")) {
            Pattern pattern = Pattern.compile("data:([^;]+);base64,");
            Matcher matcher = pattern.matcher(base64);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        return "image/jpeg"; // 默认类型
    }

    /**
     * 从base64字符串中提取纯数据部分
     */
    private String extractBase64Data(String base64) {
        if (base64.startsWith("data:")) {
            int commaIndex = base64.indexOf(",");
            if (commaIndex != -1) {
                return base64.substring(commaIndex + 1);
            }
        }
        return base64;
    }
}

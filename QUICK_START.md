# 🚀 不晚心理平台快速启动指南

## 📋 环境要求

### 必需软件
- ✅ Java 17+ 
- ✅ Maven 3.6+
- ✅ Node.js 18+
- ✅ MySQL 8.0+ (已配置: localhost:3336, root/123456)
- ✅ Redis 6.0+ (已配置: localhost:6379, 无密码)

### 检查环境
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查Node.js版本
node -version

# 检查MySQL服务
mysql -h localhost -P 3336 -u root -p123456 -e "SELECT VERSION();"

# 检查Redis服务
redis-cli ping
```

## 🚀 一键启动

### 启动应用（自动处理所有步骤）
```bash
# Windows - 双击运行或命令行执行
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

启动脚本会自动：
1. ✅ 检查环境（Java, Maven, Node.js, MySQL, Redis）
2. ✅ 验证数据库连接
3. ✅ 初始化数据库（如果不存在）
4. ✅ 启动后端服务（Spring Boot）
5. ✅ 安装前端依赖（如果需要）
6. ✅ 启动前端服务（React）

### 停止服务
```bash
# Windows
stop.bat

# Linux/Mac
chmod +x stop.sh
./stop.sh
```

### 手动启动（可选）
如果需要单独启动某个服务：

#### 仅启动后端
```bash
cd backend
mvn spring-boot:run
```

#### 仅启动前端
```bash
cd frontend
npm install
npm run dev
```

## 🌐 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html (开发中)

## 👤 默认账户

### 管理员账户
- **用户名**: admin
- **邮箱**: <EMAIL>  
- **密码**: admin123

### 测试用户
可以通过前端注册页面创建新用户进行测试。

## 🔧 配置说明

### 数据库配置
- **主机**: localhost:3336
- **用户名**: root
- **密码**: 123456
- **生产库**: buwan_psychology
- **开发库**: buwan_psychology_dev

### Redis配置
- **主机**: localhost:6379
- **密码**: 无
- **数据库**: 0

### AI配置（可选）
如需使用AI功能，请设置环境变量：
```bash
export AI_API_KEY="your-openai-api-key"
export AI_BASE_URL="https://api.openai.com/v1"
```

## 🐛 常见问题

### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
# Windows
net start mysql

# Linux
sudo systemctl status mysql
sudo systemctl start mysql

# Mac
brew services start mysql
```

### 2. Redis连接失败
```bash
# 检查Redis服务状态
# Windows
redis-server

# Linux
sudo systemctl status redis
sudo systemctl start redis

# Mac
brew services start redis
```

### 3. 端口被占用
```bash
# 查看端口占用
# Windows
netstat -ano | findstr :8080
netstat -ano | findstr :3000

# Linux/Mac
lsof -i :8080
lsof -i :3000

# 杀死进程
# Windows
taskkill /PID <PID> /F

# Linux/Mac
kill -9 <PID>
```

### 4. Maven依赖下载失败
```bash
# 清理并重新下载依赖
cd backend
mvn clean
mvn dependency:resolve
```

### 5. npm安装失败
```bash
# 清理缓存并重新安装
cd frontend
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## 📝 开发提示

### 热重载
- 后端：修改代码后自动重启（Spring Boot DevTools）
- 前端：修改代码后自动刷新（Vite HMR）

### 日志查看
```bash
# 后端日志
tail -f backend.log

# 前端日志  
tail -f frontend.log

# 应用日志
tail -f logs/buwan-psychology.log
```

### 数据库管理
推荐使用以下工具管理数据库：
- MySQL Workbench
- phpMyAdmin
- DBeaver
- Navicat

## 🎯 下一步

1. **测试基础功能**
   - 用户注册/登录
   - 基础页面导航

2. **开发核心功能**
   - 完善聊天界面
   - 集成AI API
   - 实现心理评估

3. **部署上线**
   - 参考 [部署指南](docs/DEPLOYMENT.md)

## 🆘 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 检查日志文件
3. 参考 [API文档](docs/API.md)
4. 查看 [部署指南](docs/DEPLOYMENT.md)

---

**祝您开发愉快！** 🎉

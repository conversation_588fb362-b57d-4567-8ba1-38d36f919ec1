/* 首页样式 */
.home-container {
  min-height: calc(100vh - 64px - 70px);
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  color: white;
  padding: 120px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 2rem;
  font-weight: 400;
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.9);
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 40px;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  margin-top: 40px;
}

/* 英雄区域按钮基础样式 */
.hero-actions .hero-btn-primary,
.hero-actions .hero-btn-secondary {
  height: 56px !important;
  padding: 0 32px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  border-radius: 28px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  border: none !important;
  min-width: 180px;
}

/* 主要按钮样式 */
.hero-actions .hero-btn-primary {
  background: rgba(255, 255, 255, 0.95) !important;
  color: #6366f1 !important;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(255, 255, 255, 0.3) !important;
}

.hero-actions .hero-btn-primary:hover,
.hero-actions .hero-btn-primary:focus {
  background: rgba(255, 255, 255, 1) !important;
  color: #5b5ff5 !important;
  transform: translateY(-4px) scale(1.02) !important;
  box-shadow: 0 16px 48px rgba(255, 255, 255, 0.4) !important;
}

/* 次要按钮样式 */
.hero-actions .hero-btn-secondary {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.hero-actions .hero-btn-secondary:hover,
.hero-actions .hero-btn-secondary:focus {
  background: rgba(255, 255, 255, 0.95) !important;
  color: #6366f1 !important;
  border-color: rgba(255, 255, 255, 1) !important;
  transform: translateY(-4px) scale(1.02) !important;
  box-shadow: 0 16px 48px rgba(255, 255, 255, 0.4) !important;
}

/* 按钮点击效果 */
.hero-actions .hero-btn-primary:active,
.hero-actions .hero-btn-secondary:active {
  transform: translateY(-2px) scale(1.01) !important;
}

/* 按钮内容居中 */
.hero-actions .hero-btn-primary .ant-btn-loading-icon,
.hero-actions .hero-btn-secondary .ant-btn-loading-icon {
  margin-right: 8px;
}

/* 特色功能区域 */
.features-section {
  padding: 120px 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
}

.section-title {
  text-align: center;
  margin-bottom: 60px;
  color: #1e293b;
  font-size: 2.5rem;
  font-weight: 700;
}

.feature-card {
  height: 100%;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.2);
}

.feature-content {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 20px;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.2));
}

.feature-title {
  color: #1e293b;
  margin-bottom: 16px;
  font-size: 1.3rem;
  font-weight: 600;
}

.feature-description {
  color: #475569;
  line-height: 1.6;
  font-size: 14px;
}

/* 统计数据区域 */
.stats-section {
  padding: 80px 0;
  background: #fff;
  color: #333;
  position: relative;
}

.stats-section .container {
  padding: 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0;
}

.stat-item {
  flex: 1;
  padding: 20px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  color: #1890ff;
  display: block;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
  display: block;
}

/* 行动号召区域 */
.cta-section {
  padding: 100px 0;
  background: white;
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 24px;
}

.cta-title {
  color: #1e293b;
  margin-bottom: 24px;
  font-size: 2.5rem;
  font-weight: 700;
}

.cta-description {
  color: #475569;
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 40px;
}

.cta-button {
  height: 56px;
  padding: 0 40px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
}

.cta-button:hover,
.cta-button:focus {
  background: linear-gradient(135deg, #5b5ff5 0%, #8049f0 100%);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(99, 102, 241, 0.4);
  color: white;
}

.cta-button:active {
  transform: translateY(-2px) scale(1.01);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.5rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-actions .ant-space {
    flex-direction: column;
    width: 100%;
  }

  .hero-actions .hero-btn-primary,
  .hero-actions .hero-btn-secondary {
    width: 100% !important;
    margin-bottom: 16px;
    height: 52px !important;
    font-size: 16px !important;
    min-width: auto !important;
  }

  .cta-button {
    width: 100%;
    height: 52px;
    font-size: 16px;
  }
  
  .features-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }
  
  .stats-section {
    padding: 40px 0;
  }
  .stats-section .container {
    padding: 0;
    margin: 0 16px;
  }
  .stat-item {
    flex: 1;
    padding: 12px 0;
  }
  .stat-number {
    font-size: 1.5rem;
  }
  .stat-label {
    font-size: 0.9rem;
  }
  .stats-row {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .cta-section {
    padding: 60px 0;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-description {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .hero-content {
    padding: 0 16px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .feature-content {
    padding: 16px;
  }
  
  .feature-icon {
    font-size: 2.5rem;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .stat-label {
    font-size: 0.9rem;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content > * {
  animation: fadeInUp 0.8s ease-out;
}

.hero-title {
  animation-delay: 0.1s;
}

.hero-subtitle {
  animation-delay: 0.2s;
}

.hero-description {
  animation-delay: 0.3s;
}

.hero-actions {
  animation-delay: 0.4s;
}

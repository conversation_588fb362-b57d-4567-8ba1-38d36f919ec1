# 编译输出
target/
dist/
build/

# 依赖目录
node_modules/
.pnp
.pnp.js

# 日志文件
*.log
logs/
backend.log
frontend.log

# 运行时文件
*.pid
.DS_Store
Thumbs.db

# IDE文件
.idea/
.vscode/
*.swp
*.swo
*~

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传文件
uploads/
temp/

# 缓存文件
.cache/
.parcel-cache/

# 测试覆盖率
coverage/
*.lcov

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# Maven
.mvn/
mvnw
mvnw.cmd

# Spring Boot
spring-boot-*.jar

# Node.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

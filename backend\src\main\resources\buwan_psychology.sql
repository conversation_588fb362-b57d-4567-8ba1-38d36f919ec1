/*
 Navicat Premium Dump SQL

 Source Server         : mysql-localhost
 Source Server Type    : MySQL
 Source Server Version : 80406 (8.4.6)
 Source Host           : localhost:3336
 Source Schema         : buwan_psychology

 Target Server Type    : MySQL
 Target Server Version : 80406 (8.4.6)
 File Encoding         : 65001

 Date: 25/07/2025 20:39:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for assessment_results
-- ----------------------------
DROP TABLE IF EXISTS `assessment_results`;
CREATE TABLE `assessment_results`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `assessment_id` bigint NOT NULL COMMENT '评估ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `answers` json NULL COMMENT '用户答案（JSON格式）',
  `score` int NULL DEFAULT NULL COMMENT '评估得分',
  `result_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '结果级别',
  `analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '结果分析',
  `recommendations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '建议',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_assessment_id`(`assessment_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_result_level`(`result_level` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `assessment_results_ibfk_1` FOREIGN KEY (`assessment_id`) REFERENCES `psychological_assessments` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `assessment_results_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评估结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assessment_results
-- ----------------------------

-- ----------------------------
-- Table structure for chat_messages
-- ----------------------------
DROP TABLE IF EXISTS `chat_messages`;
CREATE TABLE `chat_messages`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `session_id` bigint NOT NULL COMMENT '会话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `message_type` tinyint NULL DEFAULT 0,
  `emotion_analysis` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '情感分析结果',
  `keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词提取',
  `confidence` int NULL DEFAULT NULL COMMENT 'AI置信度（0-100）',
  `has_risk_signal` tinyint(1) NULL DEFAULT 0 COMMENT '是否包含风险信号',
  `risk_level` tinyint NULL DEFAULT 0,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_message_type`(`message_type` ASC) USING BTREE,
  INDEX `idx_risk_level`(`risk_level` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_messages_session_time`(`session_id` ASC, `create_time` ASC) USING BTREE,
  CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `consultation_sessions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `chat_messages_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chat_messages
-- ----------------------------

-- ----------------------------
-- Table structure for consultation_sessions
-- ----------------------------
DROP TABLE IF EXISTS `consultation_sessions`;
CREATE TABLE `consultation_sessions`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话标题',
  `session_type` tinyint NULL DEFAULT 0,
  `status` tinyint NULL DEFAULT 0,
  `summary` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话摘要',
  `ai_analysis` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'AI分析结果',
  `recommendations` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '建议和方案',
  `urgency_level` tinyint NULL DEFAULT 0,
  `start_time` datetime NULL DEFAULT NULL COMMENT '会话开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '会话结束时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `counselor_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_session_type`(`session_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_urgency_level`(`urgency_level` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_sessions_user_time`(`user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `FK4rncvfc1av7sv2ba2mwes22tx`(`counselor_id` ASC) USING BTREE,
  CONSTRAINT `consultation_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK4rncvfc1av7sv2ba2mwes22tx` FOREIGN KEY (`counselor_id`) REFERENCES `counselors` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '心理咨询会话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of consultation_sessions
-- ----------------------------
INSERT INTO `consultation_sessions` VALUES (1, 1, '111', 0, 0, NULL, NULL, NULL, 0, '2025-07-25 20:27:19', NULL, NULL, NULL, 3);
INSERT INTO `consultation_sessions` VALUES (2, 1, '222', 0, 0, NULL, NULL, NULL, 0, '2025-07-25 20:28:31', NULL, NULL, NULL, 3);
INSERT INTO `consultation_sessions` VALUES (3, 1, '444', 0, 0, NULL, NULL, NULL, 0, '2025-07-25 20:28:40', NULL, NULL, NULL, 3);
INSERT INTO `consultation_sessions` VALUES (4, 1, '43', 0, 0, NULL, NULL, NULL, 0, '2025-07-25 20:31:15', NULL, NULL, NULL, 3);
INSERT INTO `consultation_sessions` VALUES (5, 1, '21', 0, 0, NULL, NULL, NULL, 0, '2025-07-25 20:31:54', NULL, NULL, NULL, 3);

-- ----------------------------
-- Table structure for counselors
-- ----------------------------
DROP TABLE IF EXISTS `counselors`;
CREATE TABLE `counselors`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `consultation_count` int NULL DEFAULT NULL,
  `consultation_fee` double NULL DEFAULT NULL,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `education` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `experience` int NULL DEFAULT NULL,
  `is_available` bit(1) NULL DEFAULT NULL,
  `license_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `philosophy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `rating` double NULL DEFAULT NULL,
  `specializations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `working_hours` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `photo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of counselors
-- ----------------------------
INSERT INTO `counselors` VALUES (2, 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=80&h=80&fit=crop&crop=face&auto=format', 156, NULL, NULL, '专业心理咨询师', NULL, 10, b'1', NULL, '李心怡', NULL, 4.8, '[\"焦虑障碍\"]', NULL, '心理咨询师', NULL, NULL, 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (3, 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=80&h=80&fit=crop&crop=face&auto=format', 208, NULL, NULL, '青少年心理专家', NULL, 15, b'1', NULL, '王明轩', NULL, 4.9, '[\"青少年心理\"]', NULL, '临床心理学博士', '2025-07-25 20:31:54.015882', NULL, 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (4, 'https://images.unsplash.com/photo-1594824388853-e4d2e3e5e8e5?w=80&h=80&fit=crop&crop=face&auto=format', 128, NULL, NULL, '婚姻家庭专家', NULL, 8, b'1', NULL, '张雅琳', NULL, 4.7, '[\"婚姻家庭\"]', NULL, '婚姻家庭咨询师', NULL, NULL, 'https://images.unsplash.com/photo-1594824388853-e4d2e3e5e8e5?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (5, 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=80&h=80&fit=crop&crop=face&auto=format', 89, NULL, NULL, '创伤治疗专家', NULL, 12, b'1', NULL, '陈志强', NULL, 4.6, '[\"创伤治疗\"]', NULL, '创伤治疗专家', NULL, NULL, 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (6, 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=80&h=80&fit=crop&crop=face&auto=format', 167, NULL, NULL, '儿童心理专家', NULL, 9, b'1', NULL, '刘美娟', NULL, 4.8, '[\"儿童心理\"]', NULL, '儿童心理专家', NULL, NULL, 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (7, 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face&auto=format', 94, 220, NULL, '企业EAP心理顾问', NULL, 7, b'1', NULL, '赵文博', NULL, 4.5, '[\"职场心理\", \"压力管理\"]', '[\"理性\", \"务实\"]', '职场心理顾问', NULL, '周一至周五 18:00-22:00', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (8, 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face&auto=format', 76, 200, NULL, '专业成瘾治疗师', NULL, 5, b'1', NULL, '孙雨萱', NULL, 4.4, '[\"成瘾治疗\", \"网络成瘾\"]', '[\"年轻\", \"有活力\"]', '成瘾治疗师', NULL, '周二至周六 16:00-21:00', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=600&fit=crop&auto=format&q=80');
INSERT INTO `counselors` VALUES (9, 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face&auto=format', 145, 180, NULL, '老年心理健康专家', NULL, 20, b'1', NULL, '马建国', NULL, 4.7, '[\"老年心理\", \"抑郁障碍\"]', '[\"温和\", \"慈祥\"]', '老年心理专家', NULL, '周一至周五 9:00-16:00', 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=600&fit=crop&auto=format&q=80');

-- ----------------------------
-- Table structure for psychological_assessments
-- ----------------------------
DROP TABLE IF EXISTS `psychological_assessments`;
CREATE TABLE `psychological_assessments`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `assessment_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评估类型',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评估标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评估描述',
  `questions` json NULL COMMENT '评估问题（JSON格式）',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态：0-进行中，1-已完成',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_assessment_type`(`assessment_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `psychological_assessments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '心理评估表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of psychological_assessments
-- ----------------------------
INSERT INTO `psychological_assessments` VALUES (1, 1, 'anxiety', '焦虑自评量表(SAS)', '用于评估个体焦虑水平的标准化量表', '[{\"id\": 1, \"options\": [\"从不\", \"有时\", \"经常\", \"总是\"], \"question\": \"我感到比平常更加紧张和焦虑\"}, {\"id\": 2, \"options\": [\"从不\", \"有时\", \"经常\", \"总是\"], \"question\": \"我无缘无故地感到害怕\"}, {\"id\": 3, \"options\": [\"从不\", \"有时\", \"经常\", \"总是\"], \"question\": \"我容易心烦意乱或感到恐慌\"}, {\"id\": 4, \"options\": [\"从不\", \"有时\", \"经常\", \"总是\"], \"question\": \"我感到我可能将要发疯\"}, {\"id\": 5, \"options\": [\"从不\", \"有时\", \"经常\", \"总是\"], \"question\": \"我觉得一切都很好，也不会发生什么不幸\"}]', 1, '2025-07-25 15:25:35', '2025-07-25 15:25:35');

-- ----------------------------
-- Table structure for system_logs
-- ----------------------------
DROP TABLE IF EXISTS `system_logs`;
CREATE TABLE `system_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '操作描述',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_action`(`action` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `system_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of system_logs
-- ----------------------------

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码（加密后）',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NULL DEFAULT 0,
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `user_type` tinyint NULL DEFAULT 0,
  `status` tinyint NULL DEFAULT 0,
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `bio` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户简介',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_user_type`(`user_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_users_type_status`(`user_type` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', '<EMAIL>', 'admin123', '系统管理员', NULL, 0, NULL, NULL, 2, 0, '2025-07-25 20:25:07', '2025-07-25 15:25:35', '2025-07-25 17:38:13', NULL);
INSERT INTO `users` VALUES (2, 'testuser', '<EMAIL>', 'password123', 'Test User', NULL, NULL, NULL, NULL, 0, 0, '2025-07-25 18:07:50', NULL, '2025-07-25 17:38:20', NULL);
INSERT INTO `users` VALUES (3, 'newuser', '<EMAIL>', 'newpass123', 'New User', NULL, NULL, NULL, NULL, 0, 0, '2025-07-25 18:01:46', NULL, NULL, NULL);

-- ----------------------------
-- View structure for session_statistics
-- ----------------------------
DROP VIEW IF EXISTS `session_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `session_statistics` AS select count(0) AS `total_sessions`,count((case when (`consultation_sessions`.`status` = 0) then 1 end)) AS `active_sessions`,count((case when (`consultation_sessions`.`status` = 1) then 1 end)) AS `completed_sessions`,count((case when (`consultation_sessions`.`urgency_level` >= 2) then 1 end)) AS `urgent_sessions`,avg(timestampdiff(MINUTE,`consultation_sessions`.`start_time`,`consultation_sessions`.`end_time`)) AS `avg_session_duration` from `consultation_sessions`;

-- ----------------------------
-- View structure for user_statistics
-- ----------------------------
DROP VIEW IF EXISTS `user_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `user_statistics` AS select count(0) AS `total_users`,count((case when (`users`.`user_type` = 1) then 1 end)) AS `teenager_users`,count((case when (`users`.`status` = 0) then 1 end)) AS `active_users`,count((case when (`users`.`last_login_time` >= (now() - interval 30 day)) then 1 end)) AS `recent_active_users` from `users`;

SET FOREIGN_KEY_CHECKS = 1;

{"name": "buwan-psychology-frontend", "version": "1.0.0", "description": "不晚心理平台前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^1.9.7", "@uiw/react-md-editor": "^4.0.8", "antd": "^5.12.0", "axios": "^1.6.0", "classnames": "^2.3.2", "dayjs": "^1.11.10", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-redux": "^8.1.3", "react-router-dom": "^6.20.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^7.0.6"}, "keywords": ["react", "typescript", "psychology", "ai", "mental-health"], "author": "不晚心理团队", "license": "MIT"}
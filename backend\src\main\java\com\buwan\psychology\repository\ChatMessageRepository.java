package com.buwan.psychology.repository;

import com.buwan.psychology.entity.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息数据访问接口
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    /**
     * 根据会话ID查找消息列表，按创建时间升序
     */
    List<ChatMessage> findBySessionIdOrderByCreateTimeAsc(Long sessionId);

    /**
     * 根据会话ID查找消息列表，按创建时间倒序
     */
    List<ChatMessage> findBySessionIdOrderByCreateTimeDesc(Long sessionId);

    /**
     * 根据用户ID查找消息列表
     */
    List<ChatMessage> findByUserIdOrderByCreateTimeDesc(Long userId);

    /**
     * 根据消息类型查找消息列表
     */
    List<ChatMessage> findByMessageTypeOrderByCreateTimeDesc(Integer messageType);

    /**
     * 查找有风险信号的消息
     */
    List<ChatMessage> findByHasRiskSignalTrueOrderByCreateTimeDesc();

    /**
     * 根据风险等级查找消息
     */
    List<ChatMessage> findByRiskLevelOrderByCreateTimeDesc(Integer riskLevel);

    /**
     * 查找指定时间范围内的消息
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.createTime BETWEEN :startTime AND :endTime ORDER BY m.createTime DESC")
    List<ChatMessage> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计会话的消息数量
     */
    long countBySessionId(Long sessionId);

    /**
     * 统计用户的消息数量
     */
    long countByUserId(Long userId);

    /**
     * 统计指定类型的消息数量
     */
    long countByMessageType(Integer messageType);

    /**
     * 查找最近的消息
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.sessionId = :sessionId ORDER BY m.createTime DESC")
    List<ChatMessage> findRecentMessagesBySessionId(@Param("sessionId") Long sessionId);

    /**
     * 根据关键词搜索消息
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.content LIKE %:keyword% ORDER BY m.createTime DESC")
    List<ChatMessage> findByContentContaining(@Param("keyword") String keyword);

    /**
     * 查找用户最近的一条消息
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.userId = :userId ORDER BY m.createTime DESC LIMIT 1")
    List<ChatMessage> findTop1ByUserIdOrderByCreateTimeDesc(@Param("userId") Long userId);
}

/* 色彩心理游戏样式 */
.color-psychology-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.result-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

/* 测试头部 */
.test-header {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-button {
  border-radius: 8px !important;
}

.test-info {
  text-align: center;
  flex: 1;
  margin: 0 20px;
}

/* 题目卡片 */
.question-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.question-card .ant-card-body {
  padding: 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-title {
  color: #1a1a1a !important;
  margin-bottom: 40px !important;
  font-size: 22px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  text-align: center;
}

/* 颜色网格 */
.colors-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  flex: 1;
  align-items: center;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.color-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border: 3px solid transparent;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.color-option:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.color-option.selected {
  border-color: #1890ff;
  background: #e6f7ff;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
}

.color-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.color-option:hover .color-circle,
.color-option.selected .color-circle {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.color-name {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #333 !important;
  text-align: center;
}

/* 题目操作按钮 */
.question-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.question-actions .ant-btn {
  height: 40px !important;
  padding: 0 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* 结果卡片 */
.result-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-card .ant-card-body {
  padding: 40px;
}

.result-content {
  margin-top: 24px;
}

/* 性格分析 */
.personality-section {
  text-align: center;
  margin-bottom: 32px;
}

.personality-description {
  font-size: 16px !important;
  color: #666 !important;
  line-height: 1.6 !important;
  margin: 16px 0 !important;
}

/* 特征网格 */
.traits-section {
  margin-bottom: 32px;
}

.traits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.trait-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.trait-icon {
  color: #52c41a;
  font-size: 16px;
}

/* 建议列表 */
.suggestions-section {
  margin-bottom: 32px;
}

.suggestions-list {
  margin: 16px 0;
  padding-left: 20px;
}

.suggestions-list li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #666;
}

/* 色彩选择展示 */
.color-choices-section {
  margin-bottom: 32px;
}

.color-choices {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.color-choice {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.color-choice .color-circle {
  width: 40px;
  height: 40px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.color-choice .ant-typography {
  font-size: 12px !important;
  color: #666 !important;
}

/* 免责声明 */
.disclaimer {
  margin-top: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ffa940;
}

/* 结果操作按钮 */
.result-actions {
  margin-top: 32px;
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.result-actions .ant-btn {
  height: 40px !important;
  padding: 0 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .color-psychology-container {
    padding: 12px;
  }
  
  .test-container,
  .result-container {
    max-width: 100%;
  }
  
  .question-card .ant-card-body {
    padding: 24px;
  }
  
  .question-title {
    font-size: 18px !important;
    margin-bottom: 30px !important;
  }
  
  .colors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .color-option {
    padding: 16px;
  }
  
  .color-circle {
    width: 50px;
    height: 50px;
  }
  
  .question-actions {
    margin-top: 30px;
    padding-top: 20px;
  }
  
  .result-card .ant-card-body {
    padding: 24px;
  }
  
  .traits-grid {
    grid-template-columns: 1fr;
  }
  
  .color-choices {
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .color-psychology-container {
    padding: 8px;
  }
  
  .question-card .ant-card-body {
    padding: 16px;
  }
  
  .question-title {
    font-size: 16px !important;
    margin-bottom: 24px !important;
  }
  
  .colors-grid {
    gap: 12px;
  }
  
  .color-option {
    padding: 12px;
  }
  
  .color-circle {
    width: 40px;
    height: 40px;
  }
  
  .color-name {
    font-size: 12px !important;
  }
  
  .question-actions .ant-btn {
    height: 36px !important;
    padding: 0 16px !important;
    font-size: 14px;
  }
  
  .result-card .ant-card-body {
    padding: 16px;
  }
  
  .result-actions .ant-btn {
    height: 36px !important;
    padding: 0 16px !important;
    font-size: 14px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .test-info {
    margin: 0;
  }
}

/* 动画效果 */
.question-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.color-option {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 认证页面样式 */
.auth-container {
  min-height: calc(100vh - 64px - 70px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.auth-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.2) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

.auth-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(30px) rotate(240deg); }
}

.auth-content {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 480px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.auth-card {
  width: 100%;
  border-radius: 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30px);
  box-shadow:
    0 32px 80px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: none;
  overflow: hidden;
  position: relative;
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.auth-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 40px 100px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.auth-card .ant-card-body {
  padding: 48px;
  position: relative;
}

/* 卡片内部装饰 */
.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.auth-header::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.auth-title {
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 700;
  font-size: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 8px;
}

/* 品牌标识 */
.auth-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  gap: 12px;
}

.auth-brand-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.auth-brand-text {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.auth-form {
  margin-bottom: 32px;
}

.auth-input {
  border-radius: 16px !important;
  border: 2px solid #e2e8f0 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(248, 250, 252, 0.8) !important;
  height: 56px !important;
  font-size: 16px !important;
  padding: 0 20px !important;
  backdrop-filter: blur(10px);
}

.auth-input:focus,
.auth-input:hover {
  border-color: #667eea !important;
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow:
    0 0 0 4px rgba(102, 126, 234, 0.1) !important,
    0 8px 24px rgba(102, 126, 234, 0.15) !important;
  transform: translateY(-2px) !important;
}

/* 输入框图标样式 */
.auth-input .anticon {
  color: #94a3b8;
  font-size: 18px;
}

.auth-input:focus .anticon,
.auth-input:hover .anticon {
  color: #667eea;
}

.auth-button {
  height: 56px !important;
  border-radius: 16px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow:
    0 8px 24px rgba(102, 126, 234, 0.3) !important,
    0 0 0 1px rgba(255, 255, 255, 0.2) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: white !important;
  position: relative;
  overflow: hidden;
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.auth-button:hover::before {
  left: 100%;
}

.auth-button:hover,
.auth-button:focus {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
  transform: translateY(-4px) scale(1.02) !important;
  box-shadow:
    0 16px 40px rgba(102, 126, 234, 0.4) !important,
    0 0 0 1px rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

.auth-links {
  text-align: center;
  margin-top: 24px;
}

.auth-link-item {
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.auth-link-item:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
}

.auth-link {
  color: #667eea;
  font-weight: 600;
  margin-left: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.auth-link:hover {
  color: #5a67d8;
}

.auth-link:hover::after {
  width: 100%;
}

.auth-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.auth-footer-text {
  font-size: 13px;
  line-height: 1.6;
  color: #94a3b8;
}

/* 分割线样式 */
.auth-divider {
  margin: 32px 0 !important;
  border-color: rgba(226, 232, 240, 0.6) !important;
}

.auth-divider .ant-divider-inner-text {
  color: #94a3b8 !important;
  font-size: 14px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  padding: 0 16px !important;
}

/* 表单项样式 */
.ant-form-item-label > label {
  color: #1e293b;
  font-weight: 600;
}

.ant-form-item-explain-error {
  font-size: 12px;
}

/* 分割线样式 */
.ant-divider-horizontal.ant-divider-with-text {
  margin: 24px 0;
}

.ant-divider-inner-text {
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    padding: 24px 16px;
  }

  .auth-content {
    max-width: 400px;
  }

  .auth-card .ant-card-body {
    padding: 32px 24px;
  }

  .auth-title {
    font-size: 28px;
  }

  .auth-input {
    height: 52px !important;
    font-size: 15px !important;
  }

  .auth-button {
    height: 52px !important;
    font-size: 15px !important;
  }
}

@media (max-width: 576px) {
  .auth-container {
    padding: 20px 12px;
  }

  .auth-content {
    max-width: 100%;
  }

  .auth-card {
    border-radius: 24px;
  }

  .auth-card .ant-card-body {
    padding: 24px 20px;
  }

  .auth-header {
    margin-bottom: 32px;
  }

  .auth-title {
    font-size: 24px;
  }

  .auth-subtitle {
    font-size: 15px;
  }

  .auth-input {
    height: 48px !important;
    font-size: 14px !important;
    border-radius: 12px !important;
  }

  .auth-button {
    height: 48px !important;
    font-size: 14px !important;
    border-radius: 12px !important;
  }
}

/* 加载状态 */
.auth-button.ant-btn-loading {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%) !important;
  color: white !important;
  cursor: not-allowed;
}

.auth-button.ant-btn-loading::before {
  display: none;
}

/* 按钮点击状态 */
.auth-button:active {
  transform: translateY(-2px) scale(0.98) !important;
  box-shadow:
    0 8px 20px rgba(102, 126, 234, 0.4) !important,
    0 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

/* 表单项样式 */
.ant-form-item-label > label {
  color: #374151 !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.ant-form-item-explain-error {
  font-size: 13px !important;
  margin-top: 8px !important;
}

/* 错误状态 */
.ant-form-item-has-error .auth-input {
  border-color: #ef4444 !important;
  background: rgba(254, 242, 242, 0.8) !important;
}

.ant-form-item-has-error .auth-input:focus {
  border-color: #ef4444 !important;
  box-shadow:
    0 0 0 4px rgba(239, 68, 68, 0.1) !important,
    0 8px 24px rgba(239, 68, 68, 0.15) !important;
}

/* 成功状态 */
.ant-form-item-has-success .auth-input {
  border-color: #10b981 !important;
  background: rgba(240, 253, 244, 0.8) !important;
}

.ant-form-item-has-success .auth-input:focus {
  border-color: #10b981 !important;
  box-shadow:
    0 0 0 4px rgba(16, 185, 129, 0.1) !important,
    0 8px 24px rgba(16, 185, 129, 0.15) !important;
}

/* 禁用状态 */
.auth-button:disabled {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
  color: #94a3b8 !important;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

import React from 'react'
import { Card, But<PERSON>, Typography, Space } from 'antd'
import { MessageOutlined, HeartOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const HomeButtonTest: React.FC = () => {
  return (
    <div style={{ padding: '40px', background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1} style={{ textAlign: 'center', marginBottom: '40px' }}>
          首页按钮修复测试
        </Title>

        {/* 完全复制首页的英雄区域结构 */}
        <Card title="首页英雄区域按钮复制" style={{ marginBottom: '24px' }}>
          <div style={{ 
            background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
            padding: '80px 40px',
            borderRadius: '16px',
            textAlign: 'center',
            color: 'white'
          }}>
            <Title level={1} style={{ color: 'white', marginBottom: '16px', fontSize: '3.5rem', fontWeight: 700 }}>
              不晚心理
            </Title>
            <Title level={2} style={{ color: 'rgba(255, 255, 255, 0.95)', marginBottom: '24px', fontSize: '2rem', fontWeight: 500 }}>
              AI驱动的心理健康平台
            </Title>
            <Text style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '18px', display: 'block', marginBottom: '40px', lineHeight: 1.8 }}>
              专业的心理咨询服务，24小时陪伴您的心理健康之旅。
              无论是焦虑、抑郁还是生活压力，我们都在这里为您提供支持。
            </Text>
            
            {/* 使用与首页完全相同的结构和类名 */}
            <Space size="large" className="hero-actions">
              <Button
                size="large"
                className="hero-btn-primary"
                icon={<MessageOutlined />}
              >
                开始心理咨询
              </Button>
              <Button
                size="large"
                className="hero-btn-secondary"
                icon={<HeartOutlined />}
              >
                心理健康评估
              </Button>
            </Space>
          </div>
        </Card>

        {/* 按钮尺寸对比 */}
        <Card title="按钮尺寸对比测试" style={{ marginBottom: '24px' }}>
          <div style={{ background: '#f8fafc', padding: '40px', borderRadius: '12px' }}>
            <div style={{ marginBottom: '32px' }}>
              <Text strong style={{ display: 'block', marginBottom: '16px', fontSize: '16px' }}>
                英雄区域按钮（应该完全一致）：
              </Text>
              <Space size="large" className="hero-actions">
                <Button size="large" className="hero-btn-primary">
                  开始心理咨询
                </Button>
                <Button size="large" className="hero-btn-secondary">
                  心理健康评估
                </Button>
              </Space>
            </div>

            <div style={{ marginBottom: '32px' }}>
              <Text strong style={{ display: 'block', marginBottom: '16px', fontSize: '16px' }}>
                标准Ant Design按钮对比：
              </Text>
              <Space size="large">
                <Button type="primary" size="large">
                  Primary按钮
                </Button>
                <Button size="large">
                  Default按钮
                </Button>
              </Space>
            </div>

            <div>
              <Text strong style={{ display: 'block', marginBottom: '16px', fontSize: '16px' }}>
                尺寸测量参考：
              </Text>
              <div style={{
                display: 'flex',
                width: '180px',
                height: '56px',
                background: 'rgba(99, 102, 241, 0.1)',
                border: '2px dashed #6366f1',
                borderRadius: '28px',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#6366f1',
                fontWeight: 600
              }}>
                180px × 56px
              </div>
            </div>
          </div>
        </Card>

        {/* 样式检查清单 */}
        <Card title="样式检查清单">
          <div style={{ background: '#f8fafc', padding: '32px', borderRadius: '12px' }}>
            <Title level={4} style={{ marginBottom: '24px' }}>
              按钮应该满足以下要求：
            </Title>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
              <div>
                <Text strong style={{ color: '#059669' }}>✅ 尺寸一致性</Text>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>高度：56px</li>
                  <li>最小宽度：180px</li>
                  <li>内边距：0 32px</li>
                  <li>圆角：28px</li>
                </ul>
              </div>
              
              <div>
                <Text strong style={{ color: '#059669' }}>✅ 字体样式</Text>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>字体大小：18px</li>
                  <li>字体粗细：600</li>
                  <li>文字居中对齐</li>
                </ul>
              </div>
              
              <div>
                <Text strong style={{ color: '#059669' }}>✅ 视觉效果</Text>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>毛玻璃背景效果</li>
                  <li>悬停动画效果</li>
                  <li>阴影和光晕</li>
                  <li>平滑过渡动画</li>
                </ul>
              </div>
              
              <div>
                <Text strong style={{ color: '#059669' }}>✅ 响应式设计</Text>
                <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                  <li>移动端全宽显示</li>
                  <li>移动端高度52px</li>
                  <li>移动端字体16px</li>
                </ul>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default HomeButtonTest

/**
 * 测试会话切换时的消息状态管理
 * 验证修复：切换会话时旧消息不应该显示在新会话中
 */

import { configureStore } from '@reduxjs/toolkit'
import chatReducer, { setCurrentSession, clearMessages, fetchMessages } from '@/store/slices/chatSlice'

// 模拟的测试数据
const mockSession1 = {
  id: 1,
  userId: 1,
  title: '王明轩-1',
  sessionType: 0,
  status: 0,
  createTime: '2024-01-01T10:00:00Z',
  updateTime: '2024-01-01T10:00:00Z'
}

const mockSession2 = {
  id: 15,
  userId: 1,
  title: '新会话-15',
  sessionType: 0,
  status: 0,
  createTime: '2024-01-02T10:00:00Z',
  updateTime: '2024-01-02T10:00:00Z'
}

const mockMessages1 = [
  {
    id: 1,
    sessionId: 1,
    userId: 1,
    content: '这是王明轩-1会话中的消息',
    messageType: 0,
    createTime: '2024-01-01T10:01:00Z'
  },
  {
    id: 2,
    sessionId: 1,
    userId: 1,
    content: 'AI回复消息',
    messageType: 1,
    createTime: '2024-01-01T10:02:00Z'
  }
]

const mockMessages2 = [
  {
    id: 3,
    sessionId: 15,
    userId: 1,
    content: '这是新会话-15中的消息',
    messageType: 0,
    createTime: '2024-01-02T10:01:00Z'
  }
]

describe('Chat Session Switch Bug Fix', () => {
  let store: any

  beforeEach(() => {
    store = configureStore({
      reducer: {
        chat: chatReducer
      }
    })
  })

  test('应该在切换会话时清空旧消息', () => {
    // 1. 设置初始状态：当前会话为session1，有消息
    store.dispatch(setCurrentSession(mockSession1))
    
    // 模拟fetchMessages.fulfilled action
    store.dispatch({
      type: 'chat/fetchMessages/fulfilled',
      payload: mockMessages1
    })

    // 验证初始状态
    let state = store.getState().chat
    expect(state.currentSession?.id).toBe(1)
    expect(state.messages).toHaveLength(2)
    expect(state.messages[0].content).toBe('这是王明轩-1会话中的消息')

    // 2. 切换到新会话前先清空消息（这是我们的修复）
    store.dispatch(clearMessages())
    store.dispatch(setCurrentSession(mockSession2))

    // 验证消息已被清空
    state = store.getState().chat
    expect(state.currentSession?.id).toBe(15)
    expect(state.messages).toHaveLength(0) // 关键：消息应该被清空

    // 3. 加载新会话的消息
    store.dispatch({
      type: 'chat/fetchMessages/fulfilled',
      payload: mockMessages2
    })

    // 验证最终状态
    state = store.getState().chat
    expect(state.currentSession?.id).toBe(15)
    expect(state.messages).toHaveLength(1)
    expect(state.messages[0].content).toBe('这是新会话-15中的消息')
    expect(state.messages[0].sessionId).toBe(15)
  })

  test('修复前的错误行为：不清空消息直接切换会话', () => {
    // 模拟修复前的错误行为
    store.dispatch(setCurrentSession(mockSession1))
    store.dispatch({
      type: 'chat/fetchMessages/fulfilled',
      payload: mockMessages1
    })

    // 直接切换会话而不清空消息（错误的做法）
    store.dispatch(setCurrentSession(mockSession2))

    // 此时旧消息仍然存在
    let state = store.getState().chat
    expect(state.currentSession?.id).toBe(15)
    expect(state.messages).toHaveLength(2) // 旧消息仍然存在
    expect(state.messages[0].sessionId).toBe(1) // 这些消息属于旧会话

    // 当新消息加载时，会看到混合的消息
    store.dispatch({
      type: 'chat/fetchMessages/fulfilled',
      payload: mockMessages2
    })

    state = store.getState().chat
    expect(state.messages).toHaveLength(1) // fetchMessages会替换所有消息
    expect(state.messages[0].sessionId).toBe(15)
  })
})

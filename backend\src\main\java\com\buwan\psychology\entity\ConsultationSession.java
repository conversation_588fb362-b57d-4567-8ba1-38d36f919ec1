package com.buwan.psychology.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


import java.time.LocalDateTime;

/**
 * 心理咨询会话实体类
 */
@Entity
@Table(name = "consultation_sessions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class ConsultationSession {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(nullable = false)
    private Long userId;

    /**
     * 心理咨询师ID
     */
    @Column
    private Long counselorId;

    /**
     * 心理咨询师关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "counselorId", insertable = false, updatable = false)
    private Counselor counselor;

    /**
     * 会话标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 详细描述：用户的详细情况和问题描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 会话类型：0-一般咨询，1-焦虑问题，2-抑郁问题，3-学习压力，4-人际关系，5-其他
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer sessionType;

    /**
     * 会话状态：0-进行中，1-已结束，2-已暂停
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer status;

    /**
     * 会话摘要
     */
    @Column(length = 1000)
    private String summary;

    /**
     * AI分析结果
     */
    @Column(length = 2000)
    private String aiAnalysis;

    /**
     * 建议和方案
     */
    @Column(length = 2000)
    private String recommendations;

    /**
     * 紧急程度：0-正常，1-需要关注，2-紧急
     */
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer urgencyLevel;

    /**
     * 会话开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会话结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private LocalDateTime updateTime;

    /**
     * 获取会话类型描述
     */
    public String getSessionTypeDesc() {
        if (sessionType == null) return "一般咨询";
        return switch (sessionType) {
            case 1 -> "焦虑问题";
            case 2 -> "抑郁问题";
            case 3 -> "学习压力";
            case 4 -> "人际关系";
            case 5 -> "其他";
            default -> "一般咨询";
        };
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "进行中";
        return switch (status) {
            case 1 -> "已结束";
            case 2 -> "已暂停";
            default -> "进行中";
        };
    }

    /**
     * 是否是紧急情况
     */
    public boolean isUrgent() {
        return urgencyLevel != null && urgencyLevel >= 2;
    }
}

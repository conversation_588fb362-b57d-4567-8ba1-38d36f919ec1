import axios, { AxiosResponse } from 'axios'
import { message } from 'antd'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 60000, // 60秒超时
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
      message.error('登录已过期，请重新登录')
    } else if (error.response?.status === 403) {
      message.error('没有权限访问该资源')
    } else if (error.response?.status >= 500) {
      message.error('服务器错误，请稍后重试')
    } else {
      message.error(error.response?.data?.message || '请求失败')
    }
    return Promise.reject(error)
  }
)

// API接口定义
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
}

// 认证相关API
export const authApi = {
  login: (credentials: { usernameOrEmail: string; password: string }): Promise<ApiResponse> =>
    api.post('/users/login', credentials),
  
  register: (userData: any): Promise<ApiResponse> =>
    api.post('/users/register', userData),
  
  getUserInfo: (id: number): Promise<ApiResponse> =>
    api.get(`/users/${id}`),
  
  updateUser: (id: number, userData: any): Promise<ApiResponse> =>
    api.put(`/users/${id}`, userData),
  
  changePassword: (id: number, passwordData: { oldPassword: string; newPassword: string }): Promise<ApiResponse> =>
    api.post(`/users/${id}/change-password`, passwordData),
}

// 聊天相关API
export const chatApi = {
  getSessions: (userId: number): Promise<ApiResponse> =>
    api.get(`/chat/sessions?userId=${userId}`),
  
  createSession: (sessionData: any): Promise<ApiResponse> =>
    api.post('/chat/sessions', sessionData),
  
  getMessages: (sessionId: number): Promise<ApiResponse> =>
    api.get(`/chat/sessions/${sessionId}/messages`),

  clearMessages: (sessionId: number, userId: number): Promise<ApiResponse> =>
    api.delete(`/chat/sessions/${sessionId}/messages?userId=${userId}`),

  sendMessage: (sessionId: number, messageData: any): Promise<ApiResponse> =>
    api.post(`/chat/sessions/${sessionId}/messages`, messageData),

  // 流式发送消息
  sendStreamMessage: (sessionId: number, messageData: any): Promise<ReadableStream> => {
    const token = localStorage.getItem('token')
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    return fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'}/api/chat/sessions/${sessionId}/messages/stream`, {
      method: 'POST',
      headers,
      body: JSON.stringify(messageData),
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.body!
    })
  },

  // 保存完整AI回复
  saveCompleteResponse: (sessionId: number, content: string): Promise<ApiResponse> =>
    api.post(`/chat/sessions/${sessionId}/messages/complete`, { content }),
  
  endSession: (sessionId: number): Promise<ApiResponse> =>
    api.post(`/chat/sessions/${sessionId}/end`),
}

// 心理评估相关API
export const assessmentApi = {
  getAssessments: (): Promise<ApiResponse> =>
    api.get('/assessments'),
  
  submitAssessment: (assessmentData: any): Promise<ApiResponse> =>
    api.post('/assessments', assessmentData),
  
  getAssessmentResults: (userId: number): Promise<ApiResponse> =>
    api.get(`/assessments/results?userId=${userId}`),
}

// 管理相关API
export const adminApi = {
  getUsers: (): Promise<ApiResponse> =>
    api.get('/admin/users'),
  
  getTeenageUsers: (): Promise<ApiResponse> =>
    api.get('/users/teenagers'),
  
  getActiveUsers: (days: number = 30): Promise<ApiResponse> =>
    api.get(`/users/active?days=${days}`),
  
  disableUser: (id: number): Promise<ApiResponse> =>
    api.post(`/users/${id}/disable`),
  
  enableUser: (id: number): Promise<ApiResponse> =>
    api.post(`/users/${id}/enable`),
  
  getStatistics: (): Promise<ApiResponse> =>
    api.get('/admin/statistics'),
}

// 心理咨询师相关API
export const counselorApi = {
  getAllCounselors: (): Promise<ApiResponse> =>
    api.get('/counselors'),

  getCounselorById: (id: number): Promise<ApiResponse> =>
    api.get(`/counselors/${id}`),

  getCounselorsBySpecialization: (specialization: string): Promise<ApiResponse> =>
    api.get(`/counselors/specialization/${specialization}`),

  getRecommendedCounselors: (): Promise<ApiResponse> =>
    api.get('/counselors/recommended'),

  matchCounselor: (sessionType: number): Promise<ApiResponse> =>
    api.get(`/counselors/match?sessionType=${sessionType}`),

  getCounselorStats: (id: number): Promise<ApiResponse> =>
    api.get(`/counselors/${id}/stats`)
}

export default api

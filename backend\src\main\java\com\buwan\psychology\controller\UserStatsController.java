package com.buwan.psychology.controller;

import com.buwan.psychology.entity.ConsultationSession;
import com.buwan.psychology.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户统计数据控制器
 */
@RestController
@RequestMapping("/api/user/stats")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class UserStatsController {

    private final UserStatsService userStatsService;

    /**
     * 获取当前认证用户的ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof Long) {
            return (Long) authentication.getPrincipal();
        }
        throw new RuntimeException("用户未认证");
    }

    /**
     * 获取用户统计数据
     */
    @GetMapping("/overview")
    public ResponseEntity<?> getUserStats() {
        try {
            Long userId = getCurrentUserId();
            Map<String, Object> stats = userStatsService.getUserStats(userId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "获取用户统计数据成功",
                "data", stats
            ));
        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取统计数据失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取最近的咨询会话
     */
    @GetMapping("/recent-sessions")
    public ResponseEntity<?> getRecentSessions(@RequestParam(defaultValue = "5") int limit) {
        try {
            Long userId = getCurrentUserId();
            List<ConsultationSession> sessions = userStatsService.getRecentSessions(userId, limit);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "获取最近咨询会话成功",
                "data", sessions
            ));
        } catch (Exception e) {
            log.error("获取最近咨询会话失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取最近咨询会话失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取最近的评估记录
     */
    @GetMapping("/recent-assessments")
    public ResponseEntity<?> getRecentAssessments(@RequestParam(defaultValue = "5") int limit) {
        try {
            Long userId = getCurrentUserId();
            List<Map<String, Object>> assessments = userStatsService.getRecentAssessments(userId, limit);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "获取最近评估记录成功",
                "data", assessments
            ));
        } catch (Exception e) {
            log.error("获取最近评估记录失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取最近评估记录失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取用户活跃度统计
     */
    @GetMapping("/activity")
    public ResponseEntity<?> getUserActivityStats() {
        try {
            Long userId = getCurrentUserId();
            Map<String, Object> activity = userStatsService.getUserActivityStats(userId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "获取用户活跃度统计成功",
                "data", activity
            ));
        } catch (Exception e) {
            log.error("获取用户活跃度统计失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "获取活跃度统计失败: " + e.getMessage()
            ));
        }
    }
}

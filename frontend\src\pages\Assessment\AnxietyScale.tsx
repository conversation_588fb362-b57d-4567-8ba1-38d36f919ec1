import React, { useState } from 'react'
import { Card, Typography, Button, Radio, Progress, Result, Space, Divider } from 'antd'
import { 
  ArrowLeftOutlined, 
  ArrowRightOutlined, 
  CheckCircleOutlined,
  HeartOutlined,
  TrophyOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import './AnxietyScale.css'

const { Title, Paragraph, Text } = Typography

// 焦虑自评量表题目
const anxietyQuestions = [
  {
    id: 1,
    question: "我觉得比平常容易紧张和着急",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 2,
    question: "我无缘无故地感到害怕",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 3,
    question: "我容易心里烦乱或觉得惊恐",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 4,
    question: "我觉得我可能将要发疯",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 5,
    question: "我觉得一切都很好，也不会发生什么不幸",
    options: [
      { value: 4, label: "没有或很少时间" },
      { value: 3, label: "小部分时间" },
      { value: 2, label: "相当多时间" },
      { value: 1, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 6,
    question: "我手脚发抖打颤",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 7,
    question: "我因为头痛、颈痛和背痛而苦恼",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 8,
    question: "我感觉容易衰弱和疲乏",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 9,
    question: "我觉得心平气和，并且容易安静坐着",
    options: [
      { value: 4, label: "没有或很少时间" },
      { value: 3, label: "小部分时间" },
      { value: 2, label: "相当多时间" },
      { value: 1, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 10,
    question: "我觉得心跳得很快",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 11,
    question: "我因为一阵阵头晕而苦恼",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 12,
    question: "我有晕倒发作，或觉得要晕倒似的",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 13,
    question: "我吸气呼气都感到很容易",
    options: [
      { value: 4, label: "没有或很少时间" },
      { value: 3, label: "小部分时间" },
      { value: 2, label: "相当多时间" },
      { value: 1, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 14,
    question: "我的手脚麻木和刺痛",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 15,
    question: "我因为胃痛和消化不良而苦恼",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 16,
    question: "我常常要小便",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 17,
    question: "我的手脚常常是干燥温暖的",
    options: [
      { value: 4, label: "没有或很少时间" },
      { value: 3, label: "小部分时间" },
      { value: 2, label: "相当多时间" },
      { value: 1, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 18,
    question: "我脸红发热",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 19,
    question: "我容易入睡并且一夜睡得很好",
    options: [
      { value: 4, label: "没有或很少时间" },
      { value: 3, label: "小部分时间" },
      { value: 2, label: "相当多时间" },
      { value: 1, label: "绝大部分或全部时间" }
    ]
  },
  {
    id: 20,
    question: "我做恶梦",
    options: [
      { value: 1, label: "没有或很少时间" },
      { value: 2, label: "小部分时间" },
      { value: 3, label: "相当多时间" },
      { value: 4, label: "绝大部分或全部时间" }
    ]
  }
]

const AnxietyScale: React.FC = () => {
  const navigate = useNavigate()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<{ [key: number]: number }>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [result, setResult] = useState<any>(null)

  // 处理答案选择
  const handleAnswerChange = (questionId: number, value: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }))
  }

  // 下一题
  const handleNext = () => {
    if (currentQuestion < anxietyQuestions.length - 1) {
      setCurrentQuestion(prev => prev + 1)
    } else {
      // 完成测试，计算结果
      calculateResult()
    }
  }

  // 上一题
  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1)
    }
  }

  // 计算结果
  const calculateResult = () => {
    const totalScore = Object.values(answers).reduce((sum, score) => sum + score, 0)
    const standardScore = Math.round((totalScore / 80) * 100) // 标准分

    let level = ''
    let description = ''
    let suggestions: string[] = []

    if (standardScore < 50) {
      level = '正常范围'
      description = '您的焦虑水平在正常范围内，心理状态良好。'
      suggestions = [
        '保持良好的生活习惯',
        '适当运动，保持身心健康',
        '学会放松技巧，如深呼吸、冥想等'
      ]
    } else if (standardScore < 60) {
      level = '轻度焦虑'
      description = '您可能存在轻度的焦虑情绪，建议关注并采取一些调节措施。'
      suggestions = [
        '学习压力管理技巧',
        '保持规律的作息时间',
        '与朋友家人分享您的感受',
        '考虑学习放松训练'
      ]
    } else if (standardScore < 70) {
      level = '中度焦虑'
      description = '您的焦虑水平较高，建议寻求专业帮助或采取积极的应对措施。'
      suggestions = [
        '建议咨询心理健康专家',
        '学习认知行为技巧',
        '规律运动，改善睡眠',
        '避免过度使用咖啡因和酒精'
      ]
    } else {
      level = '重度焦虑'
      description = '您的焦虑水平很高，强烈建议寻求专业的心理健康服务。'
      suggestions = [
        '立即寻求专业心理咨询',
        '考虑药物治疗的可能性',
        '建立强有力的社会支持系统',
        '学习应急应对策略'
      ]
    }

    setResult({
      totalScore,
      standardScore,
      level,
      description,
      suggestions
    })
    setIsCompleted(true)
  }

  // 重新开始
  const handleRestart = () => {
    setCurrentQuestion(0)
    setAnswers({})
    setIsCompleted(false)
    setResult(null)
  }

  // 返回评估列表
  const handleBackToList = () => {
    navigate('/assessment')
  }

  if (isCompleted && result) {
    return (
      <div className="anxiety-scale-container">
        <div className="result-container">
          <Card className="result-card">
            <Result
              icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              title="测试完成"
              subTitle="感谢您完成焦虑自评量表"
            />
            
            <div className="result-content">
              <div className="score-section">
                <Title level={3}>您的评估结果</Title>
                <div className="score-display">
                  <div className="score-item">
                    <Text type="secondary">总分</Text>
                    <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                      {result.totalScore}/80
                    </Title>
                  </div>
                  <div className="score-item">
                    <Text type="secondary">标准分</Text>
                    <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                      {result.standardScore}
                    </Title>
                  </div>
                </div>
                
                <div className="level-badge">
                  <Text strong style={{ fontSize: '18px' }}>{result.level}</Text>
                </div>
              </div>

              <Divider />

              <div className="description-section">
                <Title level={4}>结果说明</Title>
                <Paragraph>{result.description}</Paragraph>
              </div>

              <div className="suggestions-section">
                <Title level={4}>建议</Title>
                <ul className="suggestions-list">
                  {result.suggestions.map((suggestion: string, index: number) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>

              <div className="disclaimer">
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  * 此测试结果仅供参考，不能替代专业医疗诊断。如有严重心理困扰，请及时寻求专业帮助。
                </Text>
              </div>
            </div>

            <div className="result-actions">
              <Space size="large">
                <Button onClick={handleRestart} icon={<HeartOutlined />}>
                  重新测试
                </Button>
                <Button type="primary" onClick={handleBackToList} icon={<TrophyOutlined />}>
                  更多测试
                </Button>
              </Space>
            </div>
          </Card>
        </div>
      </div>
    )
  }

  const currentQ = anxietyQuestions[currentQuestion]
  const progress = ((currentQuestion + 1) / anxietyQuestions.length) * 100
  const isAnswered = answers[currentQ.id] !== undefined

  return (
    <div className="anxiety-scale-container">
      <div className="test-container">
        {/* 头部 */}
        <Card className="test-header">
          <div className="header-content">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBackToList}
              className="back-button"
            >
              返回
            </Button>
            <div className="test-info">
              <Title level={4} style={{ margin: 0 }}>焦虑自评量表 (SAS)</Title>
              <Text type="secondary">
                第 {currentQuestion + 1} 题 / 共 {anxietyQuestions.length} 题
              </Text>
            </div>
          </div>
          <Progress 
            percent={progress} 
            showInfo={false} 
            strokeColor="#1890ff"
            className="progress-bar"
          />
        </Card>

        {/* 题目卡片 */}
        <Card className="question-card">
          <div className="question-content">
            <Title level={3} className="question-title">
              {currentQ.question}
            </Title>
            
            <Radio.Group
              value={answers[currentQ.id]}
              onChange={(e) => handleAnswerChange(currentQ.id, e.target.value)}
              className="options-group"
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {currentQ.options.map(option => (
                  <Radio key={option.value} value={option.value} className="option-radio">
                    {option.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </div>

          <div className="question-actions">
            <Button 
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              icon={<ArrowLeftOutlined />}
            >
              上一题
            </Button>
            <Button 
              type="primary"
              onClick={handleNext}
              disabled={!isAnswered}
              icon={<ArrowRightOutlined />}
            >
              {currentQuestion === anxietyQuestions.length - 1 ? '完成测试' : '下一题'}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default AnxietyScale

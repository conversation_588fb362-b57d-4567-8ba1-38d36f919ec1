-- 创建用户详细资料表
-- 用于存储用户的详细个人信息，帮助咨询师更好地了解用户

CREATE TABLE user_profiles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL UNIQUE COMMENT '关联的用户ID',
    real_name VARCHAR(50) COMMENT '真实姓名',
    birth_date DATE COMMENT '出生日期',
    marital_status VARCHAR(20) COMMENT '婚姻状况：single-未婚，married-已婚，divorced-离异，widowed-丧偶',
    education VARCHAR(20) COMMENT '教育程度：primary-小学，middle-初中，high-高中，college-大专，bachelor-本科，master-硕士，doctor-博士',
    occupation VARCHAR(100) COMMENT '职业',
    income VARCHAR(50) COMMENT '月收入范围',
    province VARCHAR(50) COMMENT '居住地址-省份',
    city VARCHAR(50) COMMENT '居住地址-城市',
    district VARCHAR(50) COMMENT '居住地址-区县',
    emergency_contact_name VARCHAR(50) COMMENT '紧急联系人姓名',
    emergency_contact_relationship VARCHAR(20) COMMENT '紧急联系人关系',
    emergency_contact_phone VARCHAR(20) COMMENT '紧急联系人电话',
    medical_history TEXT COMMENT '既往病史（JSON格式存储数组）',
    psychological_history TEXT COMMENT '心理健康史（JSON格式存储数组）',
    current_medications TEXT COMMENT '当前用药情况',
    allergies TEXT COMMENT '过敏史',
    smoking BOOLEAN DEFAULT FALSE COMMENT '是否吸烟',
    drinking BOOLEAN DEFAULT FALSE COMMENT '是否饮酒',
    exercise_frequency VARCHAR(50) COMMENT '运动频率',
    sleep_duration VARCHAR(50) COMMENT '睡眠时长',
    personality_traits TEXT COMMENT '性格特征（JSON格式存储数组）',
    interests TEXT COMMENT '兴趣爱好（JSON格式存储数组）',
    stress_factors TEXT COMMENT '压力来源（JSON格式存储数组）',
    coping_methods TEXT COMMENT '应对方式（JSON格式存储数组）',
    goals TEXT COMMENT '咨询目标',
    additional_info TEXT COMMENT '补充信息',
    show_real_name BOOLEAN DEFAULT TRUE COMMENT '隐私设置-是否显示真实姓名',
    show_age BOOLEAN DEFAULT TRUE COMMENT '隐私设置-是否显示年龄',
    show_location BOOLEAN DEFAULT TRUE COMMENT '隐私设置-是否显示位置信息',
    allow_data_sharing BOOLEAN DEFAULT FALSE COMMENT '隐私设置-是否允许数据共享用于研究',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_profiles_user_id (user_id),
    INDEX idx_user_profiles_create_time (create_time)
) COMMENT='用户详细资料表';

/* 基础Markdown样式 */
.markdown-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #374151;
  word-wrap: break-word;
}

/* 用户消息样式 */
.user-message {
  font-size: 15px;
  line-height: 1.6;
  color: inherit;
  word-wrap: break-word;
}

/* 聊天消息中的Markdown样式 */
.chat-message .markdown-renderer {
  font-size: 15px;
  line-height: 1.5;
  color: inherit;
}

.chat-message .markdown-renderer .w-md-editor-preview {
  background: transparent !important;
  color: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  padding: 0 !important;
}

.chat-message .markdown-renderer .w-md-editor-preview p {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.chat-message .markdown-renderer .w-md-editor-preview p:first-child {
  margin-top: 0;
}

.chat-message .markdown-renderer .w-md-editor-preview p:last-child {
  margin-bottom: 0;
}

.chat-message .markdown-renderer .w-md-editor-preview ul,
.chat-message .markdown-renderer .w-md-editor-preview ol {
  margin: 0.25rem 0;
  padding-left: 1rem;
}

.chat-message .markdown-renderer .w-md-editor-preview li {
  margin: 0.125rem 0;
  line-height: 1.4;
}

.chat-message .markdown-renderer .w-md-editor-preview strong {
  font-weight: 600;
  color: inherit;
}

.chat-message .markdown-renderer .w-md-editor-preview em {
  font-style: italic;
  color: inherit;
}

.chat-message .markdown-renderer .w-md-editor-preview code {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.875em;
}

.chat-message .markdown-renderer .w-md-editor-preview pre {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.375rem;
  padding: 0.5rem;
  margin: 0.25rem 0;
  font-size: 0.875rem;
}

.chat-message .markdown-renderer .w-md-editor-preview blockquote {
  border-left: 3px solid #6366f1;
  background: rgba(99, 102, 241, 0.05);
  margin: 0.25rem 0;
  padding: 0.5rem 0.75rem;
  border-radius: 0 0.375rem 0.375rem 0;
}

/* 通用Markdown样式覆盖 */
.markdown-renderer .w-md-editor-preview {
  background: transparent !important;
  color: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  padding: 0 !important;
}

.markdown-renderer .w-md-editor-preview h1,
.markdown-renderer .w-md-editor-preview h2,
.markdown-renderer .w-md-editor-preview h3,
.markdown-renderer .w-md-editor-preview h4,
.markdown-renderer .w-md-editor-preview h5,
.markdown-renderer .w-md-editor-preview h6 {
  color: inherit !important;
  margin: 0.5rem 0 0.25rem 0;
  line-height: 1.4;
}

.markdown-renderer .w-md-editor-preview p {
  margin: 0.375rem 0;
  line-height: 1.5;
}

.markdown-renderer .w-md-editor-preview ul,
.markdown-renderer .w-md-editor-preview ol {
  margin: 0.375rem 0;
  padding-left: 1.25rem;
}

.markdown-renderer .w-md-editor-preview li {
  margin: 0.125rem 0;
  line-height: 1.4;
}

.markdown-renderer .w-md-editor-preview strong {
  font-weight: 600;
  color: inherit;
}

.markdown-renderer .w-md-editor-preview em {
  font-style: italic;
  color: inherit;
}

.markdown-renderer .w-md-editor-preview code {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.875em;
  font-weight: 500;
}

.markdown-renderer .w-md-editor-preview pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.markdown-renderer .w-md-editor-preview blockquote {
  border-left: 3px solid #6366f1;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  margin: 0.5rem 0;
  padding: 0.75rem 1rem;
  border-radius: 0 0.375rem 0.375rem 0;
  font-style: italic;
  color: #4b5563;
}

.markdown-renderer .w-md-editor-preview table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 0.5rem;
  overflow: hidden;
  margin: 0.5rem 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.markdown-renderer .w-md-editor-preview th {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
}

.markdown-renderer .w-md-editor-preview td {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  vertical-align: top;
}

.markdown-renderer .w-md-editor-preview tr:nth-child(even) {
  background: #f8fafc;
}

.markdown-renderer .w-md-editor-preview tr:hover {
  background: rgba(99, 102, 241, 0.05);
}

.markdown-renderer .w-md-editor-preview a {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.markdown-renderer .w-md-editor-preview a:hover {
  color: #4f46e5;
  border-bottom-color: #6366f1;
  text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-renderer .w-md-editor-preview h1 {
    font-size: 1.5rem;
  }
  
  .markdown-renderer .w-md-editor-preview h2 {
    font-size: 1.25rem;
  }
  
  .markdown-renderer .w-md-editor-preview h3 {
    font-size: 1.125rem;
  }
  
  .markdown-renderer .w-md-editor-preview pre {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  
  .markdown-renderer .w-md-editor-preview th,
  .markdown-renderer .w-md-editor-preview td {
    padding: 0.5rem 0.75rem;
  }
}

/* 自定义Markdown样式 - 减少底部边距 */
.wmde-markdown p,
.wmde-markdown blockquote,
.wmde-markdown ul,
.wmde-markdown ol,
.wmde-markdown dl,
.wmde-markdown table,
.wmde-markdown pre,
.wmde-markdown details {
  margin-bottom: -16px;
}

/* 焦虑量表测试页面样式 */
.anxiety-scale-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.result-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

/* 测试头部 */
.test-header {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.back-button {
  border-radius: 8px !important;
}

.test-info {
  text-align: center;
  flex: 1;
  margin: 0 20px;
}

.progress-bar {
  margin: 0;
}

.progress-bar .ant-progress-bg {
  transition: all 0.3s ease;
}

/* 题目卡片 */
.question-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.question-card .ant-card-body {
  padding: 32px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-title {
  color: #1a1a1a !important;
  margin-bottom: 32px !important;
  font-size: 20px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
  text-align: center;
}

/* 选项组 */
.options-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.option-radio {
  padding: 16px 20px !important;
  border: 2px solid #f0f0f0 !important;
  border-radius: 12px !important;
  margin: 0 !important;
  transition: all 0.3s ease !important;
  background: #fafafa;
}

.option-radio:hover {
  border-color: #1890ff !important;
  background: #f0f8ff;
}

.option-radio.ant-radio-wrapper-checked {
  border-color: #1890ff !important;
  background: #e6f7ff !important;
}

.option-radio .ant-radio {
  margin-right: 12px;
}

.option-radio .ant-radio-inner {
  width: 18px;
  height: 18px;
}

/* 题目操作按钮 */
.question-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.question-actions .ant-btn {
  height: 40px !important;
  padding: 0 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* 结果卡片 */
.result-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-card .ant-card-body {
  padding: 40px;
}

.result-content {
  margin-top: 24px;
}

/* 分数展示 */
.score-section {
  text-align: center;
  margin-bottom: 32px;
}

.score-display {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin: 24px 0;
}

.score-item {
  text-align: center;
}

.level-badge {
  margin-top: 16px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  border-radius: 20px;
  display: inline-block;
}

/* 描述和建议 */
.description-section,
.suggestions-section {
  margin-bottom: 24px;
}

.suggestions-list {
  margin: 16px 0;
  padding-left: 20px;
}

.suggestions-list li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #666;
}

/* 免责声明 */
.disclaimer {
  margin-top: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ffa940;
}

/* 结果操作按钮 */
.result-actions {
  margin-top: 32px;
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.result-actions .ant-btn {
  height: 40px !important;
  padding: 0 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .anxiety-scale-container {
    padding: 12px;
  }
  
  .test-container,
  .result-container {
    max-width: 100%;
  }
  
  .test-header {
    margin-bottom: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .test-info {
    margin: 0;
  }
  
  .question-card .ant-card-body {
    padding: 20px;
  }
  
  .question-title {
    font-size: 18px !important;
    margin-bottom: 24px !important;
  }
  
  .option-radio {
    padding: 12px 16px !important;
  }
  
  .question-actions {
    margin-top: 24px;
    padding-top: 16px;
  }
  
  .result-card .ant-card-body {
    padding: 24px;
  }
  
  .score-display {
    gap: 40px;
  }
  
  .score-item {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .anxiety-scale-container {
    padding: 8px;
  }
  
  .question-card .ant-card-body {
    padding: 16px;
  }
  
  .question-title {
    font-size: 16px !important;
    margin-bottom: 20px !important;
  }
  
  .option-radio {
    padding: 10px 12px !important;
    font-size: 14px;
  }
  
  .question-actions .ant-btn {
    height: 36px !important;
    padding: 0 16px !important;
    font-size: 14px;
  }
  
  .result-card .ant-card-body {
    padding: 16px;
  }
  
  .score-display {
    flex-direction: column;
    gap: 20px;
  }
  
  .level-badge {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .result-actions .ant-btn {
    height: 36px !important;
    padding: 0 16px !important;
    font-size: 14px;
  }
}

/* 动画效果 */
.question-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.option-radio {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 进度条自定义样式 */
.progress-bar .ant-progress-outer {
  padding-right: 0 !important;
}

.progress-bar .ant-progress-inner {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.progress-bar .ant-progress-bg {
  border-radius: 10px;
  background: linear-gradient(90deg, #1890ff 0%, #722ed1 100%);
}

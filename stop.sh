#!/bin/bash

echo "================================"
echo "Stopping Buwan Psychology Platform"
echo "================================"

echo ""
echo "Stopping services..."

# 从PID文件停止服务
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "Stopping backend (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
    fi
    rm -f backend.pid
fi

if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "Stopping frontend (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
    fi
    rm -f frontend.pid
fi

# 备用方法：按进程名停止
echo "Stopping any remaining Java processes..."
pkill -f "spring-boot:run" 2>/dev/null

echo "Stopping any remaining Node.js processes..."
pkill -f "npm run dev" 2>/dev/null

echo ""
echo "All services stopped."
echo ""

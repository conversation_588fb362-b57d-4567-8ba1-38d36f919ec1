import React, { useEffect, useState } from 'react'
import { List, Avatar, Typography, Tag, Spin, Badge } from 'antd'
import { MessageOutlined, ClockCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { Text } = Typography

interface Counselor {
  id: number
  name: string
  title?: string
  avatar?: string
  specializations?: string
  rating?: number
  experience?: number
}

interface Session {
  id: number
  title: string
  sessionType: number
  status: number
  createTime: string
  updateTime: string
  counselorId?: number
  counselor?: Counselor
}

interface SessionListProps {
  sessions: Session[]
  currentSession: Session | null
  onSelectSession: (session: Session) => void
  loading: boolean
}

const SessionList: React.FC<SessionListProps> = ({
  sessions,
  currentSession,
  onSelectSession,
  loading
}) => {
  const [newSessionIds, setNewSessionIds] = useState<Set<number>>(new Set())

  // 检测新创建的会话
  useEffect(() => {
    if (sessions.length > 0) {
      const latestSession = sessions[0]
      const now = dayjs()
      const sessionCreateTime = dayjs(latestSession.createTime)

      // 如果会话是在最近30秒内创建的，标记为新会话
      if (now.diff(sessionCreateTime, 'second') < 30) {
        setNewSessionIds(prev => new Set(prev).add(latestSession.id))

        // 5秒后移除新会话标记
        setTimeout(() => {
          setNewSessionIds(prev => {
            const newSet = new Set(prev)
            newSet.delete(latestSession.id)
            return newSet
          })
        }, 5000)
      }
    }
  }, [sessions])

  const handleSelectSession = (session: Session) => {
    // 选择会话时移除新会话标记
    setNewSessionIds(prev => {
      const newSet = new Set(prev)
      newSet.delete(session.id)
      return newSet
    })
    onSelectSession(session)
  }
  const getSessionTypeTag = (type: number) => {
    const types = {
      0: { text: '一般咨询', color: 'default' },
      1: { text: '焦虑问题', color: 'orange' },
      2: { text: '抑郁问题', color: 'red' },
      3: { text: '学习压力', color: 'blue' },
      4: { text: '人际关系', color: 'green' },
      5: { text: '其他', color: 'purple' }
    }
    const typeInfo = types[type as keyof typeof types] || types[0]
    return (
      <Tag
        color={typeInfo.color}
        onClick={(e) => {
          console.log('Type tag clicked, preventing default and stopping propagation')
          e.preventDefault()
          e.stopPropagation()
          return false
        }}
        style={{ cursor: 'default' }}
      >
        {typeInfo.text}
      </Tag>
    )
  }

  const getStatusTag = (status: number) => {
    const statuses = {
      0: { text: '进行中', color: 'processing' },
      1: { text: '已结束', color: 'success' },
      2: { text: '已暂停', color: 'warning' }
    }
    const statusInfo = statuses[status as keyof typeof statuses] || statuses[0]
    return (
      <Tag
        color={statusInfo.color}
        onClick={(e) => {
          console.log('Status tag clicked, preventing default and stopping propagation')
          e.preventDefault()
          e.stopPropagation()
          return false
        }}
        style={{ cursor: 'default' }}
      >
        {statusInfo.text}
      </Tag>
    )
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <List
      dataSource={sessions}
      renderItem={(session) => (
        <Badge
          dot={newSessionIds.has(session.id)}
          color="#52c41a"
          offset={[-5, 5]}
        >
          <List.Item
            className={`session-item ${currentSession?.id === session.id ? 'active' : ''} ${newSessionIds.has(session.id) ? 'new-session' : ''}`}
            onClick={() => handleSelectSession(session)}
            style={{
              cursor: 'pointer',
              padding: '12px 16px',
              backgroundColor: currentSession?.id === session.id ? '#f0f8ff' :
                              newSessionIds.has(session.id) ? '#f6ffed' : 'transparent',
              borderLeft: currentSession?.id === session.id ? '3px solid #1890ff' :
                         newSessionIds.has(session.id) ? '3px solid #52c41a' : '3px solid transparent',
              transition: 'all 0.3s ease'
            }}
          >
          <List.Item.Meta
            avatar={
              session.counselor?.avatar ? (
                <Avatar
                  src={session.counselor.avatar}
                  size={40}
                />
              ) : (
                <Avatar
                  icon={<MessageOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                  size={40}
                />
              )
            }
            title={
              <div>
                <Text strong ellipsis style={{ maxWidth: '200px' }}>
                  {session.title}
                </Text>
                <div style={{ marginTop: '4px' }} onClick={(e) => e.stopPropagation()}>
                  {getSessionTypeTag(session.sessionType)}
                  {getStatusTag(session.status)}
                </div>
              </div>
            }
            description={
              <div>
                {session.counselor && (
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      咨询师：{session.counselor.name}
                      {session.counselor.title && ` · ${session.counselor.title}`}
                    </Text>
                  </div>
                )}
                <div style={{ display: 'flex', alignItems: 'center', color: '#999' }}>
                  <ClockCircleOutlined style={{ marginRight: '4px' }} />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {dayjs(session.createTime).format('MM-DD HH:mm')}
                  </Text>
                </div>
              </div>
            }
          />
        </List.Item>
        </Badge>
      )}
    />
  )
}

export default SessionList

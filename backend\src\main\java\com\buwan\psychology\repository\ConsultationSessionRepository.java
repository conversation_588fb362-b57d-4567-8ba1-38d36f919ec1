package com.buwan.psychology.repository;

import com.buwan.psychology.entity.ConsultationSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 咨询会话数据访问接口
 */
@Repository
public interface ConsultationSessionRepository extends JpaRepository<ConsultationSession, Long> {

    /**
     * 根据用户ID查找会话列表，按创建时间倒序
     */
    List<ConsultationSession> findByUserIdOrderByCreateTimeDesc(Long userId);

    /**
     * 根据用户ID查找会话列表，包含咨询师信息，按创建时间倒序
     */
    @Query("SELECT s FROM ConsultationSession s LEFT JOIN FETCH s.counselor WHERE s.userId = :userId ORDER BY s.createTime DESC")
    List<ConsultationSession> findByUserIdWithCounselorOrderByCreateTimeDesc(@Param("userId") Long userId);

    /**
     * 根据用户ID和状态查找会话列表
     */
    List<ConsultationSession> findByUserIdAndStatusOrderByCreateTimeDesc(Long userId, Integer status);

    /**
     * 根据会话类型查找会话列表
     */
    List<ConsultationSession> findBySessionTypeOrderByCreateTimeDesc(Integer sessionType);

    /**
     * 查找指定时间范围内的会话
     */
    @Query("SELECT s FROM ConsultationSession s WHERE s.createTime BETWEEN :startTime AND :endTime ORDER BY s.createTime DESC")
    List<ConsultationSession> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 根据紧急程度查找会话
     */
    List<ConsultationSession> findByUrgencyLevelOrderByCreateTimeDesc(Integer urgencyLevel);

    /**
     * 查找活跃会话（状态为进行中）
     */
    @Query("SELECT s FROM ConsultationSession s WHERE s.status = 0 ORDER BY s.updateTime DESC")
    List<ConsultationSession> findActiveSessions();

    /**
     * 统计用户的会话数量
     */
    long countByUserId(Long userId);

    /**
     * 统计指定状态的会话数量
     */
    long countByStatus(Integer status);

    /**
     * 查找用户指定时间后的会话
     */
    @Query("SELECT s FROM ConsultationSession s WHERE s.userId = :userId AND s.createTime >= :startTime ORDER BY s.createTime DESC")
    List<ConsultationSession> findByUserIdAndCreateTimeAfter(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime);

    /**
     * 查找用户最近的一个会话
     */
    @Query("SELECT s FROM ConsultationSession s WHERE s.userId = :userId ORDER BY s.createTime DESC LIMIT 1")
    List<ConsultationSession> findTop1ByUserIdOrderByCreateTimeDesc(@Param("userId") Long userId);
}

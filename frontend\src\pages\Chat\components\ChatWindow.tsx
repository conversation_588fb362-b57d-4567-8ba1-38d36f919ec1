import React, { useState, useEffect, useRef } from 'react'
import { Card, Input, Button, Avatar, Typography, Space, Spin, message } from 'antd'
import { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons'
import { useSelector, useDispatch } from 'react-redux'
import { RootState, AppDispatch } from '@/store'
import { fetchMessages, sendMessage } from '@/store/slices/chatSlice'
import dayjs from 'dayjs'
import Markdown<PERSON>enderer from '../../../components/MarkdownRenderer'

const { Text } = Typography
const { TextArea } = Input

interface ChatWindowProps {
  session: any
  sending?: boolean
}

const ChatWindow: React.FC<ChatWindowProps> = ({ session, sending: isSending = false }) => {
  const dispatch = useDispatch<AppDispatch>()
  const { messages, sending } = useSelector((state: RootState) => state.chat)
  // const { user } = useSelector((state: RootState) => state.auth)
  const [inputValue, setInputValue] = useState('')
  const [autoScroll, setAutoScroll] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (session?.id) {
      dispatch(fetchMessages(session.id))
      // 重置自动滚动状态
      setAutoScroll(true)
    }
  }, [dispatch, session?.id])

  useEffect(() => {
    // 只有在自动滚动开启时才滚动到底部
    if (autoScroll && messages.length > 0) {
      scrollToBottom()
    }
  }, [messages, autoScroll])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 检测用户是否手动滚动
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 50 // 50px 容差
    setAutoScroll(isAtBottom)
  }

  const handleSend = async () => {
    if (!inputValue.trim() || sending) return

    try {
      // 发送消息前启用自动滚动
      setAutoScroll(true)

      await dispatch(sendMessage({
        sessionId: session.id,
        content: inputValue.trim()
      })).unwrap()

      setInputValue('')
    } catch (error) {
      message.error('发送消息失败')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const renderMessage = (msg: any) => {
    const isUser = msg.messageType === 0
    const isAi = msg.messageType === 1
    
    return (
      <div
        key={msg.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: '16px'
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: isUser ? 'row-reverse' : 'row',
            alignItems: 'flex-start',
            maxWidth: '70%'
          }}
        >
          <Avatar
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              background: isUser
                ? 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
                : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
              margin: isUser ? '0 0 0 12px' : '0 12px 0 0',
              boxShadow: isUser
                ? '0 4px 12px rgba(99, 102, 241, 0.3)'
                : '0 4px 12px rgba(16, 185, 129, 0.3)',
              border: '2px solid rgba(255, 255, 255, 0.9)',
              width: '40px',
              height: '40px'
            }}
          />
          
          <div>
            <div
              style={{
                background: isUser
                  ? 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
                  : 'rgba(255, 255, 255, 0.95)',
                color: isUser ? 'white' : '#334155',
                padding: '14px 18px',
                borderRadius: '18px',
                borderTopLeftRadius: isUser ? '18px' : '6px',
                borderTopRightRadius: isUser ? '6px' : '18px',
                wordBreak: 'break-word',
                lineHeight: '1.6',
                boxShadow: isUser
                  ? '0 4px 16px rgba(99, 102, 241, 0.25)'
                  : '0 2px 12px rgba(0, 0, 0, 0.08)',
                border: isUser ? 'none' : '1px solid rgba(226, 232, 240, 0.5)',
                backdropFilter: isUser ? 'none' : 'blur(10px)',
                fontSize: '15px',
                fontWeight: isUser ? '500' : '400'
              }}
            >
              {isAi ? (
                <div className="chat-message">
                  <MarkdownRenderer content={msg.content} />
                </div>
              ) : (
                <div className="user-message">
                  {msg.content}
                  {sending && msg.id < 0 && (
                    <div style={{ 
                      fontSize: '12px', 
                      opacity: 0.7, 
                      marginTop: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        animation: 'pulse 1.5s infinite'
                      }}></div>
                      发送中...
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div
              style={{
                textAlign: isUser ? 'right' : 'left',
                marginTop: '4px'
              }}
            >
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {dayjs(msg.createTime).format('HH:mm')}
              </Text>
              
              {msg.hasRiskSignal && (
                <Text type="danger" style={{ fontSize: '12px', marginLeft: '8px' }}>
                  ⚠️ 需要关注
                </Text>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card
      title={
        <Space>
          {session.counselor?.avatar ? (
            <Avatar
              src={session.counselor.avatar}
              size={32}
            />
          ) : (
            <Avatar
              icon={<RobotOutlined />}
              style={{ backgroundColor: '#1890ff' }}
              size={32}
            />
          )}
          <span>{session.title}</span>
        </Space>
      }
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
    >
      {/* 消息列表 */}
      <div
        ref={messagesContainerRef}
        onScroll={handleScroll}
        style={{
          flex: 1,
          padding: '20px',
          overflowY: 'auto',
          background: 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%)',
          backdropFilter: 'blur(10px)'
        }}
      >
        {messages.length === 0 ? (
          <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '16px' }}>
            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', maxWidth: '70%' }}>
              {session.counselor?.avatar ? (
                <Avatar
                  src={session.counselor.avatar}
                  style={{
                    margin: '0 12px 0 0',
                    boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
                    border: '2px solid rgba(255, 255, 255, 0.9)',
                    width: '40px',
                    height: '40px'
                  }}
                />
              ) : (
                <Avatar
                  icon={<RobotOutlined />}
                  style={{
                    background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                    margin: '0 12px 0 0',
                    boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
                    border: '2px solid rgba(255, 255, 255, 0.9)',
                    width: '40px',
                    height: '40px'
                  }}
                />
              )}

              <div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.95)',
                  color: '#334155',
                  padding: '14px 18px',
                  borderRadius: '18px',
                  borderTopLeftRadius: '6px',
                  borderTopRightRadius: '18px',
                  wordBreak: 'break-word',
                  lineHeight: '1.6',
                  boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
                  border: '1px solid rgba(226, 232, 240, 0.5)',
                  backdropFilter: 'blur(10px)',
                  fontSize: '15px',
                  fontWeight: '400'
                }}>
                  {session.counselor?.welcomeMessage ||
                   `您好！我是${session.counselor?.name || 'AI心理咨询师'}，很高兴为您提供心理咨询服务。请告诉我您遇到的问题，我会尽力帮助您。`}
                </div>

                <div style={{ textAlign: 'left', marginTop: '4px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    刚刚
                  </Text>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* 显示欢迎语作为第一条消息 */}
            {session.counselor && (
              <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '16px' }}>
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', maxWidth: '70%' }}>
                  {session.counselor.avatar ? (
                    <Avatar
                      src={session.counselor.avatar}
                      style={{
                        margin: '0 12px 0 0',
                        boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
                        border: '2px solid rgba(255, 255, 255, 0.9)',
                        width: '40px',
                        height: '40px'
                      }}
                    />
                  ) : (
                    <Avatar
                      icon={<RobotOutlined />}
                      style={{
                        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                        margin: '0 12px 0 0',
                        boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
                        border: '2px solid rgba(255, 255, 255, 0.9)',
                        width: '40px',
                        height: '40px'
                      }}
                    />
                  )}

                  <div>
                    <div style={{
                      background: 'rgba(255, 255, 255, 0.95)',
                      color: '#334155',
                      padding: '14px 18px',
                      borderRadius: '18px',
                      borderTopLeftRadius: '6px',
                      borderTopRightRadius: '18px',
                      wordBreak: 'break-word',
                      lineHeight: '1.6',
                      boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
                      border: '1px solid rgba(226, 232, 240, 0.5)',
                      backdropFilter: 'blur(10px)',
                      fontSize: '15px',
                      fontWeight: '400'
                    }}>
                      {session.counselor.welcomeMessage ||
                       `您好！我是${session.counselor.name}，很高兴为您提供心理咨询服务。请告诉我您遇到的问题，我会尽力帮助您。`}
                    </div>

                    <div style={{ textAlign: 'left', marginTop: '4px' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {dayjs(session.createTime).format('HH:mm')}
                      </Text>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {messages.map(renderMessage)}
          </>
        )}
        
        {sending && (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <Spin size="small" />
            <Text type="secondary" style={{ marginLeft: '8px' }}>
              AI正在思考中...
            </Text>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div style={{
        padding: '20px',
        borderTop: '1px solid rgba(226, 232, 240, 0.3)',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)'
      }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的问题..."
            autoSize={{ minRows: 1, maxRows: 4 }}
            style={{ resize: 'none' }}
            disabled={sending}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            loading={sending}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </Space.Compact>
        
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>
          按 Enter 发送，Shift + Enter 换行
        </div>
      </div>
    </Card>
  )
}

export default ChatWindow

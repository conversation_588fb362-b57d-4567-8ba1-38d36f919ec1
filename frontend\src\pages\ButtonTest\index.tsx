import React from 'react'
import { Card, Button, Typography, Row, Col, Space } from 'antd'
import { MessageOutlined, HeartOutlined, RocketOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const ButtonTest: React.FC = () => {
  return (
    <div style={{ padding: '40px', background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1} style={{ textAlign: 'center', marginBottom: '40px' }}>
          按钮样式测试
        </Title>

        <Row gutter={[24, 24]}>
          {/* 英雄区域按钮测试 */}
          <Col xs={24}>
            <Card title="英雄区域按钮样式">
              <div style={{ 
                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                padding: '60px 40px',
                borderRadius: '16px',
                textAlign: 'center'
              }}>
                <Title level={2} style={{ color: 'white', marginBottom: '32px' }}>
                  不晚心理
                </Title>
                <Text style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '18px', display: 'block', marginBottom: '40px' }}>
                  专业的心理健康平台
                </Text>
                
                <Space size="large">
                  <Button 
                    size="large" 
                    className="hero-btn-primary"
                    icon={<MessageOutlined />}
                  >
                    开始心理咨询
                  </Button>
                  <Button 
                    size="large" 
                    className="hero-btn-secondary"
                    icon={<HeartOutlined />}
                  >
                    心理健康评估
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>

          {/* CTA按钮测试 */}
          <Col xs={24} md={12}>
            <Card title="CTA按钮样式">
              <div style={{ 
                background: 'rgba(255, 255, 255, 0.8)',
                padding: '40px',
                borderRadius: '12px',
                textAlign: 'center'
              }}>
                <Title level={3} style={{ color: '#1e293b', marginBottom: '16px' }}>
                  开始您的心理健康之旅
                </Title>
                <Text style={{ color: '#475569', fontSize: '16px', display: 'block', marginBottom: '32px' }}>
                  心理健康同样重要，不要等到明天
                </Text>
                
                <Button 
                  size="large" 
                  className="cta-button"
                  icon={<RocketOutlined />}
                >
                  立即开始咨询
                </Button>
              </div>
            </Card>
          </Col>

          {/* 标准按钮对比 */}
          <Col xs={24} md={12}>
            <Card title="标准按钮对比">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <Text strong style={{ display: 'block', marginBottom: '8px' }}>Primary按钮：</Text>
                  <Button type="primary" size="large">
                    标准Primary按钮
                  </Button>
                </div>
                
                <div>
                  <Text strong style={{ display: 'block', marginBottom: '8px' }}>Default按钮：</Text>
                  <Button size="large">
                    标准Default按钮
                  </Button>
                </div>
                
                <div>
                  <Text strong style={{ display: 'block', marginBottom: '8px' }}>Dashed按钮：</Text>
                  <Button type="dashed" size="large">
                    标准Dashed按钮
                  </Button>
                </div>
              </Space>
            </Card>
          </Col>

          {/* 移动端预览 */}
          <Col xs={24}>
            <Card title="移动端按钮预览">
              <div style={{ 
                maxWidth: '375px',
                margin: '0 auto',
                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                padding: '40px 20px',
                borderRadius: '16px',
                textAlign: 'center'
              }}>
                <Title level={3} style={{ color: 'white', marginBottom: '24px' }}>
                  移动端效果
                </Title>
                
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Button 
                    size="large" 
                    className="hero-btn-primary"
                    style={{ width: '100%', height: '52px', fontSize: '16px' }}
                    icon={<MessageOutlined />}
                  >
                    开始心理咨询
                  </Button>
                  <Button 
                    size="large" 
                    className="hero-btn-secondary"
                    style={{ width: '100%', height: '52px', fontSize: '16px' }}
                    icon={<HeartOutlined />}
                  >
                    心理健康评估
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>

          {/* 按钮状态测试 */}
          <Col xs={24}>
            <Card title="按钮状态测试">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <div style={{ textAlign: 'center' }}>
                    <Text strong style={{ display: 'block', marginBottom: '16px' }}>正常状态</Text>
                    <Button className="hero-btn-primary" size="large">
                      主要按钮
                    </Button>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ textAlign: 'center' }}>
                    <Text strong style={{ display: 'block', marginBottom: '16px' }}>加载状态</Text>
                    <Button className="hero-btn-primary" size="large" loading>
                      加载中...
                    </Button>
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ textAlign: 'center' }}>
                    <Text strong style={{ display: 'block', marginBottom: '16px' }}>禁用状态</Text>
                    <Button className="hero-btn-primary" size="large" disabled>
                      禁用按钮
                    </Button>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ButtonTest

import React, { useState } from 'react'
import { Card, Typography, Button, Result, Space, Divider } from 'antd'
import { 
  ArrowLeftOutlined, 
  ArrowRightOutlined, 
  CheckCircleOutlined,
  ExperimentOutlined,
  TrophyOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import './ColorPsychology.css'

const { Title, Paragraph, Text } = Typography

// 色彩选择题目
const colorQuestions = [
  {
    id: 1,
    question: "选择一种最能代表您当前心情的颜色",
    colors: [
      { color: '#FF6B6B', name: '红色', value: 'red' },
      { color: '#4ECDC4', name: '青色', value: 'cyan' },
      { color: '#45B7D1', name: '蓝色', value: 'blue' },
      { color: '#96CEB4', name: '绿色', value: 'green' },
      { color: '#FFEAA7', name: '黄色', value: 'yellow' },
      { color: '#DDA0DD', name: '紫色', value: 'purple' }
    ]
  },
  {
    id: 2,
    question: "如果您的房间只能选择一种主色调，您会选择",
    colors: [
      { color: '#FF7675', name: '温暖红', value: 'warm-red' },
      { color: '#74B9FF', name: '天空蓝', value: 'sky-blue' },
      { color: '#00B894', name: '森林绿', value: 'forest-green' },
      { color: '#FDCB6E', name: '阳光黄', value: 'sunshine-yellow' },
      { color: '#E17055', name: '橙色', value: 'orange' },
      { color: '#A29BFE', name: '薰衣草紫', value: 'lavender' }
    ]
  },
  {
    id: 3,
    question: "在这些颜色中，哪种最让您感到平静",
    colors: [
      { color: '#81ECEC', name: '薄荷绿', value: 'mint' },
      { color: '#FD79A8', name: '粉红色', value: 'pink' },
      { color: '#6C5CE7', name: '深紫色', value: 'deep-purple' },
      { color: '#A0E7E5', name: '浅青色', value: 'light-cyan' },
      { color: '#B2DFDB', name: '海绿色', value: 'sea-green' },
      { color: '#F8BBD9', name: '淡粉色', value: 'light-pink' }
    ]
  },
  {
    id: 4,
    question: "选择一种颜色来描述您的理想工作环境",
    colors: [
      { color: '#00CED1', name: '深青色', value: 'dark-turquoise' },
      { color: '#FF8C00', name: '深橙色', value: 'dark-orange' },
      { color: '#32CD32', name: '酸橙绿', value: 'lime-green' },
      { color: '#9370DB', name: '中紫色', value: 'medium-purple' },
      { color: '#FFD700', name: '金色', value: 'gold' },
      { color: '#DC143C', name: '深红色', value: 'crimson' }
    ]
  },
  {
    id: 5,
    question: "最后，选择一种最能激发您创造力的颜色",
    colors: [
      { color: '#FF1493', name: '深粉色', value: 'deep-pink' },
      { color: '#00FFFF', name: '青色', value: 'aqua' },
      { color: '#FF4500', name: '橙红色', value: 'orange-red' },
      { color: '#9ACD32', name: '黄绿色', value: 'yellow-green' },
      { color: '#8A2BE2', name: '蓝紫色', value: 'blue-violet' },
      { color: '#FFB6C1', name: '浅粉色', value: 'light-pink-2' }
    ]
  }
]

// 性格分析结果
const personalityResults = {
  'creative': {
    title: '创意型人格',
    description: '您是一个富有创造力和想象力的人，喜欢探索新事物，具有艺术天赋。',
    traits: ['富有想象力', '创新思维', '艺术敏感', '独立思考'],
    suggestions: ['从事创意相关工作', '培养艺术爱好', '保持好奇心', '勇于表达自己']
  },
  'calm': {
    title: '平和型人格',
    description: '您是一个内心平静、善于思考的人，喜欢和谐的环境，具有很好的自控力。',
    traits: ['内心平静', '善于倾听', '理性思考', '情绪稳定'],
    suggestions: ['练习冥想', '培养耐心', '成为他人的倾听者', '保持内心平衡']
  },
  'energetic': {
    title: '活力型人格',
    description: '您是一个充满活力和热情的人，喜欢挑战，具有很强的行动力。',
    traits: ['充满活力', '积极主动', '勇于挑战', '领导能力'],
    suggestions: ['参与团队活动', '设定挑战目标', '保持运动习惯', '发挥领导才能']
  },
  'gentle': {
    title: '温和型人格',
    description: '您是一个温和善良的人，重视人际关系，具有很强的同理心。',
    traits: ['温和善良', '富有同情心', '重视关系', '善于合作'],
    suggestions: ['培养人际关系', '参与志愿活动', '练习表达情感', '建立支持网络']
  },
  'rational': {
    title: '理性型人格',
    description: '您是一个理性客观的人，喜欢分析问题，具有很强的逻辑思维能力。',
    traits: ['逻辑思维', '客观分析', '注重效率', '追求真理'],
    suggestions: ['培养分析能力', '学习新知识', '保持客观态度', '追求专业发展']
  }
}

const ColorPsychology: React.FC = () => {
  const navigate = useNavigate()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<{ [key: number]: string }>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [result, setResult] = useState<any>(null)

  // 处理颜色选择
  const handleColorSelect = (questionId: number, colorValue: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: colorValue
    }))
  }

  // 下一题
  const handleNext = () => {
    if (currentQuestion < colorQuestions.length - 1) {
      setCurrentQuestion(prev => prev + 1)
    } else {
      // 完成测试，分析结果
      analyzeResult()
    }
  }

  // 上一题
  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1)
    }
  }

  // 分析结果
  const analyzeResult = () => {
    const colorChoices = Object.values(answers)
    
    // 简单的性格分析逻辑
    let personalityType = 'calm' // 默认
    
    const colorCounts = {
      warm: 0, // 红、橙、黄
      cool: 0, // 蓝、绿、紫
      bright: 0, // 明亮色彩
      soft: 0   // 柔和色彩
    }

    colorChoices.forEach(choice => {
      if (['red', 'warm-red', 'orange', 'sunshine-yellow', 'gold', 'orange-red', 'crimson'].includes(choice)) {
        colorCounts.warm++
      }
      if (['blue', 'cyan', 'sky-blue', 'forest-green', 'deep-purple', 'dark-turquoise'].includes(choice)) {
        colorCounts.cool++
      }
      if (['yellow', 'sunshine-yellow', 'gold', 'lime-green', 'aqua'].includes(choice)) {
        colorCounts.bright++
      }
      if (['mint', 'pink', 'light-cyan', 'sea-green', 'light-pink', 'lavender'].includes(choice)) {
        colorCounts.soft++
      }
    })

    // 根据颜色偏好确定性格类型
    if (colorCounts.warm >= 3) {
      personalityType = 'energetic'
    } else if (colorCounts.cool >= 3) {
      personalityType = 'rational'
    } else if (colorCounts.bright >= 2) {
      personalityType = 'creative'
    } else if (colorCounts.soft >= 2) {
      personalityType = 'gentle'
    }

    setResult({
      personalityType,
      ...personalityResults[personalityType],
      colorChoices
    })
    setIsCompleted(true)
  }

  // 重新开始
  const handleRestart = () => {
    setCurrentQuestion(0)
    setAnswers({})
    setIsCompleted(false)
    setResult(null)
  }

  // 返回评估列表
  const handleBackToList = () => {
    navigate('/assessment')
  }

  if (isCompleted && result) {
    return (
      <div className="color-psychology-container">
        <div className="result-container">
          <Card className="result-card">
            <Result
              icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              title="色彩心理分析完成"
              subTitle="通过您的色彩偏好，我们分析出了您的性格特征"
            />
            
            <div className="result-content">
              <div className="personality-section">
                <Title level={3}>{result.title}</Title>
                <Paragraph className="personality-description">
                  {result.description}
                </Paragraph>
              </div>

              <Divider />

              <div className="traits-section">
                <Title level={4}>您的性格特征</Title>
                <div className="traits-grid">
                  {result.traits.map((trait: string, index: number) => (
                    <div key={index} className="trait-item">
                      <CheckCircleOutlined className="trait-icon" />
                      <span>{trait}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="suggestions-section">
                <Title level={4}>建议</Title>
                <ul className="suggestions-list">
                  {result.suggestions.map((suggestion: string, index: number) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>

              <div className="color-choices-section">
                <Title level={4}>您的色彩选择</Title>
                <div className="color-choices">
                  {result.colorChoices.map((choice: string, index: number) => {
                    const questionColors = colorQuestions[index].colors
                    const selectedColor = questionColors.find(c => c.value === choice)
                    return (
                      <div key={index} className="color-choice">
                        <div 
                          className="color-circle"
                          style={{ backgroundColor: selectedColor?.color }}
                        />
                        <Text>{selectedColor?.name}</Text>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="disclaimer">
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  * 此测试结果仅供娱乐参考，基于色彩心理学理论进行简单分析。
                </Text>
              </div>
            </div>

            <div className="result-actions">
              <Space size="large">
                <Button onClick={handleRestart} icon={<ExperimentOutlined />}>
                  重新测试
                </Button>
                <Button type="primary" onClick={handleBackToList} icon={<TrophyOutlined />}>
                  更多测试
                </Button>
              </Space>
            </div>
          </Card>
        </div>
      </div>
    )
  }

  const currentQ = colorQuestions[currentQuestion]
  const isAnswered = answers[currentQ.id] !== undefined

  return (
    <div className="color-psychology-container">
      <div className="test-container">
        {/* 头部 */}
        <Card className="test-header">
          <div className="header-content">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBackToList}
              className="back-button"
            >
              返回
            </Button>
            <div className="test-info">
              <Title level={4} style={{ margin: 0 }}>色彩心理游戏</Title>
              <Text type="secondary">
                第 {currentQuestion + 1} 题 / 共 {colorQuestions.length} 题
              </Text>
            </div>
          </div>
        </Card>

        {/* 题目卡片 */}
        <Card className="question-card">
          <div className="question-content">
            <Title level={3} className="question-title">
              {currentQ.question}
            </Title>
            
            <div className="colors-grid">
              {currentQ.colors.map(color => (
                <div
                  key={color.value}
                  className={`color-option ${answers[currentQ.id] === color.value ? 'selected' : ''}`}
                  onClick={() => handleColorSelect(currentQ.id, color.value)}
                >
                  <div 
                    className="color-circle"
                    style={{ backgroundColor: color.color }}
                  />
                  <Text className="color-name">{color.name}</Text>
                </div>
              ))}
            </div>
          </div>

          <div className="question-actions">
            <Button 
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              icon={<ArrowLeftOutlined />}
            >
              上一题
            </Button>
            <Button 
              type="primary"
              onClick={handleNext}
              disabled={!isAnswered}
              icon={<ArrowRightOutlined />}
            >
              {currentQuestion === colorQuestions.length - 1 ? '查看结果' : '下一题'}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default ColorPsychology

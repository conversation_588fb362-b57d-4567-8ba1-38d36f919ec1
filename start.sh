#!/bin/bash

echo "================================"
echo "Buwan Psychology Platform Setup"
echo "================================"

echo ""
echo "Database Config: localhost:3336, root/123456"
echo "Redis Config: localhost:6379, no password"
echo ""

# 检查环境
echo "[1/5] Checking environment..."

if ! command -v java &> /dev/null; then
    echo "ERROR: Java not found, please install Java 17+"
    exit 1
fi

if ! command -v mvn &> /dev/null; then
    echo "ERROR: Maven not found, please install Maven"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js not found, please install Node.js 18+"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "ERROR: npm not found"
    exit 1
fi

echo "Environment check passed"

echo ""
echo "[2/5] Checking MySQL service..."
if ! mysql -h localhost -P 3336 -u root -p123456 -e "SELECT VERSION();" &> /dev/null; then
    echo "ERROR: Cannot connect to MySQL!"
    echo "Please check:"
    echo "- MySQL service is running"
    echo "- Port 3336 is correct"
    echo "- Username/password: root/123456"
    exit 1
fi
echo "MySQL service OK"

echo ""
echo "[3/5] Checking/Initializing database..."
if ! mysql -h localhost -P 3336 -u root -p123456 -e "USE buwan_psychology; SELECT 'Database exists' as status;" &> /dev/null; then
    echo "Database not found, initializing..."
    if mysql -h localhost -P 3336 -u root -p123456 < docs/database.sql; then
        echo "Database initialized successfully!"
        echo "Default admin: <EMAIL> / admin123"
    else
        echo "ERROR: Database initialization failed!"
        exit 1
    fi
else
    echo "Database already exists"
fi

echo ""
echo "[4/5] Starting backend service..."
cd backend
echo "Setting Java 17 environment..."
export JAVA_HOME="/c/Program Files/Java/jdk-17"
export PATH="$JAVA_HOME/bin:$PATH"
echo "Installing dependencies and starting Spring Boot..."
nohup mvn spring-boot:run > ../backend.log 2>&1 &
BACKEND_PID=$!
echo "Backend PID: $BACKEND_PID"

echo ""
echo "Waiting for backend to start..."
sleep 20

echo ""
echo "[5/5] Starting frontend service..."
cd ../frontend

if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

echo "Starting React development server..."
nohup npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo "Frontend PID: $FRONTEND_PID"

echo ""
echo "================================"
echo "Setup Complete!"
echo "================================"
echo "Backend API: http://localhost:8080"
echo "Frontend App: http://localhost:3000"
echo "Admin Login: <EMAIL> / admin123"
echo "================================"
echo ""
echo "Process IDs:"
echo "Backend: $BACKEND_PID"
echo "Frontend: $FRONTEND_PID"
echo ""
echo "View logs:"
echo "  Backend: tail -f backend.log"
echo "  Frontend: tail -f frontend.log"
echo ""
echo "Stop services:"
echo "  kill $BACKEND_PID $FRONTEND_PID"
echo ""

# 保存PID到文件
echo $BACKEND_PID > backend.pid
echo $FRONTEND_PID > frontend.pid

echo "PIDs saved to backend.pid and frontend.pid files"

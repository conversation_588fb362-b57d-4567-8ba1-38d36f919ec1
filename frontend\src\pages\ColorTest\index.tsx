import React from 'react'
import { Card, Typography, Row, Col, Space } from 'antd'

const { Title, Text, Paragraph } = Typography

const ColorTest: React.FC = () => {
  return (
    <div style={{ padding: '40px', background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1} style={{ textAlign: 'center', marginBottom: '40px' }}>
          配色测试页面
        </Title>

        <Row gutter={[24, 24]}>
          {/* 浅色背景测试 */}
          <Col xs={24} md={12}>
            <Card title="浅色背景文字测试" style={{ background: 'rgba(255, 255, 255, 0.95)' }}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div>
                  <Text strong style={{ color: '#1e293b' }}>主标题文字 (#1e293b)</Text>
                </div>
                <div>
                  <Text style={{ color: '#475569' }}>副标题文字 (#475569)</Text>
                </div>
                <div>
                  <Text style={{ color: '#64748b' }}>正文文字 (#64748b)</Text>
                </div>
                <div>
                  <Text style={{ color: '#94a3b8' }}>辅助文字 (#94a3b8)</Text>
                </div>
                <Paragraph style={{ color: '#475569' }}>
                  这是一段测试文字，用来验证在浅色背景上的可读性。
                  文字应该清晰可见，对比度足够。
                </Paragraph>
              </Space>
            </Card>
          </Col>

          {/* 深色背景测试 */}
          <Col xs={24} md={12}>
            <Card 
              title={<span style={{ color: '#f8fafc' }}>深色背景文字测试</span>}
              style={{ 
                background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                color: '#f8fafc'
              }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div>
                  <Text strong style={{ color: '#f8fafc' }}>主标题文字 (#f8fafc)</Text>
                </div>
                <div>
                  <Text style={{ color: '#e2e8f0' }}>副标题文字 (#e2e8f0)</Text>
                </div>
                <div>
                  <Text style={{ color: '#cbd5e1' }}>正文文字 (#cbd5e1)</Text>
                </div>
                <div>
                  <Text style={{ color: '#94a3b8' }}>辅助文字 (#94a3b8)</Text>
                </div>
                <Paragraph style={{ color: '#cbd5e1' }}>
                  这是一段测试文字，用来验证在深色背景上的可读性。
                  文字应该清晰可见，对比度足够。
                </Paragraph>
              </Space>
            </Card>
          </Col>

          {/* 渐变背景测试 */}
          <Col xs={24}>
            <Card 
              title={<span style={{ color: '#f8fafc' }}>渐变背景文字测试</span>}
              style={{ 
                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
                color: '#f8fafc'
              }}
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={8}>
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Title level={3} style={{ color: '#f8fafc', marginBottom: '16px' }}>
                      主要内容
                    </Title>
                    <Text style={{ color: '#f1f5f9', fontSize: '16px' }}>
                      在渐变背景上的白色文字应该清晰可见
                    </Text>
                  </div>
                </Col>
                <Col xs={24} md={8}>
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Title level={3} style={{ color: '#f8fafc', marginBottom: '16px' }}>
                      次要内容
                    </Title>
                    <Text style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '16px' }}>
                      半透明白色文字也应该有足够的对比度
                    </Text>
                  </div>
                </Col>
                <Col xs={24} md={8}>
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Title level={3} style={{ color: '#f8fafc', marginBottom: '16px' }}>
                      辅助内容
                    </Title>
                    <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '16px' }}>
                      更淡的文字用于不太重要的信息
                    </Text>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 联系信息测试 */}
          <Col xs={24}>
            <Card 
              title={<span style={{ color: '#f8fafc' }}>Footer样式测试</span>}
              style={{ 
                background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                color: '#f8fafc'
              }}
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={8}>
                  <Title level={5} style={{ color: '#f8fafc', marginBottom: '16px' }}>
                    联系我们
                  </Title>
                  <Space direction="vertical" size="small">
                    <div style={{ color: '#cbd5e1' }}>📞 400-123-4567</div>
                    <div style={{ color: '#cbd5e1' }}>✉️ <EMAIL></div>
                    <div style={{ color: '#cbd5e1' }}>📍 北京市朝阳区xxx大厦</div>
                  </Space>
                </Col>
                <Col xs={24} md={8}>
                  <Title level={5} style={{ color: '#f8fafc', marginBottom: '16px' }}>
                    关注我们
                  </Title>
                  <Space>
                    <div style={{ 
                      color: '#cbd5e1', 
                      padding: '8px', 
                      borderRadius: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}>
                      微信
                    </div>
                    <div style={{ 
                      color: '#cbd5e1', 
                      padding: '8px', 
                      borderRadius: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}>
                      QQ
                    </div>
                  </Space>
                </Col>
                <Col xs={24} md={8}>
                  <Title level={5} style={{ color: '#f8fafc', marginBottom: '16px' }}>
                    版权信息
                  </Title>
                  <Text style={{ color: '#94a3b8', fontSize: '14px' }}>
                    © 2024 不晚心理. 保留所有权利.
                  </Text>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ColorTest

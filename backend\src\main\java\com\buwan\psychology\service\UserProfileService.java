package com.buwan.psychology.service;

import com.buwan.psychology.entity.User;
import com.buwan.psychology.entity.UserProfile;
import com.buwan.psychology.repository.UserProfileRepository;
import com.buwan.psychology.repository.UserRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户详细资料服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileService {

    private final UserProfileRepository userProfileRepository;
    private final UserRepository userRepository;
    private final ObjectMapper objectMapper;

    /**
     * 获取用户详细资料
     */
    public Optional<UserProfile> getUserProfile(Long userId) {
        log.info("获取用户详细资料，用户ID: {}", userId);
        return userProfileRepository.findByUserIdWithUser(userId);
    }

    /**
     * 获取合并了User基本信息的用户详细资料
     */
    public Map<String, Object> getMergedUserProfile(Long userId) {
        log.info("获取合并用户信息，用户ID: {}", userId);

        // 获取用户基本信息
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        User user = userOpt.get();
        Map<String, Object> mergedProfile = new HashMap<>();

        // 添加User中的基本信息
        mergedProfile.put("id", user.getId());
        mergedProfile.put("username", user.getUsername());
        mergedProfile.put("email", user.getEmail());
        mergedProfile.put("nickname", user.getNickname());
        mergedProfile.put("avatar", user.getAvatar());
        mergedProfile.put("gender", user.getGender());
        mergedProfile.put("age", user.getAge());
        mergedProfile.put("phone", user.getPhone());

        // 获取详细资料
        Optional<UserProfile> profileOpt = userProfileRepository.findByUserId(userId);
        if (profileOpt.isPresent()) {
            UserProfile profile = profileOpt.get();

            // 添加UserProfile中的详细信息
            mergedProfile.put("realName", profile.getRealName());
            mergedProfile.put("birthDate", profile.getBirthDate());
            mergedProfile.put("maritalStatus", profile.getMaritalStatus());
            mergedProfile.put("education", profile.getEducation());
            mergedProfile.put("occupation", profile.getOccupation());
            mergedProfile.put("income", profile.getIncome());

            // 地址信息
            Map<String, Object> location = new HashMap<>();
            location.put("province", profile.getProvince());
            location.put("city", profile.getCity());
            location.put("district", profile.getDistrict());
            mergedProfile.put("location", location);

            // 紧急联系人
            Map<String, Object> emergencyContact = new HashMap<>();
            emergencyContact.put("name", profile.getEmergencyContactName());
            emergencyContact.put("relationship", profile.getEmergencyContactRelationship());
            emergencyContact.put("phone", profile.getEmergencyContactPhone());
            mergedProfile.put("emergencyContact", emergencyContact);

            // 健康信息
            mergedProfile.put("medicalHistory", jsonToList(profile.getMedicalHistory()));
            mergedProfile.put("psychologicalHistory", jsonToList(profile.getPsychologicalHistory()));
            mergedProfile.put("currentMedications", profile.getCurrentMedications());
            mergedProfile.put("allergies", profile.getAllergies());

            // 生活方式
            Map<String, Object> lifestyle = new HashMap<>();
            lifestyle.put("smoking", profile.getSmoking());
            lifestyle.put("drinking", profile.getDrinking());
            lifestyle.put("exercise", profile.getExerciseFrequency());
            lifestyle.put("sleep", profile.getSleepDuration());
            mergedProfile.put("lifestyle", lifestyle);

            // 心理特征
            mergedProfile.put("personalityTraits", jsonToList(profile.getPersonalityTraits()));
            mergedProfile.put("interests", jsonToList(profile.getInterests()));
            mergedProfile.put("stressFactors", jsonToList(profile.getStressFactors()));
            mergedProfile.put("copingMethods", jsonToList(profile.getCopingMethods()));

            // 咨询目标
            mergedProfile.put("goals", profile.getGoals());
            mergedProfile.put("additionalInfo", profile.getAdditionalInfo());

            // 隐私设置
            Map<String, Object> privacySettings = new HashMap<>();
            privacySettings.put("showRealName", profile.getShowRealName());
            privacySettings.put("showAge", profile.getShowAge());
            privacySettings.put("showLocation", profile.getShowLocation());
            privacySettings.put("allowDataSharing", profile.getAllowDataSharing());
            mergedProfile.put("privacySettings", privacySettings);
        }

        return mergedProfile;
    }

    /**
     * 创建或更新用户详细资料
     */
    @Transactional
    public UserProfile saveOrUpdateUserProfile(Long userId, UserProfile profileData) {
        log.info("保存用户详细资料，用户ID: {}", userId);

        // 验证用户是否存在
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        // 查找现有资料
        Optional<UserProfile> existingProfileOpt = userProfileRepository.findByUserId(userId);
        
        UserProfile profile;
        if (existingProfileOpt.isPresent()) {
            // 更新现有资料
            profile = existingProfileOpt.get();
            updateProfileFields(profile, profileData);
        } else {
            // 创建新资料
            profile = profileData;
            profile.setUserId(userId);
        }

        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("用户详细资料保存成功，资料ID: {}", savedProfile.getId());
        return savedProfile;
    }

    /**
     * 更新资料字段
     */
    private void updateProfileFields(UserProfile existing, UserProfile newData) {
        if (newData.getRealName() != null) existing.setRealName(newData.getRealName());
        if (newData.getBirthDate() != null) existing.setBirthDate(newData.getBirthDate());
        if (newData.getMaritalStatus() != null) existing.setMaritalStatus(newData.getMaritalStatus());
        if (newData.getEducation() != null) existing.setEducation(newData.getEducation());
        if (newData.getOccupation() != null) existing.setOccupation(newData.getOccupation());
        if (newData.getIncome() != null) existing.setIncome(newData.getIncome());
        if (newData.getProvince() != null) existing.setProvince(newData.getProvince());
        if (newData.getCity() != null) existing.setCity(newData.getCity());
        if (newData.getDistrict() != null) existing.setDistrict(newData.getDistrict());
        if (newData.getEmergencyContactName() != null) existing.setEmergencyContactName(newData.getEmergencyContactName());
        if (newData.getEmergencyContactRelationship() != null) existing.setEmergencyContactRelationship(newData.getEmergencyContactRelationship());
        if (newData.getEmergencyContactPhone() != null) existing.setEmergencyContactPhone(newData.getEmergencyContactPhone());
        if (newData.getMedicalHistory() != null) existing.setMedicalHistory(newData.getMedicalHistory());
        if (newData.getPsychologicalHistory() != null) existing.setPsychologicalHistory(newData.getPsychologicalHistory());
        if (newData.getCurrentMedications() != null) existing.setCurrentMedications(newData.getCurrentMedications());
        if (newData.getAllergies() != null) existing.setAllergies(newData.getAllergies());
        if (newData.getSmoking() != null) existing.setSmoking(newData.getSmoking());
        if (newData.getDrinking() != null) existing.setDrinking(newData.getDrinking());
        if (newData.getExerciseFrequency() != null) existing.setExerciseFrequency(newData.getExerciseFrequency());
        if (newData.getSleepDuration() != null) existing.setSleepDuration(newData.getSleepDuration());
        if (newData.getPersonalityTraits() != null) existing.setPersonalityTraits(newData.getPersonalityTraits());
        if (newData.getInterests() != null) existing.setInterests(newData.getInterests());
        if (newData.getStressFactors() != null) existing.setStressFactors(newData.getStressFactors());
        if (newData.getCopingMethods() != null) existing.setCopingMethods(newData.getCopingMethods());
        if (newData.getGoals() != null) existing.setGoals(newData.getGoals());
        if (newData.getAdditionalInfo() != null) existing.setAdditionalInfo(newData.getAdditionalInfo());
        if (newData.getShowRealName() != null) existing.setShowRealName(newData.getShowRealName());
        if (newData.getShowAge() != null) existing.setShowAge(newData.getShowAge());
        if (newData.getShowLocation() != null) existing.setShowLocation(newData.getShowLocation());
        if (newData.getAllowDataSharing() != null) existing.setAllowDataSharing(newData.getAllowDataSharing());
    }

    /**
     * 删除用户详细资料
     */
    @Transactional
    public void deleteUserProfile(Long userId) {
        log.info("删除用户详细资料，用户ID: {}", userId);
        userProfileRepository.deleteByUserId(userId);
    }

    /**
     * 检查用户是否已有详细资料
     */
    public boolean hasUserProfile(Long userId) {
        return userProfileRepository.existsByUserId(userId);
    }

    /**
     * 将字符串列表转换为JSON字符串
     */
    public String listToJson(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            log.error("转换列表到JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为字符串列表
     */
    public List<String> jsonToList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return List.of();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            log.error("转换JSON到列表失败", e);
            return List.of();
        }
    }

    /**
     * 获取用户资料的统计信息
     */
    public Map<String, Object> getUserProfileStats(Long userId) {
        Optional<UserProfile> profileOpt = getUserProfile(userId);
        if (profileOpt.isEmpty()) {
            return Map.of("completeness", 0, "hasProfile", false);
        }

        UserProfile profile = profileOpt.get();
        int totalFields = 20; // 主要字段数量
        int completedFields = 0;

        // 计算完成度
        if (profile.getRealName() != null && !profile.getRealName().trim().isEmpty()) completedFields++;
        if (profile.getBirthDate() != null) completedFields++;
        if (profile.getMaritalStatus() != null) completedFields++;
        if (profile.getEducation() != null) completedFields++;
        if (profile.getOccupation() != null) completedFields++;
        if (profile.getIncome() != null) completedFields++;
        if (profile.getProvince() != null) completedFields++;
        if (profile.getEmergencyContactName() != null) completedFields++;
        if (profile.getEmergencyContactPhone() != null) completedFields++;
        if (profile.getMedicalHistory() != null) completedFields++;
        if (profile.getPsychologicalHistory() != null) completedFields++;
        if (profile.getCurrentMedications() != null) completedFields++;
        if (profile.getAllergies() != null) completedFields++;
        if (profile.getExerciseFrequency() != null) completedFields++;
        if (profile.getSleepDuration() != null) completedFields++;
        if (profile.getPersonalityTraits() != null) completedFields++;
        if (profile.getInterests() != null) completedFields++;
        if (profile.getStressFactors() != null) completedFields++;
        if (profile.getCopingMethods() != null) completedFields++;
        if (profile.getGoals() != null) completedFields++;

        int completeness = (int) Math.round((double) completedFields / totalFields * 100);

        return Map.of(
            "completeness", completeness,
            "hasProfile", true,
            "completedFields", completedFields,
            "totalFields", totalFields
        );
    }
}

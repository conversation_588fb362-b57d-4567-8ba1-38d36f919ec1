/* 个人中心页面样式 */
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* 用户信息卡片 */
.user-info-card {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
}

.user-avatar-section {
  text-align: center;
  position: relative;
}

.user-avatar {
  border: 4px solid #f0f0f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-badge {
  margin-top: 8px;
}

.user-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.profile-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 统计卡片 */
.stats-card {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
}

.stats-card .ant-statistic-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.stats-card .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 快捷操作卡片 */
.quick-actions-card {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
}

.action-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

.action-icon {
  transition: all 0.3s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1);
}

/* 最近活动卡片 */
.recent-card {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
}

.recent-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.recent-card .ant-card-body {
  padding: 16px 24px;
}

.recent-item {
  padding: 12px 0 !important;
  border-bottom: 1px solid #f5f5f5;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-item-content {
  width: 100%;
}

.recent-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 卡片通用样式 */
.dashboard-container .ant-card {
  transition: all 0.3s ease;
}

.dashboard-container .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.dashboard-container .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

/* 进度条样式 */
.ant-progress-line {
  flex: 1;
}

.ant-progress-text {
  color: #666;
  font-size: 12px;
}

/* 标签样式 */
.ant-tag {
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
  border: none;
}

/* 统计数字样式 */
.ant-statistic-content-value {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .dashboard-content {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .user-info-card {
    margin-bottom: 16px;
  }
  
  .stats-card {
    margin-bottom: 16px;
  }
  
  .stats-card .ant-statistic-content {
    font-size: 20px;
  }
  
  .action-card {
    margin-bottom: 12px;
  }
  
  .action-icon {
    font-size: 28px !important;
  }
  
  .recent-card {
    margin-top: 12px !important;
  }
  
  .user-avatar {
    width: 60px !important;
    height: 60px !important;
  }
  
  .profile-progress {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .dashboard-container {
    padding: 8px;
  }
  
  .user-info-card .ant-card-body {
    padding: 16px;
  }
  
  .stats-card .ant-card-body {
    padding: 16px;
  }
  
  .quick-actions-card .ant-card-body {
    padding: 16px;
  }
  
  .recent-card .ant-card-body {
    padding: 12px 16px;
  }
  
  .recent-card .ant-card-head {
    padding: 12px 16px;
  }
  
  .action-card .ant-card-body {
    padding: 16px !important;
  }
  
  .action-icon {
    font-size: 24px !important;
    margin-bottom: 8px !important;
  }
  
  .user-info .ant-typography-title {
    font-size: 20px !important;
  }
  
  .stats-card .ant-statistic-content {
    font-size: 18px;
  }
}

/* 动画效果 */
.dashboard-container .ant-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 徽章样式 */
.ant-badge-status-dot {
  width: 8px;
  height: 8px;
}

.ant-badge-status-success {
  background-color: #52c41a;
}

.ant-badge-status-text {
  color: #52c41a;
  font-size: 12px;
  margin-left: 8px;
}

/* 列表项悬停效果 */
.recent-item {
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 0 -12px;
  padding: 12px !important;
}

.recent-item:hover {
  background-color: #f8f9fa;
  transform: translateX(4px);
}

/* 按钮样式优化 */
.dashboard-container .ant-btn-link {
  color: #1890ff;
  font-size: 12px;
  padding: 0;
  height: auto;
}

.dashboard-container .ant-btn-link:hover {
  color: #40a9ff;
}

/* 卡片标题图标 */
.dashboard-container .ant-card-head-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

/* 空状态样式 */
.ant-empty {
  padding: 20px 0;
}

.ant-empty-description {
  color: #999;
  font-size: 12px;
}

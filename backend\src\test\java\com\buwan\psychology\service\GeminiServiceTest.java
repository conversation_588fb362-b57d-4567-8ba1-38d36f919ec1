package com.buwan.psychology.service;

import com.buwan.psychology.entity.ChatMessage;
import com.buwan.psychology.entity.Counselor;
import com.buwan.psychology.entity.User;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GeminiService测试类
 * 主要测试会话标题和描述是否正确传递到prompt中
 */
@ExtendWith(MockitoExtension.class)
class GeminiServiceTest {

    @Mock
    private WebClient geminiWebClient;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private GeminiService geminiService;

    @Test
    void testBuildDynamicPromptWithSessionInfo() throws Exception {
        // 准备测试数据
        Counselor counselor = new Counselor();
        counselor.setName("张医生");
        counselor.setTitle("心理咨询师");

        User user = new User();
        user.setUsername("小明");
        user.setAge(25);
        user.setGender(1);

        Integer sessionType = 1; // 焦虑问题
        String sessionTitle = "工作压力导致的焦虑";
        String sessionDescription = "我是一名25岁的软件工程师，最近因为项目压力很大，经常失眠，感到焦虑不安。希望能得到专业的指导和建议。";

        // 使用反射调用私有方法
        Method buildDynamicPromptMethod = GeminiService.class.getDeclaredMethod(
                "buildDynamicPrompt", 
                Counselor.class, User.class, Integer.class, String.class, String.class
        );
        buildDynamicPromptMethod.setAccessible(true);

        // 调用方法
        String prompt = (String) buildDynamicPromptMethod.invoke(
                geminiService, counselor, user, sessionType, sessionTitle, sessionDescription
        );

        // 验证prompt包含会话信息
        assertNotNull(prompt);
        assertTrue(prompt.contains("张医生"), "Prompt应该包含咨询师姓名");
        assertTrue(prompt.contains("小明"), "Prompt应该包含用户姓名");
        assertTrue(prompt.contains("焦虑问题咨询"), "Prompt应该包含会话类型");
        assertTrue(prompt.contains("工作压力导致的焦虑"), "Prompt应该包含会话标题");
        assertTrue(prompt.contains("软件工程师"), "Prompt应该包含会话描述中的关键信息");
        assertTrue(prompt.contains("本次咨询会话信息"), "Prompt应该包含会话信息标题");
        assertTrue(prompt.contains("咨询主题"), "Prompt应该包含咨询主题标签");
        assertTrue(prompt.contains("详细情况"), "Prompt应该包含详细情况标签");

        System.out.println("生成的Prompt:");
        System.out.println(prompt);
    }

    @Test
    void testBuildDynamicPromptWithoutSessionInfo() throws Exception {
        // 准备测试数据（不包含会话信息）
        Counselor counselor = new Counselor();
        counselor.setName("李医生");

        User user = new User();
        user.setUsername("小红");

        Integer sessionType = 0; // 一般咨询

        // 使用反射调用私有方法
        Method buildDynamicPromptMethod = GeminiService.class.getDeclaredMethod(
                "buildDynamicPrompt", 
                Counselor.class, User.class, Integer.class, String.class, String.class
        );
        buildDynamicPromptMethod.setAccessible(true);

        // 调用方法（会话信息为null）
        String prompt = (String) buildDynamicPromptMethod.invoke(
                geminiService, counselor, user, sessionType, null, null
        );

        // 验证prompt不包含会话信息部分
        assertNotNull(prompt);
        assertTrue(prompt.contains("李医生"), "Prompt应该包含咨询师姓名");
        assertTrue(prompt.contains("小红"), "Prompt应该包含用户姓名");
        assertFalse(prompt.contains("本次咨询会话信息"), "Prompt不应该包含会话信息标题");
        assertFalse(prompt.contains("咨询主题"), "Prompt不应该包含咨询主题标签");
        assertFalse(prompt.contains("详细情况"), "Prompt不应该包含详细情况标签");

        System.out.println("不包含会话信息的Prompt:");
        System.out.println(prompt);
    }
}

/* App 组件样式 */
.app-layout {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-content {
  min-height: calc(100vh - 64px);
  background-color: #f5f5f5;
}

/* Header 样式 */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.logo {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 40px;
  transition: all 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 28px;
  color: #6366f1;
  margin-right: 8px;
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3));
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.header-menu {
  border-bottom: none;
  background: transparent;
  flex: 1;
}

.header-menu .ant-menu-item {
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.header-menu .ant-menu-item:hover,
.header-menu .ant-menu-item-selected {
  color: #6366f1;
  border-bottom-color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(99, 102, 241, 0.08);
  border-radius: 12px;
}

.user-avatar {
  margin-right: 8px;
}

.user-name {
  color: #333;
  font-weight: 500;
}

.header .auth-button {
  margin-left: 8px;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: none !important;
  background: transparent !important;
  border: 1px solid #d9d9d9 !important;
  color: #333 !important;
  transition: all 0.3s ease !important;
}

.header .auth-button:hover {
  border-color: #6366f1 !important;
  color: #6366f1 !important;
  background: rgba(99, 102, 241, 0.05) !important;
  transform: none !important;
  box-shadow: none !important;
}

.header .auth-button.ant-btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border: none !important;
  color: white !important;
}

.header .auth-button.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5b5ff5 0%, #8049f0 100%) !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
}

/* Footer 样式 */
.footer {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: #f8fafc;
  padding: 40px 0 20px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-section {
  margin-bottom: 24px;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.footer-logo-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 8px;
}

.footer-logo-text {
  color: #f8fafc;
  margin: 0;
  font-weight: 600;
}

.footer-description {
  color: #cbd5e1;
  line-height: 1.6;
  font-size: 14px;
}

.footer-title {
  color: #f8fafc;
  margin-bottom: 16px;
  font-weight: 600;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-links a {
  color: #cbd5e1;
  transition: color 0.3s ease;
  text-decoration: none;
}

.footer-links a:hover {
  color: #6366f1;
}

.contact-info {
  width: 100%;
}

.contact-item {
  display: flex;
  align-items: center;
  color: #cbd5e1;
  margin-bottom: 8px;
}

.contact-icon {
  margin-right: 12px;
  color: #6366f1;
  font-size: 16px;
}

.social-links {
  margin-top: 20px;
}

.social-icon {
  font-size: 24px;
  color: #cbd5e1;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
}

.social-icon:hover {
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

.footer-divider {
  border-color: rgba(203, 213, 225, 0.2);
  margin: 32px 0 20px;
}

.footer-bottom {
  margin-bottom: 16px;
}

.copyright {
  color: #94a3b8;
  font-size: 14px;
}

.footer-disclaimer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid rgba(203, 213, 225, 0.2);
}

.disclaimer-text {
  color: #94a3b8;
  font-size: 12px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .logo {
    margin-right: 20px;
  }
  
  .header-menu {
    display: none;
  }
  
  .footer-content {
    padding: 0 16px;
  }
  
  .footer-bottom {
    text-align: center;
  }
  
  .footer-bottom .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .header-content {
    padding: 0 12px;
  }
  
  .logo-text {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}

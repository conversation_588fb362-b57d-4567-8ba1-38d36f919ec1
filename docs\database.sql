-- 不晚心理平台数据库初始化脚本
-- 数据库配置: localhost:3336, 用户: root, 密码: 123456

-- 创建数据库
CREATE DATABASE IF NOT EXISTS buwan_psychology
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 创建开发环境数据库
CREATE DATABASE IF NOT EXISTS buwan_psychology_dev
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE buwan_psychology;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '手机号',
    user_type TINYINT DEFAULT 0 COMMENT '用户类型：0-普通用户，1-青少年用户，2-管理员',
    status TINYINT DEFAULT 0 COMMENT '账户状态：0-正常，1-禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    bio VARCHAR(500) COMMENT '用户简介',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 心理咨询会话表
CREATE TABLE IF NOT EXISTS consultation_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '会话标题',
    session_type TINYINT DEFAULT 0 COMMENT '会话类型：0-一般咨询，1-焦虑问题，2-抑郁问题，3-学习压力，4-人际关系，5-其他',
    status TINYINT DEFAULT 0 COMMENT '会话状态：0-进行中，1-已结束，2-已暂停',
    summary VARCHAR(1000) COMMENT '会话摘要',
    ai_analysis VARCHAR(2000) COMMENT 'AI分析结果',
    recommendations VARCHAR(2000) COMMENT '建议和方案',
    urgency_level TINYINT DEFAULT 0 COMMENT '紧急程度：0-正常，1-需要关注，2-紧急',
    start_time DATETIME COMMENT '会话开始时间',
    end_time DATETIME COMMENT '会话结束时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_session_type (session_type),
    INDEX idx_status (status),
    INDEX idx_urgency_level (urgency_level),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='心理咨询会话表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id BIGINT NOT NULL COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    content TEXT NOT NULL COMMENT '消息内容',
    message_type TINYINT DEFAULT 0 COMMENT '消息类型：0-用户消息，1-AI回复，2-系统消息',
    emotion_analysis VARCHAR(500) COMMENT '情感分析结果',
    keywords VARCHAR(1000) COMMENT '关键词提取',
    confidence INT COMMENT 'AI置信度（0-100）',
    has_risk_signal BOOLEAN DEFAULT FALSE COMMENT '是否包含风险信号',
    risk_level TINYINT DEFAULT 0 COMMENT '风险级别：0-无风险，1-低风险，2-中风险，3-高风险',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_message_type (message_type),
    INDEX idx_risk_level (risk_level),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (session_id) REFERENCES consultation_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 心理评估表
CREATE TABLE IF NOT EXISTS psychological_assessments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    assessment_type VARCHAR(50) NOT NULL COMMENT '评估类型',
    title VARCHAR(200) NOT NULL COMMENT '评估标题',
    description TEXT COMMENT '评估描述',
    questions JSON COMMENT '评估问题（JSON格式）',
    status TINYINT DEFAULT 0 COMMENT '状态：0-进行中，1-已完成',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_assessment_type (assessment_type),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='心理评估表';

-- 评估结果表
CREATE TABLE IF NOT EXISTS assessment_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    assessment_id BIGINT NOT NULL COMMENT '评估ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    answers JSON COMMENT '用户答案（JSON格式）',
    score INT COMMENT '评估得分',
    result_level VARCHAR(50) COMMENT '结果级别',
    analysis TEXT COMMENT '结果分析',
    recommendations TEXT COMMENT '建议',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_assessment_id (assessment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_result_level (result_level),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (assessment_id) REFERENCES psychological_assessments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估结果表';

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入初始管理员用户
INSERT INTO users (username, email, password, nickname, user_type, status) 
VALUES ('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '系统管理员', 2, 0)
ON DUPLICATE KEY UPDATE username = username;

-- 插入示例心理评估
INSERT INTO psychological_assessments (user_id, assessment_type, title, description, questions, status) 
VALUES (
    1, 
    'anxiety', 
    '焦虑自评量表(SAS)', 
    '用于评估个体焦虑水平的标准化量表',
    JSON_ARRAY(
        JSON_OBJECT('id', 1, 'question', '我感到比平常更加紧张和焦虑', 'options', JSON_ARRAY('从不', '有时', '经常', '总是')),
        JSON_OBJECT('id', 2, 'question', '我无缘无故地感到害怕', 'options', JSON_ARRAY('从不', '有时', '经常', '总是')),
        JSON_OBJECT('id', 3, 'question', '我容易心烦意乱或感到恐慌', 'options', JSON_ARRAY('从不', '有时', '经常', '总是')),
        JSON_OBJECT('id', 4, 'question', '我感到我可能将要发疯', 'options', JSON_ARRAY('从不', '有时', '经常', '总是')),
        JSON_OBJECT('id', 5, 'question', '我觉得一切都很好，也不会发生什么不幸', 'options', JSON_ARRAY('从不', '有时', '经常', '总是'))
    ),
    1
)
ON DUPLICATE KEY UPDATE title = title;

-- 创建视图：用户统计
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN user_type = 1 THEN 1 END) as teenager_users,
    COUNT(CASE WHEN status = 0 THEN 1 END) as active_users,
    COUNT(CASE WHEN last_login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_active_users
FROM users;

-- 创建视图：会话统计
CREATE OR REPLACE VIEW session_statistics AS
SELECT 
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN status = 0 THEN 1 END) as active_sessions,
    COUNT(CASE WHEN status = 1 THEN 1 END) as completed_sessions,
    COUNT(CASE WHEN urgency_level >= 2 THEN 1 END) as urgent_sessions,
    AVG(TIMESTAMPDIFF(MINUTE, start_time, end_time)) as avg_session_duration
FROM consultation_sessions;

-- 创建索引优化查询性能
CREATE INDEX idx_messages_session_time ON chat_messages(session_id, create_time);
CREATE INDEX idx_sessions_user_time ON consultation_sessions(user_id, create_time);
CREATE INDEX idx_users_type_status ON users(user_type, status);

COMMIT;

import React, { useState, useEffect } from 'react'
import { Modal, Form, Input, Select, Typography, Avatar, Rate, Tag, Radio, Space } from 'antd'
import { UserOutlined, RobotOutlined } from '@ant-design/icons'
import { counselorApi } from '../../../services/api'

const { Text } = Typography
const { Option } = Select

interface Counselor {
  id: number
  name: string
  title: string
  specializations: string
  rating: number
  experience: number
  avatar: string
  isAvailable: boolean
}

interface NewSessionModalProps {
  visible: boolean
  onCancel: () => void
  onOk: (values: { title: string; description?: string; sessionType: number; counselorId?: number; autoMatch?: boolean }) => void
  loading: boolean
}

const NewSessionModal: React.FC<NewSessionModalProps> = ({
  visible,
  onCancel,
  onOk,
  loading
}) => {
  const [form] = Form.useForm()
  const [counselors, setCounselors] = useState<Counselor[]>([])
  const [counselorLoading, setCounselorLoading] = useState(false)
  const [counselorSelection, setCounselorSelection] = useState<'auto' | 'manual'>('auto')
  const [selectedCounselor, setSelectedCounselor] = useState<number | undefined>()

  useEffect(() => {
    if (visible) {
      fetchCounselors()
    }
  }, [visible])

  const fetchCounselors = async () => {
    try {
      setCounselorLoading(true)
      const response = await counselorApi.getAllCounselors()
      if (response.success) {
        setCounselors(response.data)
      }
    } catch (error) {
      console.error('获取咨询师列表失败:', error)
    } finally {
      setCounselorLoading(false)
    }
  }

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      const submitData = {
        ...values,
        counselorId: counselorSelection === 'manual' ? selectedCounselor : undefined,
        autoMatch: counselorSelection === 'auto'
      }
      onOk(submitData)
      form.resetFields()
      setCounselorSelection('auto')
      setSelectedCounselor(undefined)
    } catch (error) {
      // 表单验证失败
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setCounselorSelection('auto')
    setSelectedCounselor(undefined)
    onCancel()
  }

  const sessionTypes = [
    { value: 0, label: '一般咨询', description: '日常心理问题咨询' },
    { value: 1, label: '焦虑问题', description: '焦虑、紧张、恐慌等问题' },
    { value: 2, label: '抑郁问题', description: '情绪低落、抑郁等问题' },
    { value: 3, label: '学习压力', description: '学习、工作压力相关问题' },
    { value: 4, label: '人际关系', description: '人际交往、关系处理问题' },
    { value: 5, label: '其他', description: '其他心理健康相关问题' }
  ]

  return (
    <Modal
      title="创建新的咨询会话"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="创建"
      cancelText="取消"
      width={600}
      className="new-session-modal"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          sessionType: 0
        }}
      >
        <Form.Item
          name="title"
          label="会话标题"
          rules={[
            { required: true, message: '请输入会话标题' },
            { max: 50, message: '标题不能超过50个字符' }
          ]}
        >
          <Input
            placeholder="请简要描述您想咨询的问题"
            maxLength={50}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="详细描述"
          rules={[
            { max: 2000, message: '描述不能超过2000个字符' }
          ]}
        >
          <Input.TextArea
            placeholder="请详细描述您的情况和问题：&#10;• 您的基本信息（年龄、性别、学历等）&#10;• 遇到的具体问题或困扰&#10;• 问题持续的时间和严重程度&#10;• 希望得到什么样的帮助&#10;• 其他您认为重要的信息"
            maxLength={2000}
            showCount
            autoSize={{ minRows: 4, maxRows: 8 }}
          />
        </Form.Item>

        <Form.Item
          name="sessionType"
          label="咨询类型"
          rules={[{ required: true, message: '请选择咨询类型' }]}
        >
          <Select
            placeholder="请选择最符合您问题的类型"
            optionLabelProp="label"
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            className="session-type-select"
          >
            {sessionTypes.map(type => (
              <Option
                key={type.value}
                value={type.value}
                label={type.label}
              >
                <div className="session-type-option">
                  <div className="session-type-title">
                    {type.label}
                  </div>
                  <div className="session-type-desc">
                    {type.description}
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="选择咨询师"
          required
        >
          <Radio.Group
            value={counselorSelection}
            onChange={(e) => setCounselorSelection(e.target.value)}
            style={{ width: '100%' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Radio value="auto">
                <Space>
                  <RobotOutlined style={{ color: '#6366f1' }} />
                  <span>智能匹配（推荐）</span>
                </Space>
                <div style={{ marginLeft: 24, marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    系统将根据您的咨询类型自动匹配最合适的咨询师
                  </Text>
                </div>
              </Radio>

              <Radio value="manual">
                <Space>
                  <UserOutlined style={{ color: '#6366f1' }} />
                  <span>手动选择</span>
                </Space>
                <div style={{ marginLeft: 24, marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    从专家团队中选择您偏好的咨询师
                  </Text>
                </div>
              </Radio>
            </Space>
          </Radio.Group>

          {counselorSelection === 'manual' && (
            <div style={{ marginTop: 16 }}>
              <Select
                placeholder="请选择咨询师"
                style={{ width: '100%' }}
                value={selectedCounselor}
                onChange={setSelectedCounselor}
                loading={counselorLoading}
                optionLabelProp="label"
              >
                {counselors.filter(c => c.isAvailable).map(counselor => (
                  <Option
                    key={counselor.id}
                    value={counselor.id}
                    label={counselor.name}
                  >
                    <div style={{ padding: '8px 0' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                        <Avatar
                          size={40}
                          src={counselor.avatar}
                          icon={<UserOutlined />}
                        />
                        <div style={{ flex: 1 }}>
                          <div style={{ fontWeight: 500, marginBottom: 2 }}>
                            {counselor.name}
                          </div>
                          <div style={{ fontSize: '12px', color: '#6366f1', marginBottom: 4 }}>
                            {counselor.title}
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                            <Rate
                              disabled
                              defaultValue={counselor.rating}
                              allowHalf
                              style={{ fontSize: '12px' }}
                            />
                            <span style={{ fontSize: '11px', color: '#64748b' }}>
                              {counselor.experience}年经验
                            </span>
                          </div>
                          <div style={{ marginTop: 4 }}>
                            {counselor.specializations &&
                              JSON.parse(counselor.specializations).slice(0, 2).map((spec: string) => (
                                <Tag key={spec} size="small" color="blue">
                                  {spec}
                                </Tag>
                              ))
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
          )}
        </Form.Item>

        <div className="session-tip">
          <div className="session-tip-content">
            💡 <strong>温馨提示：</strong>
            <br />
            • <strong>标题</strong>：简明扼要地概括您的咨询主题
            <br />
            • <strong>详细描述</strong>：请详细描述您的情况，包括个人信息、具体问题、持续时间等，这有助于咨询师更好地理解和帮助您
            <br />
            • 所有信息都会严格保密，请放心填写
            <br />
            • 如遇紧急情况，请立即联系专业医疗机构
          </div>
        </div>
      </Form>
    </Modal>
  )
}

export default NewSessionModal
